const nodemailer = require("nodemailer");
const { google } = require("googleapis");
const fs = require("fs");
const sharp = require("sharp");
const WebinarUser = require("../Models/webinarUser");
const hbs = require("handlebars");
const path = require("path");

// Configure Handlebars globally to allow prototype access safely
hbs.create({
    allowProtoPropertiesByDefault: true,
    allowProtoMethodsByDefault: true,
});

// OAuth2 configuration
const CLIENT_ID = process.env.CLIENT_ID;
const CLIENT_SECRET = process.env.CLIENT_SECRET;
const REDIRECT_URI = process.env.REDIRECT_URI;
const REFRESH_TOKEN = process.env.REFRESH_TOKEN;

const oAuth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);
oAuth2Client.setCredentials({ refresh_token: REFRESH_TOKEN });

// Function to get a new access token
async function getAccessToken() {
    try {
        const accessToken = await oAuth2Client.getAccessToken();
        return accessToken.token;
    } catch (error) {
        console.error("Error fetching access token:", error.message);
        throw error;
    }
}

// Function to send an email
async function sendEmail({ to, subject, html, attachment }) {
    const accessToken = await getAccessToken();
    const transporter = nodemailer.createTransport({
        service: "gmail",
        auth: {
            type: "OAuth2",
            user: "<EMAIL>",
            clientId: CLIENT_ID,
            clientSecret: CLIENT_SECRET,
            refreshToken: REFRESH_TOKEN,
            accessToken: accessToken,
        },
    });

    const mailOptions = {
        from: "<EMAIL>",
        to,
        subject,
        html,
    };

    if (attachment) {
        mailOptions.attachments = [
            {
                filename: attachment.filename,
                content: attachment.content,
            },
        ];
    }
    return transporter.sendMail(mailOptions);
}

// Function to send bulk emails
function sendBulkCertificates(emails, webinarCode, certificateImage, certificate) {
    let promise = Promise.resolve(); // Start with a resolved promise
    let maxRetries = 3;
    let retryDelay = 5000;

    emails.forEach(async (email) => {
        const certificateContent = certificate.replace(/{name}/g, email.name).replace(/{image}/g, certificateImage); // Replace with the actual URL
        // Chain promises one after another
        const content = {
            to: email.to,
            subject: email.subject,
            html: email.html,
            attachment: {
                filename: "certificate.png",
                content: await sharp(Buffer.from(certificateContent)).png().toBuffer(),
            },
        };
        promise = promise
            .then(() => sendEmailWithRetry(content, maxRetries, retryDelay)) // Send the current email
            .then((result) => {
                const eml = result.accepted[0];
                WebinarUser.findOneAndUpdate({ email: eml, webinar_code: webinarCode }, { isCertificateSent: true })
                    .then((result) => {
                        console.log("Sent email & Updated user - " + eml); // Log success
                    })
                    .catch((error) => {
                        console.log("Error in updating webinar user", error);
                    });
            })
            .catch((error) => {
                console.error(error.message); // Log failure
            });
    });

    return promise; // Return the final promise
}

function sendBulkEmails(emails, maxRetries = 3, retryDelay = 5000) {
    let promise = Promise.resolve(); // Start with a resolved promise

    emails.forEach((email) => {
        // Chain promises one after another
        promise = promise
            .then(() => sendEmailWithRetry(email, maxRetries, retryDelay)) // Send the current email with retry mechanism
            .then((result) => {
                console.log("Sent email to " + result.accepted[0]); // Log success
            })
            .catch((error) => {
                console.error("Failed to send email to " + email.to + ": " + error); // Log failure
            });
    });

    return promise; // Return the final promise
}

async function sendEmailWithRetry(email, maxRetries, retryDelay) {
    let attempts = 0;
    while (attempts < maxRetries) {
        try {
            const result = await sendEmail(email);
            return result; // Email sent successfully
        } catch (error) {
            attempts++;
            console.error(`Attempt ${attempts} failed for ${email.to}: ${error.message}`);
            if (attempts < maxRetries) {
                await new Promise((resolve) => setTimeout(resolve, retryDelay)); // Wait before retrying
            } else {
                throw error; // Max retries reached, throw the error
            }
        }
    }
}

const sendConfirmationEmail = async (user) => {
    const fs = require("fs").promises;
    const templatePath = path.join(__dirname, "../Assets/src-email-confirmation.hbs");
    const templateSource = await fs.readFile(templatePath, "utf8");

    // Configure Handlebars to allow prototype access
    const template = hbs.compile(templateSource, {
        allowProtoPropertiesByDefault: true,
        allowProtoMethodsByDefault: true,
    });

    // Convert Mongoose document to plain object to avoid prototype issues
    const userData = user.toObject ? user.toObject() : user;

    const html = template(userData);
    return sendEmail({
        to: user.email,
        subject: "Registration Confirmation - Sheryians Coding School",
        html,
    });
};

/**
 * Test email sending functionality
 * @param {string} recipientEmail - Email address to send the test email to
 * @returns {Promise} - Promise that resolves with the email sending result
 */
const testEmailSending = async (recipientEmail) => {
    try {
        // Create a mock user object with required fields
        const mockUser = {
            fullname: "Test User",
            email: recipientEmail,
            uniqueId: "TEST123",
            expertise: ["Web Development"],
            applied_for_role: "Web Developer",
            city: "Test City",
            phone: "9876543210",
        };

        console.log(`Attempting to send test email to ${recipientEmail}...`);

        // Option 1: Test with plain HTML
        const plainEmailResult = await sendEmail({
            to: recipientEmail,
            subject: "Test Email from Sheryians - Plain HTML",
            html: `
                <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; border-radius: 5px;">
                    <h2 style="color: #3182ce;">Email Testing Successful!</h2>
                    <p>This is a test email to verify that the email sending functionality is working correctly.</p>
                    <p>If you're seeing this, it means our email system is operational.</p>
                    <p>Time sent: ${new Date().toLocaleString()}</p>
                </div>
            `,
        });

        console.log("Plain HTML email sent successfully:", plainEmailResult.messageId);

        // Option 2: Test with template
        const templateEmailResult = await sendConfirmationEmail(mockUser);

        console.log("Template email sent successfully:", templateEmailResult.messageId);

        return {
            success: true,
            plainEmailResult,
            templateEmailResult,
            message: "Both test emails sent successfully",
        };
    } catch (error) {
        console.error("Error sending test email:", error);
        return {
            success: false,
            error: error.message,
            stack: error.stack,
        };
    }
};

module.exports = { sendEmail, sendBulkEmails, sendBulkCertificates, sendConfirmationEmail, testEmailSending };
