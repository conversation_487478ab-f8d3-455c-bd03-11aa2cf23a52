const axios = require('axios');
const user = require('../Models/user');

const MERITTO_API_URL = 'https://services.in7.nopaperforms.com/webhooks/v1/leadCapture/6342';
const SECRET_KEY = 'd9383ca4ab6645469d211a1a86b1a7a6';
const COLLEGE_ID = '6342';

const merittoCourseMap = {
    'rb01': 'Backend Domination',
    'mco-01': 'Job Ready AI Powered Cohort',
    'rf01': 'Frontend Domination',
    'ap01': 'Aptitude and Reasoning',
    'dsa01': 'Java and DSA domination',
    'tj01': 'ThreeJS',
    'ddc01': 'DSA Domination Cohort'
};

async function request (data) {
  try {
    const response = await axios.post(MERITTO_API_URL, data, {
      headers: {
        'Content-Type': 'application/json'
        }
    });
        return response.data;
    }
    catch (error) {
        console.error(error);
        return error;
    }
}

function getCurrentFormattedDate() {
    // Create date object for IST using UTC+5:30
    const date = new Date();
    const istTime = new Date(date.getTime() + (330 * 60000)); // Add 5:30 hours (330 minutes)
    
    // Get date components
    const day = String(istTime.getUTCDate()).padStart(2, '0');
    const month = String(istTime.getUTCMonth() + 1).padStart(2, '0');
    const year = istTime.getUTCFullYear();
    
    // Get time components
    let hours = istTime.getUTCHours();
    const minutes = String(istTime.getUTCMinutes()).padStart(2, '0');
    const seconds = String(istTime.getUTCSeconds()).padStart(2, '0');
    
    // Convert to 12-hour format with AM/PM
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // convert 0 to 12
    hours = String(hours).padStart(2, '0');
    
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds} ${ampm}`;
}

const leadCurrentStatus = ['Sign Up', 'Explore', 'Payment Initiated', 'Payment Failed', 'Payment Success'];

async function createLead (leadData) {
  const data = {    
    secret_key: SECRET_KEY,
    college_id: COLLEGE_ID,
    name: leadData.name,
    country_dial_code: leadData.country_dial_code,
    mobile: leadData.mobile,
    mobile_verified: "1",
    email: leadData.email,
    field_current_status_sheriyans: "Sign Up",
    field_current_date_time_sheriyans: getCurrentFormattedDate(),
    source: leadData.source,
    medium: leadData.medium,
    campaign: leadData.campaign,
  };
  const response = await request(data);
  user.findOneAndUpdate({email:leadData.email}, {crm_user_id: response.data.user_id}, {new: true}, (err, doc) => {
    if (err) {
        console.log("Something wrong when updating data!");
    }
    console.log(doc);
    return response;
  });
}


async function updateCourseLead (leadData, batchCode, userId) {
    const userIdStr = userId.toString();
    const applicationNo = batchCode + userIdStr.slice(-6);
    const data = {    
        secret_key: SECRET_KEY,
        college_id: COLLEGE_ID,
        user_id: leadData.user_id,
        name: leadData.name,
        country_dial_code: leadData.country_dial_code,
        mobile: leadData.mobile,
        mobile_verified: "1",
        email: leadData.email,
        field_current_status_sheriyans: leadData.field_current_status_sheriyans,
        field_current_date_time_sheriyans: getCurrentFormattedDate(),
        field_lead_category_sheryians: merittoCourseMap[batchCode],
        field_application_no: applicationNo,
        field_application_course: merittoCourseMap[batchCode],
        field_form_stage: leadData.field_form_stage,
        field_application_status: leadData.field_application_status,
        source: leadData.source,
        medium: leadData.medium,
        campaign: leadData.campaign,
    };
    console.log(data)
    const response = await request(data);
    if(data.user_id === undefined || data.user_id == ''){
        user.findOneAndUpdate({email:leadData.email}, {crm_user_id: response.data.user_id}, {new: true}, (err, doc) => {
            if (err) {
                console.log("Something wrong when updating data!");
            }
            console.log(doc);
            return response;
        });
    }
    return response;
}

module.exports = {
    createLead,
    updateCourseLead
};