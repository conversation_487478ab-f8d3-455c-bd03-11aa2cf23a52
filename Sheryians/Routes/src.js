const express = require("express");
const router = express.Router();
var { tryCatch } = require("../utils/tryCatch.js");
const srcController = require("../Controllers/src_controller.js");
const preventXSS = require("../utils/xss.js");
const { validateSrcFields, validateCheckExists } = require("../Controllers/src_validation.js");
const rateLimit = require("express-rate-limit");
const limiter = rateLimit({
    windowMs: 30 * 1000, // 30 sec
    max: 1, // Limit each IP to 1 requests per windowMs
    message: "Too many requests from this IP, please try again after 15 minutes",
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    keyGenerator: function (req) {
        return req.get("do-connecting-ip") || req.ip;
    },
});

// Add XSS prevention middleware
router.use(preventXSS);

// Submit Form Route
router.post("/", limiter, validateSrcFields, tryCatch(srcController.registerSrc));

// Check if email or phone exists
router.post("/check-exists", validateCheckExists, tryCatch(srcController.checkIfExists));

router.get("/submissions", (req, res) => {
    res.render("src_submissions");
});

// Update SRC status and phases
router.patch("/:uniqueId/status-phases", tryCatch(srcController.updateSrcStatusAndPhases));

// Get All Submissions (API endpoint)
router.get("/", tryCatch(srcController.getSubmissions));

// Get Filter Options (fields and cities)
router.get("/filter-options", tryCatch(srcController.getFilterOptions));

module.exports = router;
