const express = require('express');
const router = express.Router();
const meritto = require('../utils/meritto.js');
const User = require('../Models/user.js');

const { parsePhoneNumber } = require('libphonenumber-js');

function parsePhone(phoneStr) {
  try {
    const parsed = parsePhoneNumber(phoneStr);
    return {
      countryCode: `+${parsed.countryCallingCode}`,
      number: parsed.nationalNumber
    };
  } catch (err) {
    return {
      countryCode: '',
      number: phoneStr 
    };
  }
}

const merittoCourseMap = {
    'rb01': 'Backend Domination',
    'mco-01': 'Job Ready AI Powered Cohort',
    'rf01': 'Frontend Domination',
    'ap01': 'Aptitude and Reasoning',
    'dsa01': 'Java and DSA domination',
    'tj01': 'ThreeJS',
    'ddc01': 'DSA Domination Cohort'
}

router.post('/lead/scrolledToBottom', async (req, res) => {
    // const { mobile, course } = req.body;
    // if(!mobile || !course) {
    //     return res.json(false);
    // }
    // const response = await meritto.updateLead({
    //     mobile: mobile.replace('+91', ''),
    //     field_current_status_sheriyans: 'Scrolled Course',
    //     field_lead_category_sheryians: course
    // });
    // return res.json(response.status ? true : false);
    return res.json(true);
});

router.post('/lead/proceedToPayment', async (req, res) => {
    const { user_id, course_id } = req.body;
    if(!user_id || !course_id) {
        return res.json(false);
    }

    const user = await User.findById(user_id);
    if(!user) {
        return res.json(false);
    }

    const getCountryCodeandPhone = parsePhone(user.phoneNumber);

    const response = await meritto.updateCourseLead({
        user_id: user.crm_user_id,
        name: user.name,
        country_dial_code: getCountryCodeandPhone.countryCode,
        mobile: getCountryCodeandPhone.number,
        email: user.email,
        field_current_status_sheriyans: "Payment Initiated",
        field_form_stage: "Payment Initiated",
        field_application_status: "Payment Initiated",
        source: req.cookies.utm_source || "website",
        medium: req.cookies.utm_medium || "organic",
        campaign: req.cookies.utm_campaign || "organic",
    }, course_id, user_id);
    return res.json(response.status ? true : false);
});

module.exports = router;