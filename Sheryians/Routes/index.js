const express = require('express'); // import express
const router = express.Router(); // create express router 
const passport = require('passport'); // import passport
const homeController = require('../Controllers/home_controller.js'); // import home controller
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser'); // import body parser
const { c, cpp, node, python, java } = require('compile-run');
const path = require('path'); // import path
const authMiddleware = require('../Config/auth.middleware'); // import isAuthenticated middleware
const {getCertificate} = require("../utils/verifyCertificate.js")

const request_callback = require('../Models/request_callback.js'); // import request callback model

const request = require('request');


router.use(express.urlencoded({ extended: true }));
router.use(cookieParser());
router.use(bodyParser.urlencoded({ extended: false }));


// get and post of all routes
// router.get('/',passport.checkAuthentication,homeController.getHome); // get home with authentication
router.get('/', homeController.getHome); // get home with authentication
router.get('/offline', homeController.offline); // get home with authentication
router.get('/gwwc', (req, res) => {
  res.render("temp_gwc")
}); // get home with authentication

router.post('/request-callback', async (req, res) => {

  const contact = req.body.contact; 
  const date = req.body.datetime;
  const name = req.body.name;
  console.log(req.body)
  // const isValidContact = /^\d{10}$/.test(Number(contact));
  const isValidDate = !isNaN(Date.parse(date));
  const isValidName = name.length > 0;

  // if (!isValidContact) {
  //   return res.status(400).json({ errors: [ { msg: 'Invalid contact number' } ] });
  // }

  if (!isValidDate) {
    return res.status(400).json({ errors: [ { msg: 'Invalid date' } ] });
  }

  if (!isValidName) {
    return res.status(400).json({ errors: [ { msg: 'Invalid name' } ] });
  }
  const newRequest = new request_callback({
    contact: req.body.contact,
    date: req.body.date,
    name: req.body.name,
    enquiryFor: req.body.enquiryFor,
    courseName: req.body.courseName,
  });

  newRequest.save().then(() => {
    console.log('Request callback saved');
    return res.status(200).json({ msg: 'Request callback saved' });
  }).catch(err => {
    console.log(err);
  })
})


router.post('/UploadProfile_dp', homeController.UploadProfile_dp); // get home with authentication


router.get("/sheryians-ide", function (req, res) {
  res.render("sheryians_ide.ejs", {
    title: "Sheryians IDE",
    user: req.user
  });
});

router.post("/compilecode", function (req, res) {
  var code = req.body.code;
  var input = req.body.input;
  var inputRadio = req.body.inputRadio;
  var lang = req.body.lang;
  console.log(code);
  if (inputRadio === "true") {
    var program = {
      script: code,
      language: lang,
      stdin: input,
      versionIndex: "0",
      clientId: "61353d58015b04108ca813d23956df87",
      clientSecret: "1a6d6e0bf109e7c0cf04d27131a318f906efacbddeba9e101a291e89e43e80ae"
    };
    request({
      url: 'https://api.jdoodle.com/v1/execute',
      method: "POST",
      json: program
    },
      function (error, response, body) {
        console.log('error:', error);
        console.log('statusCode:', response && response.statusCode);
        console.log('body:', body);
        return res.send(body);
      });
  } else {
    var program = {
      script: code,
      language: lang,
      versionIndex: "0",
      clientId: "6027a2c6a4300478c22152ac5a3b609b",
      clientSecret: "90041abdf42133a6a84603dc11624c168acea3de321a465fb96deeb73b3e5246"
    };
    request({
      url: 'https://api.jdoodle.com/v1/execute',
      method: "POST",
      json: program
    },
      function (error, response, body) {
        console.log('error:', error);
        console.log('statusCode:', response && response.statusCode);
        console.log('body:', body);
        return res.send(body);
      });
  }
});

router.get("/fullStat", function (req, res) {
  compiler.fullStat(function (data) {
    res.send(data);
  });
});


// router.get('/addEnrolledStudents', homeController.addEnrolledStudents);
// router.get('/addorderid', homeController.addorderid);
// router.get('/completepayment/:id/:course_id', homeController.completePaymnet);
// router.get('/inertia', homeController.getInertia);
// router.get('/zero-to-one', homeController.getZeroToOne);
// router.post('/register-zero-to-one', homeController.registerZeroToOne);
router.get('/aboutUs', homeController.aboutUs);
router.get('/hire', homeController.comingSoon);
router.get('/adddefault', homeController.defaultdp);
router.get('/contact-us', (req, res, next) => {
  res.render('contactUs', { title: 'Contact us', pageTitle: 'Contact', request: req.user ? req : null })
});
router.get('/shipping-and-delivery', (req, res, next) => {
  res.render('shipping', { title: 'Shipping', pageTitle: 'Shipping', request: req.user ? req : null })
});
router.use('/GirlsWhoCode', require("./girlsWhoCode"))
router.use('/attendence', require("./attendence"))
router.use(passport.setAuthenticatedUser);
router.use('/bot', require('./chatbot')); // for chatbot
router.use('/signIn', require('./user')); // use user routesff
router.use('/signOut', require('./user')); // use user routes
router.use('/signup', require('./user')); // use user routes
router.use('/campusAmbassador', require('./campusAmbassador')); // use campus ambassador
router.use('/adminPanel', require('./adminPanel')); // use admin 
router.use('/course', require('./courses')); // use courses
router.use('/courses', require('./courses')); // use courses
router.use('/whizz', require('./classTest')); // use courses
router.use('/PaymentPortal', require('./PaymentPortal')); // use payment portal
router.use('/error', require('./error.js')); // use payment portal
router.use('/myAccount', require('./myAccount')); // use classroom
router.use('/classroom', require('./classroom')); // use classroom
router.use('/liveClass', require('./liveClass')); // use live class
router.use('/certificate', require('./certificate')); // use certificate
router.use('/merchandise', require('./merch')); // use merchandise
router.use('/feedback', require('./feedback')); // use feedback routes
router.use('/policy', require('./policy')); // use merchandise
router.use('/mentor', require('./mentor')); // use merchandise
router.use('/offline-Class', require('./offlineClass')); // use offline class
router.use('/figureout', require('./figureout')); // use figureout
router.use('/certificate', require('./certificate.js'));
router.use('/re-imagine', require('./re-imagine.js')); // use re-imagine
router.use('/coding', require('./coding.js')); // use re-imagine
router.use('/webinars', require('./webinar.js')); // use re-imagine
router.use('/marathon', require('./marathon.js')); // use re-imagine
router.use('/kodr', require('./kodr.js')); // use re-imagin
router.use('/qr', require('./qrRoutes.js')); // use re-imagin
router.use('/download', require('./downloads'));
router.use('/tracker', require('./tracker.js'));
router.use('/src', require('./src.js'));
router.use('/certificates/verify/:id', getCertificate);
router.get('/sitemap.xml', function (req, res) {
  res.sendFile(path.join(__dirname, '../sitemap.xml'));
});

router.get('/protected', authMiddleware.isAuthenticated, (req, res) => {
  res.send('Protected Route', req.user);
});

router.use('/hireFromUs', require('./hireFromUs.js'))
// router.use('/codebite', require('./codebite')); // use codebite
router.get('*', function (req, res) {
  res.sendFile(path.join(__dirname, '../Views/error_page.html'));
});
module.exports = router; // export router


