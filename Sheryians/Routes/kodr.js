const express = require('express'); // import express
const router = express.Router(); // create express router
const passport = require('passport'); // import passport
const authMiddleware = require('../Config/auth.middleware'); // import isAuthenticated middleware
const kodrController = require('../Controllers/kodr_controller.js'); // import merch controller
const path = require('path'); // import path


router.get('/', kodrController.getKodr); // get home with authentication')
// router.post('/register', kodrController.createSubmission); // get home with authentication')
// router.get('/registrations',authMiddleware.isAuthenticated, kodrController.allRegistrations); // get home with authentication')
// router.post('/update',authMiddleware.isAuthenticated, kodrController.updateStatus); // get home with authentication')
// router.post('/updatecall',authMiddleware.isAuthenticated, kodrController.updateCallStatus); // get home with authentication')

module.exports = router; // export router

