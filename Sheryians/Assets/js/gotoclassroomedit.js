function pushNotify(title, text, status) {
  new Notify({
    status: status,
    title: title,
    text: text,
    showCloseButton: true,
    autoclose: true,
    autotimeout: 3000,
  })
}

function seeLiveClassCredentials() {
  // Create the popup(with close button also) with the live class credentials which includes the server key and stream key and create a button to copy the server key and stream key
  const popup = document.createElement('div');
  popup.classList.add('popup');
  popup.innerHTML = `
    <div class="popupContent" style="background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); display: flex; flex-direction: column; gap: 20px;">
      <button class="close-popup"> End Live Stream </button>
      <h1>Start Live Class</h1>
      <div class="startLiveClass">
        <p>Title: <input type="text" id="liveClassTitle" placeholder="Enter class title"></p>
        <br><br>
        <button id="startLiveClassButton">Start Live Class</button>
      </div>
      <div class="credentials" style="display: none; ">
        <p style="display: flex; gap: 7px;">Server Key: <input type="text" id="server" readonly><button class="copyButton">Copy</button></p>
        <br>
        <p style="display: flex; gap: 7px;">Stream Key: <input type="text" id="streamKey" readonly><button class="copyButton">Copy</button></p>
        <br>
        <p>Use the above credentials to start the live class on OBS or any other streaming software</p>
        <br>
        <button id="moderatorButton">Moderator Preview</button>
        <br>
        <br>
        <button id="chatPreviewButton">Chat Preview</button>
      </div>
    </div>
  `;

  popup.style.display = 'flex';
  popup.style.opacity = 1;

  document.body.appendChild(popup);

  document.getElementById('startLiveClassButton').addEventListener('click', async () => {
    const title = document.getElementById('liveClassTitle').value;
    const button = document.getElementById('startLiveClassButton');
    const originalButtonText = button.innerHTML;
    button.innerHTML = 'Starting...'; // Show loader on button
    button.disabled = true; // Disable button once the live class has started
    button.style.cursor = 'not-allowed'; // Change cursor to not-allowed while starting the live class
    button.style.backgroundColor = 'gray'; // Change button color to gray while starting the live class

    try {
      const response = await fetch('/liveClass/create-liveClass', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ title })
      });
      if (response.ok) {
        const streamId = (await response.json()).streamId;
        const checkStreamStatus = async () => {
          try {
            const response2 = await fetch('/liveClass/list-running-streams');
            const streams = await response2.json();
            console.log(streams);
            const stream = streams.liveStreams.find((stream) => stream.streamId === streamId);

            if (stream && stream.status === "Ready to Start Broadcasting") {
              document.getElementById('server').value = stream.server;
              document.getElementById('streamKey').value = stream.serverKey;
              document.getElementById('moderatorButton').addEventListener('click', () => {
                // open window in new tab
                window.open(`/liveClass/get-liveClass/${window.location.href.split("/")[ 5 ]}`, '_blank');
              });
              document.getElementById('chatPreviewButton').addEventListener('click', () => {
                // open window in new tab
                window.open(`/liveClass/chat-preview/${window.location.href.split("/")[ 5 ]}`, '_blank');
              });
              document.querySelector('.credentials').style.display = 'block';
              button.innerHTML = 'Started'; // Revert button text
              clearInterval(intervalId); // Stop polling once the status is "Ready to Start Broadcasting"

              const response3 = await fetch('/liveClass/add-liveClass', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({ courseId: window.location.href.split("/")[ 5 ], liveStream: stream })
              });

              if (response3.ok) {
                alert('Live class started successfully');
              }

            }
          } catch (error) {
            console.error('Error checking stream status:', error);
          }
        };

        // Poll every 5 seconds
        const intervalId = setInterval(checkStreamStatus, 5000);
        document.querySelector('.close-popup').addEventListener('click', async () => {
          try {
            const response = await fetch('/liveClass/end-liveClass', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ streamId: streamId })
            });

            if (response.ok) {
              const response2 = await fetch('/liveClass/delete-liveClass', {
                method: 'DELETE',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({ streamId: streamId })
              });
              if(response2.ok) {
                popup.remove();
                alert('Live class ended successfully');
              }else{
                alert('An error occurred while ending the live class');
              }
            }
          } catch (error) {
            console.error('Error:', error);
            alert('An error occurred while ending the live class');
          }
        });
      } else {
        alert('Failed to start live class');
        button.innerHTML = originalButtonText; // Revert button text on failure
        button.disabled = false; // Enable button on failure
        button.style.cursor = 'pointer'; // Change cursor to pointer on failure
        button.style.backgroundColor = '#007bff'; // Change button color to default on failure
      }
    } catch (error) {
      console.error('Error:', error);
      alert('An error occurred while starting the live class');
      button.innerHTML = originalButtonText; // Revert button text on error
    }
  });

  document.querySelectorAll('.copyButton').forEach(button => {
    button.addEventListener('click', () => {
      const input = button.previousElementSibling;
      input.select();
      document.execCommand('copy');
    });
  });
}

function setProblemIdValue(val) {
  document.querySelector(".problemIdInput").value = val
  document.querySelector(".problemDiv").innerHTML = ""
}

async function renderCodingQuestions(event) {
  try {
    if (!event.value) return document.querySelector(".problemDiv").innerHTML = ""

    const { data } = await axios.post("/coding/getQuestions", { title: event.value })
    const clutter = data.questions.map((e) => {
      return `
        <p onclick='setProblemIdValue(${e.id})'>${e.id} : ${e.title}</p>
      `
    })
    document.querySelector(".problemDiv").innerHTML = clutter.join(" ")
  } catch (err) {
    console.log(err)
  }
}

function sortab() {
  const moduleWrappers = document.querySelectorAll(".moduleWrapper");

  document.querySelectorAll(".chapters .modules").forEach((moduleWrapper) => {
    new Sortable(moduleWrapper, {
      group: {
        name: "moduleWrapper",
        put: [ "moduleWrapper" ], // Allows items to be put into 'module' and 'lecture' groups
      },
      animation: 150, // Animation duration in milliseconds
      draggable: ".moduleWrapper", // Specifies which items inside the element should be draggable
    });
  })

  moduleWrappers.forEach((moduleWrapper) => {
    new Sortable(moduleWrapper, {
      group: {
        name: "module",
        put: [ "module" ], // Allows items to be put into 'module' and 'lecture' groups
      },
      animation: 150, // Animation duration in milliseconds
      draggable: ".module", // Specifies which items inside the element should be draggable
    });

    // Initialize Sortable for lectures inside each module
    const modules = moduleWrapper.querySelectorAll(".module");
    modules.forEach((module) => {
      const lectures = module.querySelector(".lectures");
      new Sortable(lectures, {
        group: "lecture", // Specifies the group name for lectures
        animation: 150, // Animation duration in milliseconds
        draggable: ".lecture", // Specifies which items inside the element should be draggable
      });
    });
  });
}
function resizeTextarea(event) {
  event.style.height = "auto"; // Reset height to auto to calculate the actual height needed
  event.style.height = event.scrollHeight + "px"; // Set the height to the scroll height
}

function changeLectureType(type) {
  const videoHeroAreaWrap = document.querySelector('#heroArea-video');
  const MCQHeroAreaWrap = document.querySelector('#heroArea-mcq');
  const codingHeroAreaWrap = document.querySelector('#heroArea-code');
  videoHeroAreaWrap.classList.remove("active")
  MCQHeroAreaWrap.classList.remove("active")
  codingHeroAreaWrap.classList.remove("active")

  if (type == "video") {
    videoHeroAreaWrap.classList.add("active")
  } else if (type == "code") {
    codingHeroAreaWrap.classList.add("active")
  } else if (type == "mcq") {
    MCQHeroAreaWrap.classList.add("active")
  }
}

function changeCodePreview(event, module) {
  document.querySelectorAll(".headLinks p").forEach(function (paragraph) {
    paragraph.classList.remove('active');
  });
  document.querySelectorAll(".codeInput .codeInputDiv").forEach(function (paragraph) {
    paragraph.classList.remove('active');
  });
  document.querySelector(`#${module}`).classList.add("active")
  event.classList.add("active")
}
var deleteModuleWrapperArray = []
var deleteModuleArray = []

async function saveConfig() {
  var upload = true;
  const saveObj = [];
  document.querySelectorAll(".moduleWrapper").forEach((modules, index) => {
    var modules_obj = [];
    if (!modules.querySelector("#moduleTitle").value) {
      modules.querySelector(".title").style.border = "2px solid red";
      upload = false;
      return;
    }
    modules.querySelectorAll(".module").forEach((module, ind) => {
      var lec_obj = [];
      if (!module.querySelector("#moduleTitle").value) {
        module.querySelector(".title").style.border = "2px solid red";
        upload = false;
        return;
      }
      module.querySelectorAll(".lecture").forEach((lecture, ind) => {
        var link_obj = [];
        if (lecture.getAttribute("data-lectureType") == "video") {
          if (lecture.getAttribute("data-type") == "comingSoon") {
            if (!lecture.querySelector("#lectureTitle").value || isNaN(new Date(lecture.querySelector("#lectureTime").value).getTime())) {
              lecture.style.border = "2px solid red";
              upload = false;
              return;
            } else {
              lecture.style.border = "2px solid transparent";
            }

            lec_obj.push({
              type: "video",
              guid: lecture.querySelector("#lectureGuid").value
                ? lecture.querySelector("#lectureGuid").value
                : "null",
              order: ind + 1,
              lec_id: lecture.getAttribute("data-lecture-id"),
              comingSoon: true,
              newLecture: true,
              comingSoonDetails: {
                title: lecture.querySelector("#lectureTitle").value
                  ? lecture.querySelector("#lectureTitle").value
                  : "New Lecture",
                date: new Date(
                  lecture.querySelector("#lectureTime").value
                ).getTime(),
              },
            });
            return;
          } else {

            if (!lecture.querySelector("#lectureGuid").value) {
              lecture.style.border = "2px solid red";
              upload = false;
              return;
            } else {
              lecture.style.border = "2px solid transparent";
            }
            if (!lecture.querySelector("#marks").value) {
              lecture.style.border = "2px solid red";
              upload = false;
              return;
            } else {
              lecture.style.border = "2px solid transparent";
            }

            lecture.querySelectorAll("#links").forEach((link, i) => {
              link_obj.push({
                naam: link.querySelector(".linkName").value,
                link: link.querySelector(".linkLink").value.trim(),
              });
            });
            lec_obj.push({
              type: "video",
              comingSoon: false,
              guid: lecture.querySelector("#lectureGuid").value,
              order: ind + 1,
              links: link_obj,
              points: lecture.querySelector("#marks").value,
              lec_id: lecture.getAttribute("data-lecture-id"),
              newLecture: lecture.querySelector("#newLecture").checked,
              marks: lecture.querySelector("#marks").value
            });
          }
        } else if (lecture.getAttribute("data-lectureType") == "code") {
          lec_obj.push({
            type: "code",
            order: ind + 1,
            newLecture: lecture.querySelector("#newLecture").checked,
            lec_id: lecture.getAttribute("data-lecture-id"),

          });
        } else if (lecture.getAttribute("data-lectureType") == "mcq") {
          lec_obj.push({
            type: "mcq",
            order: ind + 1,
            newLecture: lecture.querySelector("#newLecture").checked,
            lec_id: lecture.getAttribute("data-lecture-id"),
          });
        } else if (lecture.getAttribute("data-lectureType") == "pdf") {
          const title = lecture.querySelector("#lectureTitle").value
          const link = lecture.querySelector("#PDFLink").value
          const marks = lecture.querySelector("#marks").value

          if (!title | !link | !marks) {
            lecture.style.border = "2px solid red";
            upload = false;
            return;
          } else {
            lecture.style.border = "2px solid transparent";
          }
          lec_obj.push({
            type: "pdf",
            order: ind + 1,
            title: title,
            pdf: link,
            marks: marks,
            newLecture: lecture.querySelector("#newLecture").checked,
            lec_id: lecture.getAttribute("data-lecture-id"),

          });
        }
      });
      modules_obj.push({
        title: module.querySelector("#moduleTitle").value,
        order: ind + 1,
        lectures: lec_obj,
        ...(module.getAttribute("data-id") && { _id: module.getAttribute("data-id") }), // Conditionally add _id property
        courseId: window.location.href.split("/")[ 5 ],
        ...(module.querySelector(".comingSoonDate input") && { comingSoonDate: module.querySelector(".comingSoonDate input").value }) // Conditionally add comingSoonDate property
      })
    });
    saveObj.push({
      order: index + 1,
      title: modules.querySelector("#moduleTitle").value,
      modules: modules_obj,
      _id: modules.getAttribute("data-id"),
      courseId: window.location.href.split("/")[ 5 ],
      ...(modules.querySelector(".comingSoonModuleDate input") && { comingSoonDate: modules.querySelector(".comingSoonModuleDate input").value }), // Conditionally add comingSoonDate property
    });
  });
  if (upload) {
    console.log(saveObj)
    pushNotify("Uploading", "Your changes are being saved", "info")
    let live_class_notes = document.querySelector(".live_class_notes").value
    console.log(live_class_notes)
    await axios.post("/classroom/updateCourse", {live_class_notes, saveObj, deleteModuleArray, deleteModuleWrapperArray });
    window.location.reload();
  } else {
    pushNotify("Fill all fields", "Some Fields are empty please fill them out", "warning")

  }
}

async function saveMcqLecture(event) {
  const div = event.closest("#heroArea-mcq")
  const title = div.querySelector(".title").value
  const question = div.querySelector(".question").value
  const explaination = div.querySelector(".explaination").value
  const marks = div.querySelector(".marks").value
  const lectureId = div.getAttribute("data-lectureId")
  const snippet_code = snippetCode.getValue()
  const snippet_code_language = document.querySelector(".snippetLanguage").value
  const choice = []
  var correctOption = 0
  const courseId = window.location.href.split("/")[ 5 ]
  const wrapperId = div.getAttribute("data-wrapper-id")
  const moduleId = div.getAttribute("data-module-id")
  
  div.querySelectorAll(".choice").forEach((options, index) => {
    if (options.querySelector("textarea").value) {
      if (options.querySelector("input").checked) correctOption = index + 1
      choice.push({ option: options.querySelector("textarea").value, id: index + 1 })
    }
  })

  if (!title || !question || !explaination || !marks || choice.length == 0) {
    return pushNotify("Missing Fields", "Please fill all the fields", "error")
  }
  if (correctOption == 0) {
    return pushNotify("Missing Fields", "Please choose a correct option", "error")
  }
  try {
    div.querySelector("button").style.pointerEvents = "none"
    pushNotify("Saving", "Your lecture is being saved", "info")
    const formData = new FormData();
    formData.append('title', title);
    formData.append('question', question);
    formData.append('explaination', explaination);
    formData.append('marks', marks);
    formData.append('lectureId', lectureId);
    formData.append('codeSnippet', snippet_code);
    formData.append('codeSnippetLanguage', snippet_code_language);
    formData.append('courseId', courseId);
    formData.append('wrapperId', wrapperId);
    formData.append('moduleId', moduleId);
    formData.append('correctOption', correctOption);

    formData.append('choice', JSON.stringify(choice));

    const fileInput = div.querySelector(".explainationVideo"); // Update with your actual file input selector
    if (fileInput && fileInput.files.length > 0) {
      console.log("addingfile")
      formData.append('file', fileInput.files[ 0 ]);
    }
    const questionImage = div.querySelector(".questionImage"); // Update with your actual file input selector
    if (questionImage && questionImage.files.length > 0) {
      console.log("addingfile")
      formData.append('questionImage', questionImage.files[ 0 ]);
    }
    const response = await axios.post('/classroom/add-mcq-lecture', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    // const data = await axios.post("/classroom/add-mcq-lecture",{codeSnippet:snippet_code,codeSnippetLanguage:snippet_code_language,wrapperId,moduleId,title,explaination,marks,lectureId,choice,question,courseId,correctOption})
    // window.location.reload()
  } catch (err) {
    console.log(err)
    div.querySelector("button").style.pointerEvents = "all"
    return pushNotify("Server Error", err.response.data.message, "error")
  }
}
async function saveCodeLecture(event) {
  // saveCurrentEditorState(); // Save latest in-memory changes
  const div = event.closest("#heroArea-code")
  const problem_name = div.querySelector(".title").value
  const main_code = mainCode.getValue()
  const code_template = templateCode.getValue()
  const solution_code = solutionCode.getValue()
  const problemId = div.querySelector(".problemIdInput").value
  const difficulty = div.querySelector("#difficulty").value
  const points = div.querySelector(".marks").value
  const lectureId = div.getAttribute("data-lectureid")
  const wrapperId = div.getAttribute("data-wrapper-id")
  const moduleId = div.getAttribute("data-module-id")
  // Create an object to store code by language
  let code = codeData
  
  // console.log(!problem_name || !main_code || !code_template || !solution_code || !compilerId || !problemId || !difficulty || !points)
  if (!problem_name || !problemId || !difficulty || !points) {
    return pushNotify("Missing Fields", "Please fill all the fields", "error")
  }
  
  // Check if there's at least one language with both main and solution code
  const hasValidCodeLanguage = Object.keys(code).some(language => 
    code[language] && code[language].mainCode && code[language].solutionCode
  );
  
  if (!hasValidCodeLanguage) {
    return pushNotify("Missing Code", "Please provide main code and solution code for at least one language", "error")
  }
  console.log(codeData)
  try {
    div.querySelector("button").style.pointerEvents = "none"
    pushNotify("Saving", "Your lecture is being saved", "info")
    await axios.post("/classroom/add-code-lecture", {code, lectureId, wrapperId, moduleId, problemId, problem_name, main_code, code_template:code_template ? code_template : "" , solution_code, difficulty, points })
    window.location.reload()
  } catch (err) {
    console.log(err)
    div.querySelector("button").style.pointerEvents = "all"
    return pushNotify("Server Error", err.response.data.message, "error")
  }
}
document.onreadystatechange = () => {
  sortab();
};

function addLecture(event) {
  event.closest(".module").classList.add("open")

  event
    .closest(".module")
    .querySelector(".lectures")
    .insertAdjacentHTML(
      "beforeend",
      `<div class="lecture" onclick="changeLectureType('video')" data-lectureType="video">
        <div class="rgiht righ">
          <div class="inputFields">
            <p class="lectureTitle">
              <small>GUID</small>
              <input id="lectureGuid" type="text" placeholder="Guid" value="">
            </p>
            <p class="lectureTitle">
              <small>Marks</small>
              <input id="marks" type="text" placeholder="Marks" value="">
            </p>
          </div>
        </div>
        <div id="newCheck">
          <div>
            <input type="checkbox" id="newLecture" > <label for="newLecture">Make this lecture new</label>
          </div>
          <div>
            <button class="lectureBtn" onclick="addLink(this)"><i class="ri-add-box-line"></i></button>
            <button onclick="delLecture(this)"><i class="ri-delete-bin-7-fill"></i></button>
          </div>
        </div>
        <div id="allLinks"></div>
      </div>`
    );
  sortab()
  // showHideVideo()
}

function addModule(event) {
  event.closest(".moduleWrapper").classList.add("open")

  event
    .closest(".moduleWrapper")
    .insertAdjacentHTML(
      "beforeend",
      `<div class="module" data-id="">
      <div class="title">
      <div class="controlls">
        <h1 class="name">
          <input id="moduleTitle" spellcheck="false" placeholder='Module name' type="text">
        </h1>
        <button class="dropdownButton dropdownButtons">
          <i title="Add MCQ Questions" onclick="addMCQ(this,'<%=moduleWrapper._id%>','<%=module._id%>')" class="ri-checkbox-multiple-line"></i>
          <i title="Add Coding Questions" onclick="addCode(this,'<%=moduleWrapper._id%>','<%=module._id%>')" class="ri-code-box-line"></i>
          <i title="Add PDF" onclick="addPDF(this)" class="ri-file-pdf-2-line"></i>
          <i title="Add Lecture" onclick="addLecture(this)" class="ri-movie-line"></i>
          <i title="Add ComingSoon Lecture" onclick="addUpdate(this)" class="ri-time-fill"></i>
          <i title="Add ComingSoon Module" onclick="addModuleUpdate(this)" class="ri-calendar-event-fill"></i>
          <i title="Delete" onclick="deleteModule(this)" class="ri-delete-bin-7-fill"></i>
        </button>
      </div>
    </div>
          <div close style="height: 0px; transition: all 1s cubic-bezier(0.23, 1, 0.32, 1) 0s; padding-top: 0px; padding-bottom: 0px;" class="lectures">
          </div>`
    );
  sortab()
}
function addModuleWrapper() {
  document
    .querySelector(".modules")
    .insertAdjacentHTML(
      "beforeend",
      `<div class="moduleWrapper" data-id="">
      <div class="title">
      <div class='controlls'>
          <h1 class="name">
          <input id="moduleTitle" placeholder='Enter module name' spellcheck="false" type="text" value=""></h1>
          <button class="dropdownButton dropdownButtons">
            <i onclick="addModule(this)" class="ri-add-box-line"></i>
            <i title="Add ComingSoon Module" onclick="addModuleWrapperUpdate(this)" class="ri-calendar-event-fill"></i>
            <i onclick="delModuleWrapper(this)" class="ri-delete-bin-7-fill"></i>
          </button>
        </div>
      </div> 

      <div close style="height: 0px; transition: all 1s cubic-bezier(0.23, 1, 0.32, 1) 0s; padding-top: 0px; padding-bottom: 0px;" class="lectures"></div>`
    );
  sortab()
}
function addUpdate(event) {
  event.closest(".module").classList.add("open")
  event
    .closest(".module")
    .querySelector(".lectures")
    .insertAdjacentHTML(
      "beforeend",
      `<div data-type="comingSoon" class="lecture">
        <div class="rgiht righ ">
        <div class="lectureTitle comingSoon">
            <div>
                <small>Topic Name</small>
                <input id="lectureGuid" type="text" placeholder="Guid *if" value="">
            </div>
            <div>
                <small>Topic Name</small>
                <input id="lectureTitle" type="text" placeholder="Title" value="">
            </div>
            <div>
                <small>Date</small>
                <input id="lectureTime" type="date" value="">
            </div>
        </div>
        <button onclick="delLecture(this)"><i class="ri-delete-bin-7-fill"></i></button>

        </div>`
    );
  // showHideVideo()
}
function addModuleWrapperUpdate(event) {
  let module = event.closest(".title")
  if (module.querySelector(".comingSoonModuleDate")) {
    module.querySelector(".comingSoonModuleDate").remove()
    module.querySelector(".seperator").remove()
  } else {
    module.insertAdjacentHTML(
      "beforeend",
      `<div class="seperator"></div>
      <div class="comingSoonModuleDate">
      <p>Select date:</p>
      <input class='moduleComingSoonDate' type="date">
      </div>`
    );
  }
}
function addModuleUpdate(event) {
  let module = event.closest(".title")
  if (module.querySelector(".comingSoonDate")) {
    module.querySelector(".comingSoonDate").remove()
    module.querySelector(".seperator").remove()
  } else {
    module.insertAdjacentHTML(
      "beforeend",
      `<div class="seperator"></div>
      <div class="comingSoonDate">
      <p>Select date:</p>
      <input class='moduleComingSoonDate' type="date">
      </div>`
    );
  }
}

function addLink(event) {
  event
    .closest(".lecture")
    .querySelector("#allLinks")
    .insertAdjacentHTML(
      "beforeend",
      `
    <div id="links">
    <div>
      <small>Link name</small>
      <div>
        <input placeholder="Link Name" class="linkName" type="text">
        <button class="lectureBtn" onclick="delLink(this)"><i
          class="ri-delete-bin-7-fill"></i></button>
        </div>
    </div>
    <small>Link</small>
    <input placeholder="Link" class="linkLink" type="text">
  </div>
   `
    );
  // showHideVideo()
}

function addCode(event, wrapperId, moduleId) {
  const loader = document.querySelector('.loader').style.display = "none"
  if (!(event.closest(".moduleWrapper").getAttribute("data-id")) || !(event.closest(".module").getAttribute("data-id"))) {
    pushNotify("Module not created", "Before adding code or mcq please save the module", "warning")
    return
  }

  const videoHeroAreaWrap = document.querySelector('#heroArea-video');
  const MCQHeroAreaWrap = document.querySelector('#heroArea-mcq');
  const codingHeroAreaWrap = document.querySelector('#heroArea-code');
  const questionIds = problemsId.map((e) => {
    return `<option value="${e._id}">${e.title}</option>`
  })
  console.log(questionIds)
  videoHeroAreaWrap.classList.remove("active")
  MCQHeroAreaWrap.classList.remove("active")
  codingHeroAreaWrap.classList.add("active")
  codingHeroAreaWrap.setAttribute("data-lectureid", "")
  codingHeroAreaWrap.innerHTML = `<div class=" heading">
          <h1>Coding Lecture Title</h1>
          <button onclick="saveCodeLecture(this)">Create Lecture</button>
        </div>
        <div class="input">
          <p>Title</p>
          <input placeholder="Enter the title of the lecture" class="title" type="text">
        </div>
        <div class="codeInput">
          <div class="codeEditorHeader">
    <div class="headLinks">
      <p onclick="changeTab(this, 'mainCode')" class="active">Main Code</p>
      <p onclick="changeTab(this, 'templateCode')">Template Code</p>
      <p onclick="changeTab(this, 'solutionCode')">Solution Code</p>
    </div>
    <div>
      <select onchange="changeLanguage(this)">
        <option value="python">Python</option>
        <option value="java">Java</option>
        <option value="c_cpp">C</option>
        <option value="javascript">JavaScript</option>
      </select>
    </div>
          </div>

          <!-- Code Editor Areas -->
          <div id="mainCode" class="codeInputDiv active"></div>
          <div id="templateCode" class="codeInputDiv"></div>
          <div id="solutionCode" class="codeInputDiv"></div>
        </div>
        <div class="input problemId">
          <p>Problem ID</p>
          <input oninput="renderCodingQuestions(this)" type="text" class="problemIdInput">
          <div class="problemDiv">

          </div>
        </div>
        <div class="footer">
          <div class="input">
            <p>Default</p>
            <select name="" onchange="changeDefaultValue(this)" class="defaultValue" id="">
              <option value="62">Java</option>
              <option value="71">Python</option>
              <option value="49">C</option>
              <option value="63">JavaScript</option>
            </select>
          </div>
          <div class="input">
            <p>Difficulty</p>
            <select name="" id="difficulty">
              <option value="Easy">Easy</option>
              <option value="Medium">Medium</option>
              <option value="Hard">Hard</option>
            </select>
          </div>
          <div class="input">
            <p>Marks</p>
            <input placeholder="Enter the Marks" class="marks" type="text">
          </div>
          <div class="input">
        <p>Languages to Enable</p>
        <div class="language-toggle-group">
          <label class="language-toggle" for="lang-java">
            <input type="checkbox" id="lang-java" value="62" />
            <span>Java</span>
          </label>

          <label class="language-toggle" for="lang-python">
            <input type="checkbox" id="lang-python" value="71" />
            <span>Python</span>
          </label>

          <label class="language-toggle" for="lang-cpp">
            <input type="checkbox" id="lang-cpp" value="49" />
            <span>C</span>
          </label>

          <label class="language-toggle" for="lang-js">
            <input type="checkbox" id="lang-js" value="63" />
            <span>JavaScript</span>
          </label>
        </div>
      </div>
        </div>`

  codingHeroAreaWrap.setAttribute("data-wrapper-id", wrapperId)
  codingHeroAreaWrap.setAttribute("data-module-id", moduleId)
  mainCode = initializeEditor("mainCode");
  templateCode = initializeEditor("templateCode");
  solutionCode = initializeEditor("solutionCode");
  editors = {
          mainCode,
          templateCode,
          solutionCode
        }
  mainCode.session.on('change', saveCurrentEditorState);
  templateCode.session.on('change', saveCurrentEditorState);
  solutionCode.session.on('change', saveCurrentEditorState);
  // showHideVideo()
  const langToggleInputs = document.querySelectorAll('.language-toggle input[type="checkbox"]');
  langToggleInputs.forEach(input => {
    input.addEventListener('change', () => {
      const selectedId = input.value;
      for (const lang in codeData) {
        if (codeData[lang].compilerId === selectedId) {
          codeData[lang].enabled = input.checked;
        }
      }
      console.log("Updated codeData enabled flags:", codeData);
    });
  });
}
function addPDF(event) {
  event.closest(".module").classList.add("open")

  event
    .closest(".module")
    .querySelector(".lectures")
    .insertAdjacentHTML(
      "beforeend",
      `<div class="lecture" data-lectureType="pdf">
        <div class="rgiht righ">
          <div class="inputFields">
            <p class="lectureTitle">
              <small>Title</small>
              <input id="lectureTitle" type="text" placeholder="Enter Title" value="">
            </p>
            <p class="lectureTitle">
              <small>PDF Link</small>
              <input id="PDFLink" type="text" placeholder="Link" value="">
            </p>
            <p class="lectureTitle">
              <small>Marks</small>
              <input id="marks" type="text" placeholder="Marks" value="">
            </p>
          </div>
        </div>
        <div id="newCheck">
          <div>
            <input type="checkbox" id="newLecture" > <label for="newLecture">Make this lecture new</label>
          </div>
          <button onclick="delLecture(this)"><i class="ri-delete-bin-7-fill"></i></button>
        </div>
      </div>`
    );
  // showHideVideo()
}
function addMCQ(event, wrapperId, moduleId) {

  MCQHeroAreaWrap.innerHTML = `<div class="heading">
  <h1>MCQ title</h1>
  <button onclick="saveMcqLecture(this)">Create Lecture</button>
</div>
<div class="input">
  <p>Title</p>
  <input placeholder="Enter the title of the lecture" class="title" type="text">
</div>
<div class="input">
  <p>Question</p>
  <textarea placeholder="Enter the question" class="question" cols="30" rows="4"></textarea>
</div>
<div class="input">
  <p>Question Image</p>
  <input name="questionImage" type="file" class="questionImage">
</div>
<div class="multipleChoice">
  <p>Choices</p>
  <div class="choice">
    <textarea placeholder="Enter the choice text" name="" id="" cols="30" rows="2" oninput="resizeTextarea(this)"></textarea>
    <div class="answer">
      <input type="radio" name="option" id="checkbox1">
      <label for="checkbox1">Mark this as answer</label>
    </div>
  </div>
  <div class="choice">
    <textarea placeholder="Enter the choice text" name="" id="" cols="30" rows="2" oninput="resizeTextarea(this)"></textarea>
    <div class="answer">
      <input type="radio" name="option" id="checkbox2">
      <label for="checkbox2">Mark this as answer</label>
    </div>
  </div>
  <div class="choice">
    <textarea placeholder="Enter the choice text" name="" id="" cols="30" rows="2" oninput="resizeTextarea(this)"></textarea>
    <div class="answer">
      <input type="radio" name="option" id="checkbox3">
      <label for="checkbox3">Mark this as answer</label>
    </div>
  </div>
  <div class="choice">
    <textarea placeholder="Enter the choice text" name="" id="" cols="30" rows="2" oninput="resizeTextarea(this)"></textarea>
    <div class="answer">
      <input type="radio" name="option" id="checkbox4">
      <label for="checkbox4">Mark this as answer</label>
    </div>
  </div>
</div>
<div class="input">
  <p>Code Snippet <span>(Optional)</span></p>
  <div class="codeInputDiv active" id="snippetCode"></div>
</div>
<div class="input">
  <p>Code snippet language <span>(if snippet is given)</span></p>
  <input placeholder="Java, Python, etc..." type="text" class="snippetLanguage" >
</div>
<div class="input">
  <p>Explaination</p>
  <textarea class="explaination" placeholder="Explaination goes here" oninput="resizeTextarea(this)" rows="5"></textarea>
</div>
<div class="input">
  <p>Explaination Video</p>
  <input name="explainationVideo" type="file" class="explainationVideo" >
</div>
<div class="input">
  <p>Marks</p>
  <input type="text" class="marks" class="marks">
  </div>`
  videoHeroAreaWrap.classList.remove("active")
  MCQHeroAreaWrap.classList.add("active")
  codingHeroAreaWrap.classList.remove("active")
  MCQHeroAreaWrap.setAttribute("data-lectureid", "")
  MCQHeroAreaWrap.setAttribute("data-wrapper-id", wrapperId)
  MCQHeroAreaWrap.setAttribute("data-module-id", moduleId)
  snippetCode = initializeEditor("snippetCode")

  // showHideVideo()
}
function delModuleWrapper(event) {
  event.closest(".moduleWrapper").remove();
}
function delModule(event) {
  event.closest(".module").remove();
}
function delLink(event) {
  event.closest("#links").remove();
}
function delLecture(event) {
  event.closest(".lecture").remove();
}
async function deleteModuleWrapper(event) {
  const module_id = event.closest(".moduleWrapper").getAttribute("data-id");
  deleteModuleWrapperArray.push(module_id)
  event.closest(".moduleWrapper").remove();
}
async function deleteModule(event) {
  const module_id = event.closest(".module").getAttribute("data-id");
  const module_wrapper_id = event.closest(".module").getAttribute("data-wrapper-id");
  deleteModuleArray.push({ moduleID: module_id, moduleWrapperID: module_wrapper_id })
  event.closest(".module").remove();

}
async function deleteLecture(event) {
  const module_id = event.closest(".lecture").getAttribute("data-module-id");
  const lecture_id = event.closest(".lecture").getAttribute("data-lecture-id");
  const module_wrapper_id = event
    .closest(".module")
    .getAttribute("data-wrapper-id");

  try {
    await axios.post(
      `/classroom/deleteLecture/${module_wrapper_id}/${module_id}/${lecture_id}`
    );
    event.closest(".lecture").remove();
  } catch (err) {
    console.log(err);
  }
}
async function deleteLink(event) {
  const module_id = event.closest(".lecture").getAttribute("data-module-id");
  const lecture_id = event.closest(".lecture").getAttribute("data-lecture-id");
  const link_id = event.closest("#links").getAttribute("data-id");
  const module_wrapper_id = event
    .closest(".module")
    .getAttribute("data-wrapper-id");
  try {
    await axios.post(
      `/classroom/deleteLink/${module_wrapper_id}/${module_id}/${lecture_id}/${link_id}`
    );
    event.closest("#links").remove();
  } catch (err) {
    console.log(err);
  }
}
function isTablet() {
  return /iPad|Android/.test(navigator.userAgent) && !window.MSStream;
}
// Check if the current device is a mobile phone
function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
}
// Check if the current device is a desktop
function isDesktop() {
  return !isTablet() && !isMobile();
}



function showDescription() {
  let description = document.querySelector(".description");
  let descriptionHeight = description.getBoundingClientRect().height;
  let showMoreFlag = true;
  description.style.height = "6rem";

  description.querySelector(".showMore").addEventListener("click", (event) => {
    if (showMoreFlag) {
      description.style.height = ` calc( ${descriptionHeight}px  + 4rem)`;
      description.querySelector(".showMore p").textContent = "See less";
    } else {
      description.style.height = "6rem";
      description.querySelector(".showMore p").textContent = "See more";
    }
    showMoreFlag = !showMoreFlag;
  });
}

function likeDislike() {
  var like = document.querySelector(".like .likes");
  var dislike = document.querySelector(".like .dislikes");
  like.addEventListener("click", (event) => {
    var likesCounter = document.querySelector(
      ".page1 .heroArea .metaData .courseDetail .like .likes .count"
    );
    var currentLikes = Number(likesCounter.textContent);
    var likeParent = document.querySelector(".like");
    if (likeParent.classList.contains("liked")) {
      likeParent.classList.remove("liked");
      currentLikes -= 1;
      likesCounter.textContent = currentLikes;
    } else {
      likeParent.classList.add("liked");
      likeParent.classList.remove("disLiked");
      currentLikes += 1;
      likesCounter.textContent = currentLikes;
    }
  });
  dislike.addEventListener("click", (event) => {
    var likesCounter = document.querySelector(
      ".page1 .heroArea .metaData .courseDetail .like .likes .count"
    );
    var currentLikes = Number(likesCounter.textContent);
    var likeParent = document.querySelector(".like");
    if (likeParent.classList.contains("disLiked")) {
      likeParent.classList.remove("disLiked");
    } else {
      likeParent.classList.add("disLiked");
      likeParent.classList.remove("liked");
      if (currentLikes > 0) {
        currentLikes -= 1;
        likesCounter.textContent = currentLikes;
      }
    }
  });
}

function showHIdeComment() {
  if (window.innerWidth < 600) {
    let isCommentClose = false;
    var commentButton = document.querySelector(
      "#main .page1 .heroArea .comments .addComment .top .arrow"
    );
    var addComment = document.querySelector(
      "#main .page1 .heroArea .comments .addComment"
    );
    var comments = document.querySelector("#main .page1 .heroArea .comments");
    var commentHeight;
    window.onload = (eve) => {
      commentHeight = comments.getBoundingClientRect().height;
      addComment.style.height = addComment.offsetHeight + "px";
      comments.style.height = comments.getBoundingClientRect().height + "px";
    };
    commentButton.addEventListener("click", (event) => {
      if (!isCommentClose) {
        comments.style.height = `calc(${addComment.offsetHeight}px + 1rem)`;
      } else {
        comments.style.height = `${commentHeight}px`;
      }
      isCommentClose = !isCommentClose;
      commentButton.classList.toggle("close");
      commentButton.classList.toggle("open");
    });
  }
}

function newCommentValidation() {
  var value = document.querySelector("#newComment").value;
  if (value.trim()) {
    document.querySelector(".addComment").classList.add("valid");
  } else {
    document.querySelector(".addComment").classList.remove("valid");
  }
}
function createNodeFromHTML(htmlString) {
  const template = document.createElement("template");
  template.innerHTML = htmlString.trim();
  return template.content.firstChild;
}

function addComment(username, image, userId) {
  if (!commentButtonEnabled) return;
  commentButtonEnabled = false;
  var commentText = document.querySelector("#newComment").value;
  if (!commentText.trim()) return; // Do not submit empty comments

  // Create a new comment node
  var newComment = createNodeFromHTML(`<div class="comment">
    <div class="left">
    <img src="${image}" alt="">
    </div>
    <div class="right">
    <div class="metaData">
    <p class="user">${username}</p>
    <p class="time">0 seconds ago</p>
    </div>
    <p class="text">
    ${decodeHTML(document.querySelector("#newComment").value)}
    </p>
    </div>
    <div class="delete">
    <i class="ri-delete-bin-7-line"></i>
    </div>
</div>`);
  document.querySelector("#newComment").value = "";
  document.querySelector(".addComment").classList.remove("valid");
  document
    .querySelector(".comments .allcomments")
    .insertBefore(newComment, document.querySelectorAll(".comment")[ 0 ]);
  if (document.querySelector(".comments .noComments")) {
    document.querySelector(".comments .noComments").remove();
  }
  // Send the comment data to the server
  const url = `/classroom/lectures/add-comment/${lectureId}/${moduleId}`;
  // Data to send in the request body
  var data = {
    user: userId,
    username: username,
    image: image,
    content: commentText,
  };

  fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
    .then((response) => response.json())
    .then((data) => {
      let commentsCount = document
        .querySelector(".page1 .heroArea .comments .addComment .top p")
        .textContent.split(" ")[ 0 ];
      commentsCount = Number(commentsCount) + 1;
      document.querySelector(
        ".page1 .heroArea .comments .addComment .top p"
      ).textContent = `${commentsCount} Comments`;
      newComment.querySelector(".delete").addEventListener("click", () => {
        deleteComment(data.commentId, data.userId);
        newComment.remove();
      });
    })
    .catch((error) => {
      console.error("Error adding comment:", error);
    })
    .finally(() => {
      commentButtonEnabled = true;
    });
}

document
  .querySelector("#newComment")
  .addEventListener("input", newCommentValidation);

window.addEventListener("load", (event) => {
  showDescription();
  showHIdeComment();
  likeDislike();
});

function showHideLecture() {
  document.querySelectorAll(".module").forEach((module) => {
    let moduleIsOpen = true;
    let lectures = module.querySelector(".lectures");
    let lectureHeight = lectures.getBoundingClientRect().height;
    let isClose = !module.querySelector(".currentPlaying");
    lectures.style.height = lectureHeight + "px";
    if (isClose) {
      window.requestAnimationFrame((time) => {
        lectures.style.transition = "none";
        lectures.style.height = 0 + "px";
        lectures.style.paddingTop = 0 + "px";
        lectures.style.paddingBottom = 0 + "px";
        setTimeout(() => {
          lectures.style.transition = "all 1s cubic-bezier(0.23, 1, 0.32, 1)";
        }, 1000);
        module
          .querySelector(".dropdownButton i")
          .classList.toggle("ri-arrow-down-s-line");
        module
          .querySelector(".dropdownButton i")
          .classList.toggle("ri-arrow-up-s-line");
      });
    }
    var moduleTimeOut;
    module.querySelector(".title").addEventListener("click", (event) => {
      if (lectures.getBoundingClientRect().height == 0) {
        window.requestAnimationFrame((time) => {
          lectures.style.height = lectureHeight + "px";
          lectures.style.padding = "0 0.7rem";
          lectures.style.paddingRight = 0 + "px";
          lectures.style.paddingTop = 0.7 + "rem";
          lectures.style.paddingBottom = 2 + "rem";
          module
            .querySelector(".dropdownButton i")
            .classList.remove("ri-arrow-down-s-line");
          module
            .querySelector(".dropdownButton i")
            .classList.add("ri-arrow-up-s-line");
        });
      } else {
        window.requestAnimationFrame((time) => {
          lectures.style.height = 0 + "px";
          lectures.style.paddingTop = 0 + "px";
          lectures.style.paddingBottom = 0 + "px";
          module
            .querySelector(".dropdownButton i")
            .classList.add("ri-arrow-down-s-line");
          module
            .querySelector(".dropdownButton i")
            .classList.remove("ri-arrow-up-s-line");
        });
      }
    });
  });
}

function showHideDescription() {
  setTimeout(() => {
    var description = document.querySelector(
      "#heroArea-wrap .description #descriptionTextWrapper"
    );
    var descriptionHeight = description.getBoundingClientRect().height;
    var showMoreFlag = true;
    document
      .querySelector("#heroArea-wrap .description")
      .querySelector(".showMore")
      .addEventListener("click", (event) => {
        if (showMoreFlag) {
          description.style.height = `auto`;
          description.style.minHeight = "initial";
          document
            .querySelector("#heroArea-wrap .description")
            .querySelector(".showMore p").textContent = "Show less";
          document.querySelector("#heroArea-wrap .description").style.cursor =
            "initial";
          document
            .querySelector("#heroArea-wrap .description")
            .querySelector(".showMore").style.pointerEvents = "initial";
          showMoreFlag = false;
        } else {
          description.style.height = "1.2rem";
          description.style.minHeight = "1.2rem";
          document
            .querySelector("#heroArea-wrap .description")
            .querySelector(".showMore p").textContent = "...more";
          document
            .querySelector("#heroArea-wrap .description")
            .querySelector(".showMore").style.pointerEvents = "none";
          document.querySelector("#heroArea-wrap .description").style.cursor =
            "pointer";
          showMoreFlag = true;
        }
      });

    document.querySelector("#heroArea-wrap .description").addEventListener(
      "click",
      (eve) => {
        if (showMoreFlag) {
          description.style.height = `auto`;
          description.style.minHeight = "initial";
          document
            .querySelector("#heroArea-wrap .description")
            .querySelector(".showMore p").textContent = "Show less";
          document.querySelector("#heroArea-wrap .description").style.cursor =
            "initial";
          document
            .querySelector("#heroArea-wrap .description")
            .querySelector(".showMore").style.pointerEvents = "initial";
          showMoreFlag = !showMoreFlag;
        }
      },
      {
        capture: true,
      }
    );
  }, 1000);
}

function likeDislike() {
  if (!isUserLoggedIn || !fullAccess) {
    return;
  }
  var like = document.querySelector(".like .likes");
  var dislike = document.querySelector(".like .dislikes");
  like.addEventListener("click", (event) => {
    var likesCounter = document.querySelector(
      ".page1 .heroArea .metaData .courseDetail .like .likes .count"
    );
    var currentLikes = Number(likesCounter.textContent);
    var likeParent = document.querySelector(".like");
    if (likeParent.classList.contains("liked")) {
      likeParent.classList.remove("liked");
      currentLikes -= 1;
      likesCounter.textContent = currentLikes;
    } else {
      likeParent.classList.add("liked");
      likeParent.classList.remove("disLiked");
      currentLikes += 1;
      likesCounter.textContent = currentLikes;
    }
  });
  dislike.addEventListener("click", (event) => {
    var likesCounter = document.querySelector(
      ".page1 .heroArea .metaData .courseDetail .like .likes .count"
    );
    var currentLikes = Number(likesCounter.textContent);
    var likeParent = document.querySelector(".like");
    if (likeParent.classList.contains("disLiked")) {
      likeParent.classList.remove("disLiked");
    } else {
      likeParent.classList.add("disLiked");
      if (likeParent.classList.contains("liked")) {
        likeParent.classList.remove("liked");
        if (currentLikes > 0) {
          currentLikes -= 1;
          likesCounter.textContent = currentLikes;
        }
      }
    }
  });
}

var isCommentOpen = false;
function showHIdeComment() {
  if (window.innerWidth <= 820) {
    if (isCommentOpen) {
      window.requestAnimationFrame(() => {
        document.querySelector("#realComments").style.top = `100dvh`;
      });
    } else {
      window.requestAnimationFrame(() => {
        document.querySelector(
          "#realComments"
        ).style.top = `calc(100vw * 9 / 16 + 3.5rem)`;
      });
    }
    isCommentOpen = !isCommentOpen;
  }
}
function closeComment() {
  if (window.innerWidth < 600) {
    window.requestAnimationFrame(() => {
      document.querySelector("#realComments").style.top = `100dvh`;
    });
    isCommentOpen = false;
  }
}

function newCommentValidation() {
  var value = document.querySelector("#newComment").value;
  if (value.trim()) {
    document.querySelector(".addComment").classList.add("valid");
  } else {
    document.querySelector(".addComment").classList.remove("valid");
  }
}
function createNodeFromHTML(htmlString) {
  const template = document.createElement("template");
  template.innerHTML = htmlString.trim();
  return template.content.firstChild;
}

function addComment(username, image, userId) {
  if (!commentButtonEnabled) return;
  if (!isUserLoggedIn || !fullAccess) {
    openPhonePopUp();
    return;
  }
  commentButtonEnabled = false;
  var commentText = document.querySelector("#newComment").value.slice(0, 1500);
  if (!commentText.trim()) return; // Do not submit empty comments

  // Create a new comment node
  var newComment = createNodeFromHTML(`<div class="comment">
    <div class="left">
    <img src="${image}" alt="">
    </div>
    <div class="right">
    <div class="top">
        <div class="metaData">
            <p class="user">${username}</p>
            <p class="time">0 seconds ago</p>
        </div>
        <p class="text">
            ${decodeHTML(document.querySelector("#newComment").value)}
        </p>
    </div>
    <div class="bottom">
        <div class="likeDislike">
            <div class="likes">
                <i class="ri-thumb-up-line"></i><span>0</span>
            </div>
            <div class="dislikes">
                <i class="ri-thumb-down-line"></i>
            </div>
        </div>
        <div class="reply"><p>Reply</p></div>
    </div>   
    </div>
    <div class="delete">
    <i class="ri-more-2-fill"></i>
    <div class="options">
      <div class='option deleteComment'>
        Delete
      </div>
    </div>
  </div>
</div>`);
  document.querySelector("#newComment").value = "";
  document.querySelector(".addComment").classList.remove("valid");
  document
    .querySelector(".comments .allcomments")
    .insertBefore(
      newComment,
      document.querySelectorAll(".comments .allcomments .comment")[ 0 ]
    );
  if (document.querySelector(".comments .noComments")) {
    document.querySelector(".comments .noComments").remove();
  }
  // Send the comment data to the server
  const url = `/classroom/lectures/add-comment/${lectureId}/${moduleId}`;
  // Data to send in the request body
  var data = {
    user: userId,
    username: username,
    image: image,
    content: commentText,
  };

  fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      if (response.status === 401) {
        openSignInpopUp();
        setTimeout(() => {
          window.location.href = "/signIn";
        }, 5000);
        return;
      }
      return response.json();
    })
    .then((data) => {
      let commentsCount = document
        .querySelector(".page1 .heroArea .comments .addComment .top p")
        .textContent.split(" ")[ 0 ];
      commentsCount = Number(commentsCount) + 1;
      document.querySelector(
        ".page1 .heroArea .comments .addComment .top p"
      ).textContent = `${commentsCount} Comments`;
      newComment.querySelector(".delete").addEventListener("click", () => {
        deleteComment(data.commentId, data.userId);
        newComment.remove();
      });

      // add a like dislike feature to replies
      const likeDislike = newComment.querySelector(".likeDislike");
      const likes = likeDislike.querySelector(".likes");
      const dislikes = likeDislike.querySelector(".dislikes");
      const likesCount = likes.querySelector("span");

      likes.addEventListener("click", () => {
        likeComments(data.commentId, userId, newComment, false);
      });
      dislikes.addEventListener("click", () => {
        dislikeComments(data.commentId, userId, newComment, false);
      });

      // Add a click event listener to reply to replies
      const reply = newComment.querySelector(".reply");
      reply.onclick = () => {
        const addCommentDiv = addCommentDivToComment(
          newComment,
          username,
          data.commentId,
          false
        );
        addCommentDiv.querySelector(".texty").focus();
      };
    })
    .catch((error) => {
      console.error("Error adding comment:", error);
    })
    .finally(() => {
      commentButtonEnabled = true;
      const textarea = document.querySelector("#newComment");
      textarea.style.border = "none";
      textarea.style.borderBottom = "1px solid var(--primaryDark)";
      textarea.style.color = "black";
      textarea.style.backgroundColor = "transparent";
    });
  if (texty) texty.style.height = "auto";
}

function addReply(
  username,
  image,
  userId,
  parentCommentId,
  parentCommentUser,
  commentDiv,
  content,
  isreply
) {
  if (!commentButtonEnabled) return;
  if (!isUserLoggedIn || !fullAccess) {
    openPhonePopUp();
    return;
  }
  commentButtonEnabled = false;

  var commentText = content.textContent.slice(0, 1500);
  if (!commentText.trim()) return; // Do not submit empty replies

  // Create a new reply node
  var newReply = createNodeFromHTML(`<div class="comment">
        <div class="left">
            <img src="${image}" alt="">
        </div>
        <div class="right">
            <div class="top">
                <div class="metaData">
                    <p class="user">${username}</p>
                    <p class="time">0 seconds ago</p>
                </div>
                <p class="text">
                    ${decodeHTML(content.textContent)}
                </p>
            </div>
            <div class="bottom">
                <div class="likeDislike">
                    <div class="likes">
                        <i class="ri-thumb-up-line"></i><span>0</span>
                    </div>
                    <div class="dislikes">
                        <i class="ri-thumb-down-line"></i>
                    </div>
                </div>
                <div class="reply"><p>Reply</p></div>
            </div>
        </div>
        <div class="delete">
            <i class="ri-more-2-fill"></i>
            <div class="options">
                <div class='option deleteReply'>
                    Delete
                </div>
            </div>
        </div>
    </div>`);

  // Clear the reply textarea
  commentDiv.querySelector(".addComment .texty").innerHTML = "";
  commentDiv.querySelector(".addComment").classList.remove("valid");
  commentDiv.querySelector(".addComment").style.display = "none";
  commentDiv.querySelector(".addComment").remove();

  // Find the parent comment element and append the reply to it
  if (!isreply) {
    if (commentDiv.querySelector(".right .replyText")) {
      const repliesCount = commentDiv
        .querySelector(".right .replyText")
        .textContent.split(" ")[ 0 ];
      commentDiv.querySelector(".right .replyText").textContent = `${Number(repliesCount) + 1
        } replies`;
    } else {
      const replyText = document.createElement("div");
      replyText.classList.add("replyText");
      replyText.textContent = "1 Replies";
      commentDiv.querySelector(".right").appendChild(replyText);
    }

    if (!commentDiv.querySelector(".right .replies")) {
      const repliesDiv = document.createElement("div");
      repliesDiv.appendChild(newReply);
      repliesDiv.classList.add("replies");
      if (!commentDiv.querySelector(".right .allReplies")) {
        commentDiv.querySelector(".right").appendChild(repliesDiv);
      } else {
        commentDiv
          .querySelector(".right")
          .insertBefore(
            repliesDiv,
            commentDiv.querySelector(".right .allReplies")
          );
      }
    } else {
      const repliesDiv = commentDiv.querySelector(".right .replies");
      repliesDiv.appendChild(newReply);
    }
  } else {
    // append the reply to the parent comment
    const repliesCount = commentDiv.parentNode.parentNode.parentNode
      .querySelector(".right .replyText")
      .textContent.split(" ")[ 0 ];
    commentDiv.parentNode.parentNode.parentNode.querySelector(
      ".right .replyText"
    ).textContent = `${Number(repliesCount) + 1} Replies`;
    insertAfter(commentDiv, newReply);
  }

  // Send the reply data to the server
  const url = `/classroom/lectures/add-reply/${lectureId}/${moduleId}`;
  const course_id = window.location.href.split("/")[ 5 ];
  // Data to send in the request body
  var data = {
    user: userId,
    username: username,
    image: image,
    content: commentText,
    parentCommentId: parentCommentId, // Add parentCommentId to specify the parent comment
    course_id: course_id,
    parentCommentUser: parentCommentUser,
  };

  fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      if (response.status === 401) {
        openSignInpopUp();
        setTimeout(() => {
          window.location.href = "/signIn";
        }, 5000);
        return;
      }
      return response.json();
    })
    .then((data) => {
      // Similar code as addComment to update the UI with the new reply
      // Add a click event listener to delete replies
      newReply.querySelector(".delete").addEventListener("click", () => {
        if (isreply) {
          deleteReply(
            data.reply._id,
            userId,
            newReply.parentNode.parentNode.parentNode
          );
        } else {
          deleteReply(data.reply._id, userId, commentDiv);
        }
        newReply.remove();
      });

      // add a like dislike feature to replies
      const likeDislike = newReply.querySelector(".likeDislike");
      const likes = likeDislike.querySelector(".likes");
      const dislikes = likeDislike.querySelector(".dislikes");
      const likesCount = likes.querySelector("span");

      likes.addEventListener("click", () => {
        likeComments(data.reply._id, userId, newReply, true);
      });
      dislikes.addEventListener("click", () => {
        dislikeComments(data.reply._id, userId, newReply, true);
      });

      // Add a click event listener to reply to replies
      const reply = newReply.querySelector(".reply");
      reply.onclick = () => {
        const addCommentDiv = addCommentDivToComment(
          newReply,
          username,
          parentCommentId,
          true
        );
        addCommentDiv.querySelector(".texty").focus();
      };
    })
    .catch((error) => {
      console.error("Error adding reply:", error);
    })
    .finally(() => {
      commentButtonEnabled = true;
      const textarea = document.querySelector("#newComment");
      textarea.style.border = "none";
      textarea.style.borderBottom = "1px solid var(--primaryDark)";
      textarea.style.color = "black";
      textarea.style.backgroundColor = "transparent";
    });

  if (texty) {
    texty.style.height = "auto";
  }
}

function clearComment() {
  document.querySelector("#newComment").value = "";
  document.querySelector(".addComment").classList.remove("valid");
}
var isAllVideoClose = true;
function seeAllVideosInMobileView() {
  var chapters = document.querySelector(".page1 .chapters");
  if (isMobile() || isTablet()) {
    var iLogo = document.querySelector(
      ".page1 .chapters .mobileViewTitle i:nth-last-child(1)"
    );
    var mobileViewTitle = document.querySelector(
      ".page1 .chapters .mobileViewTitle"
    );
    var stagger = document.querySelector(
      ".page1 .chapters .mobileViewTitle .stagger"
    );
    iLogo.classList.toggle("ri-close-line");
    iLogo.classList.toggle("ri-arrow-up-s-line");
    if (isAllVideoClose) {
      window.requestAnimationFrame(() => {
        // chapters.style.height = "calc(100dvh - 100vw * 9 / 16 + 2.5rem)";
        // chapters.style.width = "97%";
        // chapters.style.top = "calc(100vw * 9 / 16 + 3.5rem )";
        // chapters.style.paddingBottom = "2.5rem";
        // chapters.style.paddingTop = "0.4rem";
        // chapters.style.backgroundColor = "#ececec";
        mobileViewTitle.style.paddingTop = "1rem";
        stagger.style.display = "initial";
      });
    } else {
      window.requestAnimationFrame(() => {
        mobileViewTitle.style.paddingTop = "0.5rem";
        // chapters.style.height = "3.5rem";
        // chapters.style.paddingTop = "0.2rem";
        // chapters.style.width = "90%";
        // chapters.style.top = "calc(100dvh - 5.5rem)";
        // chapters.style.paddingBottom = "0";
        // chapters.style.backgroundColor = "var(--primaryLight)";
        stagger.style.display = "none";
      });
    }
    isAllVideoClose = !isAllVideoClose;
  }
}

document
  .querySelector("#newComment")
  .addEventListener("input", newCommentValidation);

// addEventListener('touchstart', (eve) => {
//     // eve.preventDefault()
//     // console.log(eve.target)
//     console.log(eve)
//     document.querySelector('.page1 .chapters .mobileViewTitle .stagger').style.top = eve.clientY + 'px'
// })
var mobileViewTitleFlag = true;
var chapters = document.querySelector(".page1 .chapters");
var comments = document.querySelector("#realComments");
document
  .querySelector(".page1 .chapters .mobileViewTitle")
  .addEventListener("touchmove", (eve) => {
    eve.preventDefault();
    if (mobileViewTitleFlag && !isAllVideoClose) {
      requestAnimationFrame(() => {
        chapters.style.top = `max(calc(100vw * 9 / 16 + 3.5rem ),${eve.touches[ 0 ].clientY}px)`;
      });
      if (innerHeight - 5.5 * 16 <= eve.touches[ 0 ].clientY) {
        mobileViewTitleFlag = false;
        seeAllVideosInMobileView();
      }
    }
  });
document
  .querySelector(".page1 .chapters .mobileViewTitle")
  .addEventListener("touchend", (eve) => {
    if (!isAllVideoClose) {
      requestAnimationFrame(() => {
        mobileViewTitleFlag = true;
        chapters.style.top = eve.clientY + "px";
      });
      // console.log(eve.touches[ 0 ].clientY, innerHeight / 2)
      if (eve.changedTouches[ 0 ].clientY >= innerHeight / 1.5) {
        seeAllVideosInMobileView();
      } else {
        requestAnimationFrame(() => {
          chapters.style.top = "calc(100vw * 9 / 16 + 3.5rem )";
        });
      }
    }
  });

document
  .querySelector(".addComment .top")
  .addEventListener("touchmove", (eve) => {
    eve.preventDefault();
    requestAnimationFrame(() => {
      comments.style.top = eve.touches[ 0 ].clientY + "px";
    });
  });
document
  .querySelector(".addComment .top")
  .addEventListener("touchend", (eve) => {
    // console.log(eve.touches[ 0 ].clientY, innerHeight / 2)
    if (eve.changedTouches[ 0 ].clientY >= innerHeight / 2) {
      requestAnimationFrame(() => {
        comments.style.top = "100dvh";
        isCommentOpen = false;
      });
    } else {
      requestAnimationFrame(() => {
        if (isCommentOpen) {
          comments.style.top = "calc(100vw * 9 / 16 + 3.5rem )";
          isCommentOpen = true;
        }
      });
    }
  });

function openPhonePopUp() {
  document.querySelector(".phoneNumberPopUp").style.display = "flex";
  document.querySelector(".phoneNumberPopUp").classList.add("active");
  document.querySelector(".phoneNumberPopUp").style.opacity = "1";
}
function openSharePopUp() {
  document.querySelector(".sharePopup").style.display = "flex";
  document.querySelector(".sharePopup").classList.add("active");
  document.querySelector(".sharePopup").style.opacity = "1";
}
function openFeedbackPopUp() {
  document.querySelector(".feedbackPopup").style.display = "flex";
  document.querySelector(".feedbackPopup").classList.add("active");
  document.querySelector(".feedbackPopup").style.opacity = "1";
}
function openSignInpopUp() {
  document.querySelector(".signInPopup").style.display = "flex";
  document.querySelector(".signInPopup").classList.add("active");
  document.querySelector(".signInPopup").style.opacity = "1";
}

function openHardwareAccelerationPopUp() {
  document.querySelector(".hardwareAcceleraionPopup").style.display = "flex";
  document.querySelector(".hardwareAcceleraionPopup").classList.add("active");
  document.querySelector(".hardwareAcceleraionPopup").style.opacity = "1";
}

function isWebGLSupported() {
  try {
    const canvas = document.createElement("canvas");
    return !!(
      window.WebGLRenderingContext &&
      (canvas.getContext("webgl") || canvas.getContext("experimental-webgl"))
    );
  } catch (e) {
    return false;
  }
}

function copyToClipboard(content) {
  const tempTextArea = document.createElement("textarea");
  tempTextArea.value = content;
  document.body.appendChild(tempTextArea);
  tempTextArea.select();
  document.execCommand("copy");
  document.body.removeChild(tempTextArea);
}

document.onreadystatechange = function () {
  if (document.readyState == "complete") {
    showHideDescription();
    showHideLecture();
    likeDislike();

    document.querySelectorAll(".unauthorized").forEach((unauth) => {
      unauth.addEventListener("click", (event) => {
        event.preventDefault();
        openSignInpopUp();
      });
    });
  }
};
window.addEventListener("load", (event) => {
  setTimeout(() => {
    if (!isMobileLandscape()) seeAllVideosInMobileView();
  }, 750);
});
function isMobileLandscape() {
  if (
    /Mobi/.test(navigator.userAgent) &&
    window.innerWidth > window.innerHeight
  ) {
    return true; // Mobile and in landscape view
  } else {
    return false; // Not mobile or not in landscape view
  }
}

async function submitFeedback(event) {
  event.innerHTML = `<div style="display: initial; margin: 0px 20px; height: 1em;width: 1em;"  class="container"></div>`;
  const feedback = event.closest(".center").querySelector("textarea");
  try {
    const data = await axios.post("/classroom/feedback", {
      feedback: feedback.value,
      lectureId,
      courseId,
    });
    event.textContent = "Send Feedback";
    feedback.value = "";
    document.querySelectorAll(".popup").forEach((pop) => {
      pop.classList.remove("active");
      pop.style.display = "none";
      pop.style.opacity = "0";
    });
  } catch (err) {
    event.style.pointerEvents = "none";
    event.textContent = "Try Again Later";
    console.log(err);
  }
}

function showToast(text) {
  const toast = document.getElementById("toast");
  toast.innerText = text;
  toast.classList.add("visible");

  setTimeout(() => {
    toast.classList.remove("visible");
  }, 3000); // Hide the toast after 3 seconds (3000 milliseconds)
}

function closeModuleWrapper(event) {
  console.log(event.closest(".moduleWrapper"))
  if (event.closest(".moduleWrapper").classList.contains("open")) {
    event.closest(".moduleWrapper").classList.remove("open")
  } else {
    event.closest(".moduleWrapper").classList.add("open")
  }
}

function closeModule(event) {
  if (event.closest(".module").classList.contains("open")) {
    event.closest(".module").classList.remove("open")
  } else {
    event.closest(".module").classList.add("open")
  }
}

function changeCodeLanguage(event){
  console.log(event.value)
  mainCode.session.setMode(`ace/mode/${event.value}`);
  solutionCode.session.setMode(`ace/mode/${event.value}`);
  templateCode.session.setMode(`ace/mode/${event.value}`);
}