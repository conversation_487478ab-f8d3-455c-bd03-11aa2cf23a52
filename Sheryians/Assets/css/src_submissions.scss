// SRC Submissions Page Styles
.src-submissions {
    font-family: gilroy;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;

    &__header {
        margin-bottom: 30px;

        h1 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5rem;
            font-weight: 700;
        }

        p {
            color: #718096;
            font-size: 1.1rem;
        }
    }

    &__filters {
        background: #f7fafc;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &-title {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        &-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        &-group {
            label {
                display: block;
                margin-bottom: 5px;
                color: #4a5568;
                font-weight: 500;
                font-size: 0.9rem;
            }

            select,
            input {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                font-size: 0.9rem;
                transition: border-color 0.2s;

                &:focus {
                    outline: none;
                    border-color: #3182ce;
                    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
                }
            }
        }

        &-actions {
            display: flex;
            gap: 10px;

            button {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;

                &.apply {
                    background: #3182ce;
                    color: white;

                    &:hover {
                        background: #2c5282;
                    }
                }

                &.clear {
                    background: #e2e8f0;
                    color: #4a5568;

                    &:hover {
                        background: #cbd5e0;
                    }
                }
            }
        }
    }

    &__table-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    &__table {
        width: 100%;
        border-collapse: collapse;

        thead {
            background: #edf2f7;

            th {
                padding: 15px 12px;
                text-align: left;
                font-weight: 600;
                color: #2d3748;
                border-bottom: 2px solid #e2e8f0;
                cursor: pointer;
                position: relative;

                &:hover {
                    background: #e2e8f0;
                }

                &.sortable::after {
                    content: "↕";
                    position: absolute;
                    right: 8px;
                    opacity: 0.5;
                }

                &.sort-asc::after {
                    content: "↑";
                    opacity: 1;
                }

                &.sort-desc::after {
                    content: "↓";
                    opacity: 1;
                }
            }
        }

        tbody {
            tr {
                transition: background-color 0.2s;

                &:hover {
                    background: #f7fafc;
                }

                &:nth-child(even) {
                    background: #fafafa;
                }

                td {
                    padding: 12px;
                    border-bottom: 1px solid #e2e8f0;
                    color: #4a5568;
                }
            }
        }
    }

    &__status {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: capitalize;

        &--pending {
            background: #fef5e7;
            color: #d69e2e;
        }

        &--good-fit {
            background: #e6fffa;
            color: #319795;
        }

        &--best-fit {
            background: #e6ffed;
            color: #38a169;
        }

        &--not-fit {
            background: #fed7d7;
            color: #e53e3e;
        }

        &--accepted {
            background: #e6ffed;
            color: #38a169;
        }

        &--rejected {
            background: #fed7d7;
            color: #e53e3e;
        }
    }

    &__pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        background: #f7fafc;

        &-info {
            color: #718096;
            font-size: 0.9rem;
        }

        &-controls {
            display: flex;
            gap: 10px;

            button {
                padding: 8px 12px;
                border: 1px solid #e2e8f0;
                background: white;
                border-radius: 6px;
                cursor: pointer;
                color: #4a5568;
                transition: all 0.2s;

                &:hover:not(:disabled) {
                    background: #edf2f7;
                    border-color: #cbd5e0;
                }

                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }

                &.active {
                    background: #3182ce;
                    color: white;
                    border-color: #3182ce;
                }
            }
        }
    }

    &__loading {
        text-align: center;
        padding: 40px;
        color: #718096;
        font-size: 1.1rem;

        &::before {
            content: "";
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid #3182ce;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
    }

    &__error {
        background: #fed7d7;
        color: #e53e3e;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }

    &__empty {
        text-align: center;
        padding: 40px;
        color: #718096;
        font-size: 1.1rem;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// Responsive design
@media (max-width: 768px) {
    .src-submissions {
        padding: 10px;

        &__filters {
            &-grid {
                grid-template-columns: 1fr;
            }

            &-actions {
                flex-direction: column;
            }
        }

        &__table-container {
            overflow-x: auto;
        }

        &__table {
            min-width: 800px;

            th,
            td {
                padding: 8px;
                font-size: 0.8rem;
            }
        }

        &__pagination {
            flex-direction: column;
            gap: 15px;

            &-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    }
}
