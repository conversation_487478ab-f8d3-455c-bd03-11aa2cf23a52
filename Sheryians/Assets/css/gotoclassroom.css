html,
body {
  background-color: var(--background-color);
  height: 100%;
  width: 100%;
}

body.no-max-width main {
  max-width: 100%;
}
body.no-max-width nav .header {
  max-width: 100%;
}

.navCursor {
  z-index: 1000 !important;
}
.navCursor .nav-right {
  z-index: 1001 !important;
}

nav {
  transition: all 0.85s cubic-bezier(0.23, 1, 0.32, 1);
}
nav::after {
  transition: all 0.85s cubic-bezier(0.23, 1, 0.32, 1);
}
nav .nav-right .toggleTheme {
  display: initial;
}

.doubtInputPopup {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.584);
  z-index: 1000;
  display: none;
  justify-content: center;
  align-items: center;
}
.doubtInputPopup .doubt-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 25rem;
  height: 20rem;
  background-color: #333;
  padding: 1rem;
  border-radius: 5px;
}
.doubtInputPopup .doubt-container p {
  opacity: 0;
  font-size: 0.9rem;
  color: rgb(255, 60, 60);
  text-align: center;
}
.doubtInputPopup .doubt-container div:nth-child(3) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.doubtInputPopup .doubt-container div:nth-child(3) button {
  width: 45%;
}
.doubtInputPopup .doubt-container div:nth-child(3) button:nth-child(2) {
  padding: 0.55rem;
}
.doubtInputPopup .doubt-container textarea {
  overflow: scroll;
  padding: 1rem;
  border-radius: 3px;
  width: 100%;
  height: 100%;
  background-color: #444;
  border: none;
  color: white;
  resize: none;
  font-size: 1rem;
  font-family: "NeueMachina";
}
.doubtInputPopup .doubt-container button {
  margin-top: 1rem;
  display: block;
  margin: auto;
}

#toast {
  position: fixed;
  top: 5rem;
  z-index: 9999;
  right: -100%;
  background-color: #333;
  color: #fff;
  padding: 10px 20px;
  border-radius: 8px;
  display: inline-block;
  opacity: 0;
  transition: opacity 0.5s ease-in-out, right 0.5s ease-in-out;
}
#toast.visible {
  right: 20px;
  opacity: 1;
}

@media (min-width: 900px) {
  #main:has(.currentPlaying[data-type=video]) .page1 .heroArea .metaData .courseDetail .right .chat {
    display: flex;
  }
}
#main {
  width: 100%;
  height: 100%;
  transition: all 0.85s cubic-bezier(0.23, 1, 0.32, 1);
  background-color: var(--background-color) !important;
}
#main .page1 {
  width: 100%;
  height: 100%;
  padding: 2rem;
  padding-bottom: 0;
  padding-top: 10.5vh;
  display: flex;
  justify-content: space-between;
  transition: 0.2s;
}
#main .page1 .heroArea {
  height: 95%;
  width: 75%;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  overflow-y: scroll;
}
#main .page1 .heroArea .notForMobilePopup {
  width: 100%;
  transition: 0.1s;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
  -webkit-backdrop-filter: blur(1px);
          backdrop-filter: blur(1px);
  background-color: rgba(0, 0, 0, 0.546);
}
#main .page1 .heroArea .notForMobilePopup .overlay {
  transition: 0.3s;
  padding-top: 2rem;
  position: absolute;
  background-color: var(--ui-element-secondary-color);
  bottom: -100%;
  padding-bottom: 0.5rem;
  border-radius: 5px;
  left: 0;
  transform: translateX(-50%);
  left: 50%;
  width: calc(100% - 7vw);
}
#main .page1 .heroArea .notForMobilePopup .image {
  padding: 0 2rem;
  width: 17rem;
  height: 17rem;
  margin: auto;
}
#main .page1 .heroArea .notForMobilePopup .image img {
  pointer-events: none;
  transform: scale(2.5);
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
#main .page1 .heroArea .notForMobilePopup .content {
  padding: 0 2rem;
  margin: 1rem 0;
  text-align: center;
}
#main .page1 .heroArea .notForMobilePopup .content h2 {
  font-size: 1.3rem;
}
#main .page1 .heroArea .notForMobilePopup .button {
  display: flex;
  justify-content: space-between;
  padding: 0 2rem;
  padding-top: 1rem;
  align-items: center;
  border-top: 1px solid rgba(128, 128, 128, 0.284);
}
#main .page1 .heroArea .notForMobilePopup .button button {
  padding: 0.3rem 2rem 0.5rem 2rem;
}
#main .page1 .heroArea .notForMobilePopup .button button:nth-child(1) {
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
  background-color: transparent;
}
#main .page1 .heroArea .notForMobilePopup .button button:nth-child(2) {
  color: black;
  font-weight: 500;
  background-color: var(--accent-color);
}
#main .page1 .heroArea #heroAreaMCQ-wrap {
  width: 100%;
  height: 100%;
}
#main .page1 .heroArea #heroAreaMCQ-wrap iframe {
  width: 100%;
  height: 100%;
}
#main .page1 .heroArea #heroAreaCode-wrap {
  width: 100%;
  height: 100%;
}
#main .page1 .heroArea #heroAreaCode-wrap iframe {
  width: 100%;
  height: 100%;
}
#main .page1 .heroArea #heroAreaPDF-wrap {
  width: 100%;
  height: 95%;
}
#main .page1 .heroArea #heroAreaPDF-wrap iframe {
  width: 100%;
  height: 95%;
}
#main .page1 .heroArea #heroAreaPDF-wrap .title {
  background-color: var(--Shark);
  width: -moz-fit-content;
  width: fit-content;
  padding: 0.3rem 1rem;
  border-radius: 3px;
  margin-bottom: 0.5rem;
}
#main .page1 .heroArea #heroAreaPDF-wrap div {
  display: flex;
  justify-content: right;
  align-items: center;
  gap: 1rem;
}
#main .page1 .heroArea #heroAreaPDF-wrap div .download {
  background-color: var(--fuscous-gray);
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
}
#main .page1 .heroArea #heroAreaPDF-wrap div .nextBtn {
  background-color: var(--caribbean-green);
  padding: 0.2rem 1rem;
  border-radius: 3px;
}
#main .page1 .heroArea::-webkit-scrollbar {
  display: none;
}
#main .page1 .heroArea > * {
  flex-shrink: 0;
}
#main .page1 .heroArea .video {
  width: 100%;
  aspect-ratio: 16/9;
  border-radius: 7px;
}
#main .page1 .heroArea .metaData {
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.9rem;
}
#main .page1 .heroArea .metaData .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#main .page1 .heroArea .metaData .title h3,
#main .page1 .heroArea .metaData .title h2,
#main .page1 .heroArea .metaData .title h1 {
  font-weight: 500;
}
#main .page1 .heroArea .metaData .title .buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
  padding-right: 1px;
  gap: 0.75rem;
}
#main .page1 .heroArea .metaData .title .buttons a,
#main .page1 .heroArea .metaData .title .buttons button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  padding: 7px 1.4rem;
  text-decoration: none;
  border-radius: 4px;
}
#main .page1 .heroArea .metaData .title .buttons a span,
#main .page1 .heroArea .metaData .title .buttons a i,
#main .page1 .heroArea .metaData .title .buttons button span,
#main .page1 .heroArea .metaData .title .buttons button i {
  font-size: 1rem;
  color: var(--text-color);
}
#main .page1 .heroArea .metaData .courseDetail {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  margin-top: 0.5rem;
}
#main .page1 .heroArea .metaData .courseDetail .left {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}
#main .page1 .heroArea .metaData .courseDetail .left img {
  border-radius: 50%;
  height: 3rem;
  width: 3rem;
  -o-object-fit: cover;
     object-fit: cover;
}
#main .page1 .heroArea .metaData .courseDetail .text h4 {
  font-weight: 600;
  white-space: nowrap;
  margin-bottom: -0.2rem;
}
#main .page1 .heroArea .metaData .courseDetail .text small {
  white-space: nowrap;
  font-weight: 600;
  opacity: 0.6;
}
#main .page1 .heroArea .metaData .courseDetail .right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  overflow-x: auto;
  width: 100%;
  gap: 0.5rem;
}
#main .page1 .heroArea .metaData .courseDetail .right::-webkit-scrollbar {
  display: none;
}
#main .page1 .heroArea .metaData .courseDetail .right .like {
  display: flex;
  align-items: center;
  justify-content: center;
}
#main .page1 .heroArea .metaData .courseDetail .right .like i {
  display: initial;
  font-size: 1.2rem;
}
#main .page1 .heroArea .metaData .courseDetail .right .like i.active {
  display: none;
}
#main .page1 .heroArea .metaData .courseDetail .right .like.liked .likes i {
  display: none;
}
#main .page1 .heroArea .metaData .courseDetail .right .like.liked .likes i.active {
  display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .right .like.liked .dislikes i {
  display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .right .like.liked .dislikes i.active {
  display: none;
}
#main .page1 .heroArea .metaData .courseDetail .right .like.disLiked .likes i {
  display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .right .like.disLiked .likes i.active {
  display: none;
}
#main .page1 .heroArea .metaData .courseDetail .right .like.disLiked .dislikes i {
  display: none;
}
#main .page1 .heroArea .metaData .courseDetail .right .like.disLiked .dislikes i.active {
  display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .right .like .likes {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid rgba(0, 0, 0, 0.158);
  padding: 0.3rem 0.7rem;
  background-color: var(--ui-element-color);
  border-top-left-radius: 1.3rem;
  border-bottom-left-radius: 1.3rem;
  gap: 0.3rem;
  height: 2.1rem;
  font-size: 0.9rem;
  transition: all 0.3s linear;
}
#main .page1 .heroArea .metaData .courseDetail .right .like .likes:active {
  scale: 0.9;
}
#main .page1 .heroArea .metaData .courseDetail .right .like .likes small {
  font-weight: 300;
}
#main .page1 .heroArea .metaData .courseDetail .right .like .dislikes {
  transition: all 0.3s linear;
  cursor: pointer;
  font-size: 0.85rem;
  height: 2.1rem;
  border-top-right-radius: 1.3rem;
  border-bottom-right-radius: 1.3rem;
  padding: 0.3rem 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ui-element-color);
}
#main .page1 .heroArea .metaData .courseDetail .right .like .dislikes:active {
  scale: 0.9;
}
#main .page1 .heroArea .metaData .courseDetail .right .share,
#main .page1 .heroArea .metaData .courseDetail .right .comment {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ui-element-color);
  gap: 0.3rem;
  height: 2.1rem;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s linear;
  text-decoration: none;
  color: var(--text-color);
  padding: 0.3rem 0.7rem;
  border-radius: 1.3rem;
}
#main .page1 .heroArea .metaData .courseDetail .right .share i,
#main .page1 .heroArea .metaData .courseDetail .right .comment i {
  font-size: 1.3rem;
  font-weight: 100;
}
#main .page1 .heroArea .metaData .courseDetail .right .comment {
  display: none;
  padding: 0.3rem;
}
#main .page1 .heroArea .metaData .courseDetail .right .poll {
  padding: 0.3rem 0.45rem;
}
#main .page1 .heroArea .metaData .courseDetail .right .btn {
  border-radius: 1.3rem;
  color: var(--white);
  padding: 0.35em 1.2em;
  padding-bottom: 0.4em;
  font-size: 0.9rem;
  display: flex;
  white-space: nowrap;
  align-items: center;
  gap: 0.25em;
}
#main .page1 .heroArea .metaData .courseDetail .right .btn * {
  color: inherit;
}
#main .page1 .heroArea .metaData .courseDetail .right .btn i {
  display: flex;
  margin-top: 0.15em;
}
#main .page1 .heroArea .metaData .courseDetail .right .chat {
  padding: 0.35rem 1.2rem;
  border-radius: 1.3rem;
  background: linear-gradient(to right, #3e006a, #97051e 125%);
  color: var(--white);
  display: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  gap: 0.45rem;
  font-size: 0.9rem;
}
#main .page1 .heroArea .metaData .courseDetail .right .chat span {
  color: var(--white);
}
#main .page1 .heroArea .metaData .courseDetail .right .chat i {
  color: var(--white);
  font-size: 1.3em;
}
#main .page1 .heroArea .metaData .courseDetail .right .discord {
  padding: 0.35rem 1.2rem;
  border-radius: 1.3rem;
  background-color: var(--discord);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  gap: 0.45rem;
  font-size: 0.9rem;
}
#main .page1 .heroArea .metaData .courseDetail .right .discord span {
  color: var(--white);
}
#main .page1 .heroArea .metaData .courseDetail .right .discord i {
  color: var(--white);
  font-size: 1.3em;
}
#main .page1 .heroArea .metaData .courseDetail .right .share:hover,
#main .page1 .heroArea .metaData .courseDetail .right .likes:hover,
#main .page1 .heroArea .metaData .courseDetail .right .dislikes:hover {
  transition: all 0.3s linear;
  background-color: var(--ui-element-highlight-color) !important;
}
#main .page1 .heroArea .links {
  width: 100%;
  background-color: var(--secondaryLight);
  padding: 1rem;
  border-radius: 7px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
#main .page1 .heroArea .links p {
  margin-bottom: 0.4rem;
  font-weight: 500;
  font-size: 0.85rem;
}
#main .page1 .heroArea .links .link small {
  font-weight: 600;
}
#main .page1 .heroArea .links .link small a {
  color: var(--text-color);
}
#main .page1 .heroArea .description {
  background-color: var(--ui-element-color);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  position: relative;
  border-radius: 7px;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  padding-bottom: 1.5rem;
  flex-shrink: 0;
  cursor: pointer;
}
#main .page1 .heroArea .description * {
  user-select: text;
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}
#main .page1 .heroArea .description small {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color);
}
#main .page1 .heroArea .description #descriptionTextWrapper {
  margin-top: 0.5rem;
  display: flex;
  min-height: 1.2rem;
  height: 1.2rem;
  overflow: hidden;
}
#main .page1 .heroArea .description #descriptionTextWrapper #links {
  margin-bottom: 10px;
}
#main .page1 .heroArea .description #descriptionTextWrapper #links a {
  color: var(--link);
}
#main .page1 .heroArea .description #descriptionTextWrapper p {
  font-weight: 400;
  line-height: 1.2rem;
  font-size: 0.9rem;
}
#main .page1 .heroArea .description .showMore {
  cursor: pointer;
  pointer-events: none;
  height: 1rem;
  width: 100%;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  color: var(--text-color);
  opacity: 0.6;
  transition: all 0.3s linear;
}
#main .page1 .heroArea .description .showMore p {
  font-weight: 400;
  line-height: 1.1;
  font-size: 0.85rem;
}
#main .page1 .heroArea .comments {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 0.5rem;
  overflow-y: hidden;
}
#main .page1 .heroArea .comments .noMoreComments {
  font-weight: 600;
  font-size: 1.5rem;
  text-align: center;
  display: block;
}
#main .page1 .heroArea .comments .addComment,
#main .page1 .heroArea .comments .addCommentDummy {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}
#main .page1 .heroArea .comments .addComment .top,
#main .page1 .heroArea .comments .addCommentDummy .top {
  display: flex;
  justify-content: space-between;
}
#main .page1 .heroArea .comments .addComment .top .arrow,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow {
  display: none;
}
#main .page1 .heroArea .comments .addComment .top .arrow.close .ri-arrow-down-s-line,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow.close .ri-arrow-down-s-line {
  display: none;
}
#main .page1 .heroArea .comments .addComment .top .arrow.close .ri-arrow-up-s-line,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow.close .ri-arrow-up-s-line {
  display: initial;
}
#main .page1 .heroArea .comments .addComment .top .arrow.open .ri-arrow-down-s-line,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow.open .ri-arrow-down-s-line {
  display: initial;
}
#main .page1 .heroArea .comments .addComment .top .arrow.open .ri-arrow-up-s-line,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow.open .ri-arrow-up-s-line {
  display: none;
}
#main .page1 .heroArea .comments .addComment .bottom,
#main .page1 .heroArea .comments .addCommentDummy .bottom {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.7rem;
}
#main .page1 .heroArea .comments .addComment .bottom .texty,
#main .page1 .heroArea .comments .addCommentDummy .bottom .texty {
  text-wrap: wrap;
  max-width: 100%;
  word-break: break-all;
  cursor: text;
  position: relative;
}
#main .page1 .heroArea .comments .addComment .bottom .texty .mentionUser,
#main .page1 .heroArea .comments .addCommentDummy .bottom .texty .mentionUser {
  padding: 0.2rem 0.5rem;
  margin-left: 0.3rem;
  border-radius: 1rem;
  background-color: var(--sub-text-color);
  word-break: keep-all;
}
#main .page1 .heroArea .comments .addComment .bottom .texty:empty::before,
#main .page1 .heroArea .comments .addCommentDummy .bottom .texty:empty::before {
  content: "Enter comment";
  opacity: 0.5;
  position: absolute;
  transform: translate(0%, -50%);
  left: calc(0.4rem + 0.5%);
  top: 50%;
}
#main .page1 .heroArea .comments .addComment .bottom .user,
#main .page1 .heroArea .comments .addCommentDummy .bottom .user {
  display: flex;
  align-self: flex-start;
  padding-top: 0.3rem;
}
#main .page1 .heroArea .comments .addComment .bottom .user img,
#main .page1 .heroArea .comments .addCommentDummy .bottom .user img {
  height: 1.5rem;
  width: 1.5rem;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}
#main .page1 .heroArea .comments .addComment .bottom textarea,
#main .page1 .heroArea .comments .addComment .bottom .texty,
#main .page1 .heroArea .comments .addCommentDummy .bottom textarea,
#main .page1 .heroArea .comments .addCommentDummy .bottom .texty {
  width: 100%;
  padding: 0.8rem 0.4rem;
  resize: none;
  background-color: transparent;
  border: none;
  border-bottom: 1px solid var(--primaryDark);
  opacity: 0.8;
  max-width: 100%;
}
#main .page1 .heroArea .comments .addComment .bottom textarea::-webkit-scrollbar,
#main .page1 .heroArea .comments .addComment .bottom .texty::-webkit-scrollbar,
#main .page1 .heroArea .comments .addCommentDummy .bottom textarea::-webkit-scrollbar,
#main .page1 .heroArea .comments .addCommentDummy .bottom .texty::-webkit-scrollbar {
  display: none;
}
#main .page1 .heroArea .comments .addComment:has(textarea:focus) .actions, #main .page1 .heroArea .comments .addComment:has(.texty) .actions,
#main .page1 .heroArea .comments .addCommentDummy:has(textarea:focus) .actions,
#main .page1 .heroArea .comments .addCommentDummy:has(.texty) .actions {
  display: flex;
}
#main .page1 .heroArea .comments .addComment .post,
#main .page1 .heroArea .comments .addCommentDummy .post {
  opacity: 0;
  pointer-events: none;
  transition: all 0.1s linear;
}
#main .page1 .heroArea .comments .addComment .actions,
#main .page1 .heroArea .comments .addCommentDummy .actions {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 1em;
  display: none;
}
#main .page1 .heroArea .comments .addComment .actions .action,
#main .page1 .heroArea .comments .addCommentDummy .actions .action {
  width: -moz-fit-content;
  width: fit-content;
  padding: 0.7rem 1rem;
  background-color: var(--sub-text-color);
  opacity: 0.7;
  border-radius: 1.7rem;
  cursor: pointer;
}
#main .page1 .heroArea .comments .addComment .actions .addcomment,
#main .page1 .heroArea .comments .addCommentDummy .actions .addcomment {
  background-color: transparent;
  pointer-events: none;
  cursor: pointer;
}
#main .page1 .heroArea .comments .addComment.valid,
#main .page1 .heroArea .comments .addCommentDummy.valid {
  display: flex !important;
}
#main .page1 .heroArea .comments .addComment.valid .actions,
#main .page1 .heroArea .comments .addCommentDummy.valid .actions {
  display: flex !important;
}
#main .page1 .heroArea .comments .addComment.valid .actions .addcomment,
#main .page1 .heroArea .comments .addCommentDummy.valid .actions .addcomment {
  background-color: var(--accent-color);
  color: var(--text-color);
  pointer-events: initial;
}
#main .page1 .heroArea .comments .replyText {
  padding: 0.3rem 1rem;
  color: var(--accent-color);
  cursor: pointer;
  font-weight: 600;
  background-color: transparent;
  transition: all 0.3s linear;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: 1rem;
  margin-bottom: 1rem;
}
#main .page1 .heroArea .comments .replyText:hover {
  background-color: var(--sub-text-color);
}
#main .page1 .heroArea .comments .loadMore {
  margin: auto;
  margin-top: 20px;
  display: block;
}
#main .page1 .heroArea .comments .comment {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.9rem;
}
#main .page1 .heroArea .comments .comment * {
  user-select: text !important;
  -webkit-user-select: text !important;
  -khtml-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}
#main .page1 .heroArea .comments .comment:last-child {
  margin-bottom: 0px;
}
#main .page1 .heroArea .comments .comment .left img {
  height: 1.5rem;
  width: 1.5rem;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}
#main .page1 .heroArea .comments .comment .right {
  width: 100%;
}
#main .page1 .heroArea .comments .comment .right .metaData {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  gap: 0.2rem;
  align-items: flex-end;
  margin-bottom: 0.2rem;
  margin-top: 0;
}
#main .page1 .heroArea .comments .comment .right .metaData p {
  width: -moz-fit-content;
  width: fit-content;
  display: inline-block;
  opacity: 0.7;
  font-size: 0.8rem;
}
#main .page1 .heroArea .comments .comment .right .metaData .user {
  font-weight: 500;
  font-size: 1rem;
}
#main .page1 .heroArea .comments .comment .right .metaData .time {
  opacity: 0.4;
}
#main .page1 .heroArea .comments .comment .right p {
  font-size: 0.95rem;
}
#main .page1 .heroArea .comments .comment .right .bottom {
  padding: 1rem 0.5rem;
  padding-bottom: 0.3rem;
  display: flex;
  gap: 1.2rem;
}
#main .page1 .heroArea .comments .comment .right .bottom .likeDislike {
  display: flex;
  gap: 0.5rem;
}
#main .page1 .heroArea .comments .comment .right .bottom .reply,
#main .page1 .heroArea .comments .comment .right .bottom .likes,
#main .page1 .heroArea .comments .comment .right .bottom .dislikes {
  padding: 0.3rem 1rem;
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s linear;
}
#main .page1 .heroArea .comments .comment .right .bottom .reply:hover,
#main .page1 .heroArea .comments .comment .right .bottom .likes:hover,
#main .page1 .heroArea .comments .comment .right .bottom .dislikes:hover {
  background-color: var(--ternary);
}
#main .page1 .heroArea .comments .comment .right .bottom .likes {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}
#main .page1 .heroArea .comments .comment .right .replies {
  width: 100%;
}
#main .page1 .heroArea .comments .comment .right .allReplies {
  width: 100%;
}
#main .page1 .heroArea .comments .comment .right .allReplies .moreReplies {
  margin-bottom: 1rem;
  color: var(--accent-color);
  display: flex;
  cursor: pointer;
  justify-content: left;
  font-weight: 600;
  align-items: center;
}
#main .page1 .heroArea .comments .comment .right .addComment {
  width: 100%;
}
#main .page1 .heroArea .comments .comment .right .addComment .bottom {
  width: 100%;
}
#main .page1 .heroArea .comments .comment .right .addComment .bottom .actions {
  display: flex !important;
}
#main .page1 .heroArea .comments .comment .right .addComment .bottom .actions .action {
  cursor: pointer;
}
#main .page1 .heroArea .comments .comment .delete {
  opacity: 0;
  pointer-events: none;
  transition: all 0.1s linear;
  position: relative;
}
#main .page1 .heroArea .comments .comment .delete .options {
  display: none;
  flex-direction: column;
  position: absolute;
  transform: translate(0%, -50%);
  right: 100%;
  top: 50%;
  background-color: var(--sub-text-color);
  padding: 0.7rem;
  border-radius: 0.3rem;
}
#main .page1 .heroArea .comments .comment .delete:hover .options {
  display: flex;
}
#main .page1 .heroArea .comments .comment:hover .delete {
  opacity: 1;
  pointer-events: initial;
}
#main .page1 .verticalResizer {
  width: 10px;
  height: 95%;
  border-radius: 10px;
  transition: 0.2s;
  margin: 0.1rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: col-resize;
}
#main .page1 .verticalResizer .line {
  background-color: var(--ui-element-ternary-color);
  width: 2px;
  height: 30px;
  transition: 0.1s;
}
#main .page1 .verticalResizer:active .line {
  height: 100%;
  background-color: #227aff;
}
#main .page1 .verticalResizer:hover .line {
  height: 100%;
  background-color: #227aff;
}
#main .page1 .chapters {
  min-width: 40px;
  width: 30%;
  max-width: 35%;
  display: flex;
  height: 95%;
  flex-direction: column;
  gap: 0.7rem;
  background-color: var(--ui-element-secondary-color);
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
}
#main .page1 .chapters .progressBar {
  width: 100%;
}
#main .page1 .chapters .progressBar * {
  font-family: "NeueMachina";
}
#main .page1 .chapters .progressBar .progress {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  font-size: 1.1rem;
  padding-bottom: 0.5rem;
}
#main .page1 .chapters .progressBar .progress h1 {
  white-space: nowrap;
  font-weight: 300;
  font-size: 1rem;
}
#main .page1 .chapters .progressBar .progress p {
  color: var(--accent-color);
  white-space: nowrap;
  font-weight: 500;
  font-size: 1em;
}
#main .page1 .chapters.onlyNav {
  width: 40px;
  background-color: var(--ui-element-color);
}
#main .page1 .chapters.onlyNav .title {
  display: flex;
  align-items: center;
  transform: rotate(90deg);
  white-space: nowrap;
  /* Prevent text from wrapping */
  margin: 5% 0;
  /* Adjust margin to ensure no overlap */
}
#main .page1 .chapters.onlyNav .modules,
#main .page1 .chapters.onlyNav .top-all-module {
  display: none;
}
#main .page1 .chapters .title {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
  align-items: center;
  font-weight: 500;
  font-size: 1rem;
  background-color: var(--ui-element-color);
  padding: 0.7rem 1rem;
}
#main .page1 .chapters .title .live {
  display: flex;
  gap: 1rem;
  align-items: center;
}
#main .page1 .chapters .title .live.active button {
  display: block;
}
#main .page1 .chapters .title .live.active p {
  display: none;
}
#main .page1 .chapters .title .live span {
  font-weight: 600;
  color: crimson;
}
#main .page1 .chapters .title .live button {
  padding: 0.3rem 1rem;
  display: none;
  background-color: crimson;
}
#main .page1 .chapters .title i {
  color: #227aff;
}
#main .page1 .chapters .top-all-module-live {
  justify-content: center;
  min-width: 22rem;
  align-items: center;
  gap: 0.5rem;
  overflow: hidden;
  padding: 1.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--ui-element-color);
  display: flex;
  flex-direction: column;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
#main .page1 .chapters .top-all-module-live button {
  background-color: #BE524B;
  color: white;
  width: 50%;
}
#main .page1 .chapters .top-all-module-live button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
#main .page1 .chapters .top-all-module-live p {
  font-size: 1.2rem;
  font-weight: 700;
}
#main .page1 .chapters .top-all-module {
  justify-content: center;
  min-width: 22rem;
  overflow: hidden;
  padding: 1.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--ui-element-color);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
#main .page1 .chapters .top-all-module .status-assessments {
  display: flex;
  flex-wrap: nowrap;
  gap: 0.5rem;
}
#main .page1 .chapters .top-all-module .status-assessments .status {
  display: flex;
}
#main .page1 .chapters .top-all-module .status-assessments .status h6 {
  font-size: 1rem;
  margin-left: 0.2rem;
  margin-right: 0.2rem;
  font-weight: 400;
}
#main .page1 .chapters .top-all-module .status-assessments .status p {
  margin-right: 0.2rem;
  font-size: 0.95rem;
}
#main .page1 .chapters .top-all-module .status-assessments .status p span {
  opacity: 0.7;
  font-weight: 300;
  font-size: 0.9rem;
}
#main .page1 .chapters .bar {
  height: 0.2em;
  background-color: var(--boulder);
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
}
#main .page1 .chapters .bar .meter {
  transition: 0.5s;
  all: inherit;
  background-color: var(--accent-color);
}
#main .page1 .chapters .modules {
  min-width: 22rem;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0 1rem;
  padding-bottom: 5vh;
}
#main .page1 .chapters .modules::-webkit-scrollbar {
  display: none;
}
#main .page1 .chapters .modules .moduleWrapper,
#main .page1 .chapters .modules .accordian {
  margin-bottom: 0.55rem;
}
#main .page1 .chapters .modules .moduleWrapper .comingsoon,
#main .page1 .chapters .modules .accordian .comingsoon {
  white-space: nowrap;
  padding: 0.1rem 0.3rem;
  font-weight: 400;
  opacity: 0.8;
  border-radius: 3px;
  font-size: 0.9rem;
  background-color: var();
  position: relative;
  margin-left: 1.5em;
}
#main .page1 .chapters .modules .moduleWrapper .comingsoon::after,
#main .page1 .chapters .modules .accordian .comingsoon::after {
  content: "\eecd";
  font-family: remixicon !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  position: absolute;
  transform: translate(0%, -50%);
  left: -1em;
  top: 50%;
}
#main .page1 .chapters .modules .moduleWrapper .comingsoon span,
#main .page1 .chapters .modules .accordian .comingsoon span {
  font-size: 0.8em;
  opacity: 0.7;
}
#main .page1 .chapters .modules .moduleWrapper:has(> label > input[type=checkbox]:checked) > .panel,
#main .page1 .chapters .modules .accordian:has(> label > input[type=checkbox]:checked) > .panel {
  grid-template-rows: 1fr !important;
  padding: 1rem 0;
  padding-bottom: 0;
}
#main .page1 .chapters .modules .moduleWrapper:has(> label > input[type=checkbox]:checked) > label > button,
#main .page1 .chapters .modules .accordian:has(> label > input[type=checkbox]:checked) > label > button {
  transform: rotate(180deg);
}
#main .page1 .chapters .modules .moduleWrapper > .panel,
#main .page1 .chapters .modules .accordian > .panel {
  display: grid;
  grid-template-rows: 0fr;
  transition: all 0.3s linear;
  padding-bottom: 0;
}
#main .page1 .chapters .modules .moduleWrapper > .panel .wrapperContent,
#main .page1 .chapters .modules .moduleWrapper > .panel .lectureContent,
#main .page1 .chapters .modules .accordian > .panel .wrapperContent,
#main .page1 .chapters .modules .accordian > .panel .lectureContent {
  overflow: hidden;
}
#main .page1 .chapters .modules .moduleWrapper > .title,
#main .page1 .chapters .modules .accordian > .title {
  cursor: pointer;
  padding: 0.8rem 0.7rem;
  background-color: var(--primaryDark);
  overflow: hidden;
  border-radius: 4px;
  width: 100%;
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  top: 0%;
  position: sticky;
  z-index: 99;
}
#main .page1 .chapters .modules .moduleWrapper > .title .name,
#main .page1 .chapters .modules .accordian > .title .name {
  width: 100%;
  font-weight: 500;
  font-size: 1rem;
  color: var(--cod-gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#main .page1 .chapters .modules .moduleWrapper > .title .name .comingSoonModuleWrapper,
#main .page1 .chapters .modules .accordian > .title .name .comingSoonModuleWrapper {
  padding: 0;
  margin: 0;
  border-radius: 2px;
  background: var(--accent-color);
  padding: 3px 7px;
  color: var(--Wild-sand);
  font-size: 0.75rem;
  font-weight: 500;
}
#main .page1 .chapters .modules .moduleWrapper > .title .name .newLabel,
#main .page1 .chapters .modules .accordian > .title .name .newLabel {
  padding: 0;
  margin: 0;
  border-radius: 5px;
  background: var(--accent-color);
  color: var(--white);
  padding: 0.7px 7px;
  font-size: 0.8rem;
  font-weight: 500;
}
#main .page1 .chapters .modules .moduleWrapper > .title button,
#main .page1 .chapters .modules .accordian > .title button {
  background-color: transparent;
  font-size: 1.2em;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.8rem;
  gap: 8px;
  transform: rotate(90deg);
  transition: all 0.3s linear;
}
#main .page1 .chapters .modules .moduleWrapper > .title button i,
#main .page1 .chapters .modules .accordian > .title button i {
  cursor: pointer;
}
#main .page1 .chapters .modules .moduleWrapper .dropdownButton,
#main .page1 .chapters .modules .accordian .dropdownButton {
  pointer-events: none !important;
}
#main .page1 .chapters .modules .moduleWrapper .module,
#main .page1 .chapters .modules .accordian .module {
  width: 100%;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .comingsoon,
#main .page1 .chapters .modules .accordian .module .comingsoon {
  white-space: nowrap;
  padding: 0.1rem 0.3rem;
  font-weight: 400;
  opacity: 0.8;
  border-radius: 3px;
  font-size: 0.9rem;
  background-color: var();
  position: relative;
  margin-left: 1.5em;
}
#main .page1 .chapters .modules .moduleWrapper .module .comingsoon::after,
#main .page1 .chapters .modules .accordian .module .comingsoon::after {
  content: "\eecd";
  font-family: remixicon !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  position: absolute;
  transform: translate(0%, -50%);
  left: -1em;
  top: 50%;
}
#main .page1 .chapters .modules .moduleWrapper .module .comingsoon span,
#main .page1 .chapters .modules .accordian .module .comingsoon span {
  font-size: 0.8em;
  opacity: 0.7;
}
#main .page1 .chapters .modules .moduleWrapper .module .addModule,
#main .page1 .chapters .modules .accordian .module .addModule {
  background-color: rgb(0, 0, 0) !important;
  text-align: center;
  color: white;
  justify-content: center !important;
}
#main .page1 .chapters .modules .moduleWrapper .module .title,
#main .page1 .chapters .modules .accordian .module .title {
  cursor: pointer;
  padding: 0.7rem 0.7rem;
  background-color: var(--ui-element-color);
  border-radius: 4px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0%;
  z-index: 99;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .name,
#main .page1 .chapters .modules .accordian .module .title .name {
  font-weight: 500;
  font-size: 0.9rem;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .name .newLabel,
#main .page1 .chapters .modules .accordian .module .title .name .newLabel {
  font-size: 0.8rem;
  padding: 0;
  margin: 0;
  border-radius: 5px;
  background: var(--accent-color);
  padding: 0.7px 7px;
  color: var(--white);
  font-weight: 500;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .name input,
#main .page1 .chapters .modules .accordian .module .title .name input {
  background-color: transparent;
  border: none;
  outline: none;
  color: var(--text-color);
  opacity: 1;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures,
#main .page1 .chapters .modules .accordian .module .lectures {
  padding-right: 0;
  width: 100%;
  min-height: -moz-fit-content;
  min-height: fit-content;
  transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
  overflow: hidden;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .currentPlaying,
#main .page1 .chapters .modules .accordian .module .lectures .currentPlaying {
  background-color: var(--ui-element-color);
  pointer-events: none;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .currentPlaying .left .thumbnail,
#main .page1 .chapters .modules .accordian .module .lectures .currentPlaying .left .thumbnail {
  position: relative;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .currentPlaying .left .thumbnail::before,
#main .page1 .chapters .modules .accordian .module .lectures .currentPlaying .left .thumbnail::before {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
  content: "\efd6";
  z-index: 2;
  font-size: 2.5rem;
  color: var(--white);
  font-family: remixicon !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .currentPlaying .left .thumbnail img,
#main .page1 .chapters .modules .accordian .module .lectures .currentPlaying .left .thumbnail img {
  opacity: 0.8 !important;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures.close,
#main .page1 .chapters .modules .accordian .module .lectures.close {
  height: 0 !important;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .comingSoon .left,
#main .page1 .chapters .modules .accordian .module .lectures .comingSoon .left {
  width: 8rem !important;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3px;
  height: 100% !important;
  background-color: var(--white);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .comingSoon .left .thumbnail h2,
#main .page1 .chapters .modules .accordian .module .lectures .comingSoon .left .thumbnail h2 {
  font-weight: 500;
  font-size: 0.9rem;
  color: royalblue;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .comingSoon .left .thumbnail img,
#main .page1 .chapters .modules .accordian .module .lectures .comingSoon .left .thumbnail img {
  all: revert-layer !important;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .comingSoon .rgiht,
#main .page1 .chapters .modules .accordian .module .lectures .comingSoon .rgiht {
  margin-left: 4px;
  display: flex;
  flex-direction: column;
  gap: 0.1rem !important;
  height: 3.5rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .comingSoon .rgiht .lectureTitle,
#main .page1 .chapters .modules .accordian .module .lectures .comingSoon .rgiht .lectureTitle {
  display: flex;
  justify-content: left;
  gap: 5px;
  align-items: center;
  height: -moz-fit-content !important;
  height: fit-content !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  line-height: normal !important;
  margin-bottom: 2px;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .comingSoon .rgiht .lectureDate,
#main .page1 .chapters .modules .accordian .module .lectures .comingSoon .rgiht .lectureDate {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.673);
  font-size: 0.85rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture,
#main .page1 .chapters .modules .accordian .module .lectures .lecture {
  position: relative;
  padding: 0.65rem;
  padding-left: 1rem;
  width: 100%;
  display: flex;
  gap: 0.7rem;
  position: relative;
  transition: all 0.3s linear;
  cursor: pointer;
  border-radius: 0.25rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.watched,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.watched {
  padding-right: 1.5rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.watched::before,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.watched::before {
  content: "";
  position: absolute;
  right: 2%;
  top: 7%;
  height: 15px;
  width: 15px;
  background-color: var(--accent-color);
  border-radius: 50%;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 35.6 35.6'%3E%3Ccircle fill='%2310a37f' cx='17.8' cy='17.8' r='17.8'%3E%3C/circle%3E%3Ccircle fill='%2310a37f' style='stroke: %23fff; stroke-miterlimit: 10;stroke-width: 2px;' cx='17.8' cy='17.8' r='14.37'%3E%3C/circle%3E%3Cpolyline fill='none' style='stroke: %23fff; stroke-miterlimit: 10;stroke-width: 2px;' points='11.78 18.12 15.55 22.23 25.17 12.87'%3E%3C/polyline%3E%3C/svg%3E");
  z-index: 98;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.left,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.left {
  padding-right: 1.5rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.left::before,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.left::before {
  content: "";
  position: absolute;
  right: 2%;
  top: 7%;
  height: 5px;
  width: 5px;
  background-color: rgb(255, 255, 32) !important;
  border-radius: 50%;
  z-index: 98;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.wrong,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.wrong {
  padding-right: 1.5rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.wrong::before,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.wrong::before {
  content: "";
  position: absolute;
  right: 2%;
  top: 7%;
  height: 5px;
  width: 5px;
  background-color: rgb(255, 0, 0);
  border-radius: 50%;
  z-index: 98;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .newLabel,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .newLabel {
  width: 7px;
  height: 7px;
  background-color: var(--accent-color);
  border-radius: 50%;
  display: inline-block;
  margin-left: 2px;
  margin-bottom: 1px;
  transform: translate(-50%, -50%);
  top: 50%;
  position: absolute;
  z-index: 9;
  left: 2px;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.lock,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.lock {
  cursor: default;
  background-color: var(--sub-text-color);
  position: relative;
  margin-bottom: 0.25rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.lock::before,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.lock::before {
  content: "\eecc";
  font-family: remixicon !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
  z-index: 25;
  color: var(--white);
  font-size: 3.5rem;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(24, 24, 24, 0);
  -webkit-backdrop-filter: blur(1px);
          backdrop-filter: blur(1px);
  border-radius: 0.25rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.lock .rgiht,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.lock .rgiht {
  opacity: 0.5;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture:active,
#main .page1 .chapters .modules .accordian .module .lectures .lecture:active {
  scale: 0.98;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.completed::before,
#main .page1 .chapters .modules .accordian .module .lectures .lecture.completed::before {
  content: "\eb80";
  font-family: "remixicon" !important;
  font-style: normal;
  position: absolute;
  top: 4%;
  left: 0.7%;
  z-index: 98;
  color: var(--accent-color);
  border-radius: 50%;
  background: radial-gradient(#fff, #fff, transparent, transparent);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left {
  background-color: rgb(12, 12, 12);
  width: 8rem;
  height: 4.5rem;
  border-radius: 0.25rem;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left.static,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left.static {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--gray-950);
  font-size: 1.8rem;
  font-weight: 700;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left .thumbnail {
  all: inherit;
  position: relative;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail img,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left .thumbnail img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.3s linear;
  -o-object-position: center;
     object-position: center;
  transform-origin: center;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail img:hover,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left .thumbnail img:hover {
  transform: scale(1.05);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail .duration,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left .thumbnail .duration {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  right: 2%;
  bottom: 2%;
  background-color: var(--gray-950);
  padding: 0.1em 0.4em;
  border-radius: 5px;
  font-size: 1.2rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail .duration small,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left .thumbnail .duration small {
  color: var(--white);
  font-size: 0.5em;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail .thumbnailProgress,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left .thumbnail .thumbnailProgress {
  position: absolute;
  left: 0%;
  bottom: 0%;
  height: 3px;
  width: 100%;
  background-color: var(--sub-text-color);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail .thumbnailProgress .meter,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .left .thumbnail .thumbnailProgress .meter {
  all: inherit;
  position: relative;
  background-color: var(--accent-color);
  width: 80%;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht {
  height: -moz-fit-content !important;
  height: fit-content !important;
  display: flex;
  flex-direction: column;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht p:nth-child(2),
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht p:nth-child(2) {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: -moz-fit-content;
  width: fit-content;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-top: 1rem;
  background-color: var(--ternary-gray-color);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht {
  height: 4rem;
  width: calc(100% - 8.7rem);
  display: flex;
  flex-direction: column;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .tags,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht .tags {
  display: flex;
  align-items: center;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .tags .tag,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht .tags .tag {
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  color: var(--accent-color);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .tags .tag.code,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht .tags .tag.code {
  background-color: var(--error-color);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .tags .tag.assesment,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht .tags .tag.assesment {
  background-color: var(--accent-color);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .lectureTitle,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht .lectureTitle {
  padding-top: 3px;
  font-size: 0.9rem;
  font-weight: 500;
  text-overflow: ellipsis;
  overflow: hidden;
  color: var(--cod-gray-200);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .lectureTitle input,
#main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht .lectureTitle input {
  width: 100%;
  background-color: var(--sub-text-color);
  border: none;
  margin-bottom: 3px;
  border-radius: 5px;
  padding: 5px;
  outline: none;
  color: black;
  opacity: 1;
}
#main .page1 .chapters .modules .moduleWrapper.comingSoon .title h1,
#main .page1 .chapters .modules .accordian.comingSoon .title h1 {
  position: relative;
  opacity: 0.4;
}
#main .page1 .chapters .modules .moduleWrapper.comingSoon .title h1::after,
#main .page1 .chapters .modules .accordian.comingSoon .title h1::after {
  height: 100%;
  width: 100%;
  background-color: rgba(34, 34, 34, 0.279);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
#main .page1 .chapters .modules .moduleWrapper > .title {
  z-index: 100;
}
#main .page1 .chapters .modules .accordian > .title {
  position: sticky;
  top: 0;
  cursor: pointer;
}
#main .page1 .chapters .chat-screen {
  position: absolute;
  display: flex;
  padding: 0.7em;
  border-radius: 0.5rem;
  overflow: hidden;
  height: 80%;
  width: 100%;
  background-color: rgba(51, 51, 51, 0.984);
  bottom: 0;
  left: 0;
  z-index: 990999;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  display: none;
}
#main .page1 .chapters .chat-screen::after {
  content: "loading...";
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
}
#main .page1 .chapters .chat-screen.loaded::after {
  display: none;
}
#main .page1 .chapters .chat-screen .controllers {
  display: flex;
  justify-content: flex-end;
}
#main .page1 .chapters .chat-screen .controllers button {
  cursor: pointer;
}
#main .page1 .chapters .chat-screen iframe {
  border: none;
  outline: none;
  width: 100%;
  height: 100%;
}
#main .iframe-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  background: rgba(255, 255, 255, 0);
  /* Transparent */
  z-index: 9999;
  /* Ensure it's above the iframe */
}

@media screen and (min-width: 600px) {
  #main .page1 .heroArea .video {
    position: initial !important;
  }
}
@media (max-width: 850px) {
  .doubtInputPopup .doubt-container {
    width: 80%;
    height: 21rem;
  }
  #main {
    height: initial;
    min-height: 100dvh;
  }
  #main .page1 {
    padding-left: 2vw;
    padding-right: 2vw;
    padding-top: 0;
    flex-direction: column;
    align-items: center;
    overflow-x: hidden;
    gap: 1.2rem;
    width: 100%;
    height: initial;
  }
  #main .page1 .heroArea {
    width: 100vw;
    overflow-x: hidden;
    padding-bottom: 3vh;
    align-items: center;
    padding-left: 4vw;
    padding-right: 4vw;
    padding-top: 4.5rem;
    padding-bottom: 7rem;
    height: 100dvh;
  }
  #main .page1 .heroArea #heroArea-wrap {
    width: 100%;
  }
  #main .page1 .heroArea #heroAreaMCQ-wrap,
  #main .page1 .heroArea #heroAreaPDF-wrap {
    padding-top: 1.5rem;
  }
  #main .page1 .heroArea .video {
    width: 100vw;
    position: sticky;
    top: 0%;
    transform: translateX(-4vw);
    margin-bottom: 0.9rem;
    z-index: 9;
  }
  #main .page1 .heroArea .video iframe {
    border-radius: 0 !important;
  }
  #main .page1 .heroArea .metaData {
    margin-top: 0rem;
  }
  #main .page1 .heroArea .metaData .title {
    align-items: flex-start;
  }
  #main .page1 .heroArea .metaData .title .buttons {
    flex-direction: column-reverse;
    align-items: stretch;
  }
  #main .page1 .heroArea .metaData .title .buttons button {
    padding: 0.3rem 0.6rem;
    width: 100%;
    gap: 0.1rem;
  }
  #main .page1 .heroArea .metaData .title .buttons button i {
    margin-bottom: 0.1rem;
  }
  #main .page1 .heroArea .metaData .courseDetail {
    align-items: flex-start;
    flex-direction: column;
    gap: 1rem;
  }
  #main .page1 .heroArea .metaData .courseDetail .left .text h4 {
    max-width: calc(92vw - 0.5rem - 2.2rem);
    overflow: hidden;
    text-overflow: ellipsis;
  }
  #main .page1 .heroArea .metaData .courseDetail .right {
    justify-content: flex-start;
  }
  #main .page1 .heroArea .metaData .courseDetail .right .comment {
    display: flex;
  }
  #main .page1 .heroArea .metaData h2 {
    font-size: 1.5rem;
  }
  #main .page1 .heroArea .description p {
    font-size: 0.8rem;
  }
  #main .page1 .heroArea #mobileViewCommentMask {
    width: 100%;
    position: static;
    margin-top: 0.9rem;
    height: -moz-fit-content;
    height: fit-content;
    display: flex !important;
    flex-direction: column;
    justify-content: center;
    gap: 0.7rem;
    background-color: var(--sub-text-color) !important;
    border-radius: 0.4rem;
    z-index: 1;
    padding-bottom: 0.45rem;
    padding-top: 0.45rem;
    display: none !important;
  }
  #main .page1 .heroArea #mobileViewCommentMask .comment {
    margin-bottom: 0;
  }
  #main .page1 .heroArea #mobileViewCommentMask .comment .right p {
    font-size: 0.8rem !important;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  #main .page1 .heroArea #mobileViewCommentMask .addCommentDummy {
    margin-bottom: 0;
    gap: 0;
  }
  #main .page1 .heroArea #mobileViewCommentMask .addCommentDummy .bottom textarea,
  #main .page1 .heroArea #mobileViewCommentMask .addCommentDummy .bottom .texty {
    pointer-events: none;
    word-break: break-all;
    cursor: text;
  }
  #main .page1 .heroArea .comments {
    position: fixed;
    top: 100dvh;
    width: 100%;
    background-color: var(--background-color);
    z-index: 99;
    height: calc(100dvh - 56.25vw);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    overflow-y: scroll;
    padding-bottom: 7dvh;
    left: 0;
    will-change: height, width, top, padding;
    padding-top: 0;
  }
  #main .page1 .heroArea .comments .stagger {
    display: flex;
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 3%;
    background-color: rgba(0, 11, 3, 0.178);
    width: 2.5rem !important;
    height: 0.38rem !important;
    min-height: 0.38rem !important;
    border-radius: 0.5rem;
    z-index: 112;
  }
  #main .page1 .heroArea .comments .addComment {
    top: 0;
    gap: 0;
    background-color: var(--background-color);
    z-index: 111;
  }
  #main .page1 .heroArea .comments .addComment .top {
    padding-bottom: 0.5rem;
    padding-top: 1.5rem;
    align-items: center;
  }
  #main .page1 .heroArea .comments .addComment .top .arrow {
    display: initial;
  }
  #main .page1 .heroArea .comments .addComment .top .arrow i {
    height: 0;
    width: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateX(-0.5rem);
    font-size: 1.5rem;
  }
  #main .page1 .heroArea .comments .addComment .actions {
    margin-top: 0.5rem;
  }
  #main .page1 .heroArea .comments .comment .delete {
    opacity: 1;
    pointer-events: initial;
  }
  #main .page1 .verticalResizer {
    display: none;
  }
  #main .page1 .chapters {
    width: 90%;
    max-width: 100%;
    position: fixed;
    border-radius: 0.5rem;
    top: calc(100dvh - 5.5rem);
    background-color: var(--text-color);
    box-shadow: 0 8px 7px -3px rgba(37, 37, 37, 0.176);
    padding-top: 0.2rem;
    z-index: 98;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    padding-bottom: 0;
    height: 3.5rem;
  }
  #main .page1 .chapters > .title {
    display: none;
    border-radius: 0.5rem;
  }
  #main .page1 .chapters > .mobileViewTitle {
    width: 100% !important;
    display: flex !important;
    align-items: flex-start;
    position: relative;
    min-height: 3.5rem !important;
    padding-top: 0.5rem;
  }
  #main .page1 .chapters > .mobileViewTitle .stagger {
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 5%;
    background-color: rgba(0, 11, 3, 0.178);
    width: 2.5rem;
    height: 0.38rem;
    border-radius: 0.5rem;
  }
  #main .page1 .chapters > .mobileViewTitle .text {
    width: calc(100% - 5rem);
  }
  #main .page1 .chapters > .mobileViewTitle .text small,
  #main .page1 .chapters > .mobileViewTitle .text h4,
  #main .page1 .chapters > .mobileViewTitle .text h5 {
    width: 100%;
    position: relative;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    display: flex;
    align-items: center;
  }
  #main .page1 .chapters > .mobileViewTitle .text small .titleCourseName,
  #main .page1 .chapters > .mobileViewTitle .text small .lectureName,
  #main .page1 .chapters > .mobileViewTitle .text h4 .titleCourseName,
  #main .page1 .chapters > .mobileViewTitle .text h4 .lectureName,
  #main .page1 .chapters > .mobileViewTitle .text h5 .titleCourseName,
  #main .page1 .chapters > .mobileViewTitle .text h5 .lectureName {
    display: inline-block;
    max-width: calc(100% - 2rem) !important;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 0.5rem;
  }
  #main .page1 .chapters > .mobileViewTitle i {
    display: inline-block !important;
    font-size: 1.5rem;
    float: right;
  }
}
@media screen and (max-width: 600Px) {
  #main .page1 .heroArea .metaData .title h2 {
    font-size: 1.7rem;
  }
  #main .page1 .heroArea .metaData .courseDetail .left .text h4 {
    font-size: 1.1em;
  }
  #main .page1 .heroArea .metaData .courseDetail .left img {
    height: 3rem;
    width: 3rem;
  }
  #main .page1 .heroArea .metaData .courseDetail .text small {
    font-size: 1.1em;
  }
  #main .page1 .heroArea .metaData .courseDetail .right .comment {
    padding: 0.3em 0.6em;
    font-size: 1em;
  }
  #main .page1 .chapters .modules .moduleWrapper .module h1,
  #main .page1 .chapters .modules .moduleWrapper .title h1 {
    font-size: 1.1em !important;
  }
  #main .page1 .chapters .modules .accordian .module .lectures .lecture .rgiht .lectureTitle {
    font-size: 1rem !important;
  }
}
@media screen and (min-width: 600px) and (max-height: 500px) and (orientation: landscape) {
  #main .page1 {
    padding-top: max(4.5rem, 9vh);
    gap: 0.8rem;
  }
  #main .page1 .chapters {
    width: 35% !important;
    height: 100% !important;
    padding-bottom: 0 !important;
  }
  #main .page1 .chapters .modules .module .lectures .lecture {
    padding-right: 0;
    padding-left: 0;
  }
  #main .page1 .chapters .modules .module .lectures .lecture .left {
    margin-left: 0.4rem;
    width: initial;
  }
  #main .page1 .chapters .modules .module .lectures .lecture .left .thumbnail {
    margin: 0;
  }
  #main .page1 .chapters .modules .module .lectures .lecture .right .lectureTitle {
    font-size: 0.9rem;
  }
  .navCursor {
    display: none;
  }
  nav .right {
    padding-top: 2rem;
  }
}
.container {
  margin: auto;
  display: none;
  pointer-events: none;
  width: 2em;
  height: 2em;
  border: 0.2em solid transparent;
  border-color: var(--sub-text-color);
  border-top-color: var(--accent-color);
  border-radius: 50%;
  animation: loadingspin 1s linear infinite;
}

@keyframes loadingspin {
  100% {
    transform: rotate(360deg);
  }
}/*# sourceMappingURL=gotoclassroom.css.map */