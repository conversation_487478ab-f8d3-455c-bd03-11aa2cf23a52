@charset "UTF-8";
.src-submissions {
  font-family: g<PERSON>roy;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}
.src-submissions__header {
  margin-bottom: 30px;
}
.src-submissions__header h1 {
  color: #2d3748;
  margin-bottom: 10px;
  font-size: 2.5rem;
  font-weight: 700;
}
.src-submissions__header p {
  color: #718096;
  font-size: 1.1rem;
}
.src-submissions__filters {
  background: #f7fafc;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.src-submissions__filters-title {
  color: #2d3748;
  margin-bottom: 15px;
  font-size: 1.3rem;
  font-weight: 600;
}
.src-submissions__filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}
.src-submissions__filters-group label {
  display: block;
  margin-bottom: 5px;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.9rem;
}
.src-submissions__filters-group select,
.src-submissions__filters-group input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s;
}
.src-submissions__filters-group select:focus,
.src-submissions__filters-group input:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}
.src-submissions__filters-actions {
  display: flex;
  gap: 10px;
}
.src-submissions__filters-actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}
.src-submissions__filters-actions button.apply {
  background: #3182ce;
  color: white;
}
.src-submissions__filters-actions button.apply:hover {
  background: #2c5282;
}
.src-submissions__filters-actions button.clear {
  background: #e2e8f0;
  color: #4a5568;
}
.src-submissions__filters-actions button.clear:hover {
  background: #cbd5e0;
}
.src-submissions__table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.src-submissions__table {
  width: 100%;
  border-collapse: collapse;
}
.src-submissions__table thead {
  background: #edf2f7;
}
.src-submissions__table thead th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 2px solid #e2e8f0;
  cursor: pointer;
  position: relative;
}
.src-submissions__table thead th:hover {
  background: #e2e8f0;
}
.src-submissions__table thead th.sortable::after {
  content: "↕";
  position: absolute;
  right: 8px;
  opacity: 0.5;
}
.src-submissions__table thead th.sort-asc::after {
  content: "↑";
  opacity: 1;
}
.src-submissions__table thead th.sort-desc::after {
  content: "↓";
  opacity: 1;
}
.src-submissions__table tbody tr {
  transition: background-color 0.2s;
}
.src-submissions__table tbody tr:hover {
  background: #f7fafc;
}
.src-submissions__table tbody tr:nth-child(even) {
  background: #fafafa;
}
.src-submissions__table tbody tr td {
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
}
.src-submissions__status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}
.src-submissions__status--pending {
  background: #fef5e7;
  color: #d69e2e;
}
.src-submissions__status--good-fit {
  background: #e6fffa;
  color: #319795;
}
.src-submissions__status--best-fit {
  background: #e6ffed;
  color: #38a169;
}
.src-submissions__status--not-fit {
  background: #fed7d7;
  color: #e53e3e;
}
.src-submissions__status--accepted {
  background: #e6ffed;
  color: #38a169;
}
.src-submissions__status--rejected {
  background: #fed7d7;
  color: #e53e3e;
}
.src-submissions__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f7fafc;
}
.src-submissions__pagination-info {
  color: #718096;
  font-size: 0.9rem;
}
.src-submissions__pagination-controls {
  display: flex;
  gap: 10px;
}
.src-submissions__pagination-controls button {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  color: #4a5568;
  transition: all 0.2s;
}
.src-submissions__pagination-controls button:hover:not(:disabled) {
  background: #edf2f7;
  border-color: #cbd5e0;
}
.src-submissions__pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.src-submissions__pagination-controls button.active {
  background: #3182ce;
  color: white;
  border-color: #3182ce;
}
.src-submissions__loading {
  text-align: center;
  padding: 40px;
  color: #718096;
  font-size: 1.1rem;
}
.src-submissions__loading::before {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3182ce;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}
.src-submissions__error {
  background: #fed7d7;
  color: #e53e3e;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}
.src-submissions__empty {
  text-align: center;
  padding: 40px;
  color: #718096;
  font-size: 1.1rem;
}

/* New Modal Styles */
.src-submissions__modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s linear 0.25s, opacity 0.25s;
}

.src-submissions__modal-backdrop.active {
  visibility: visible;
  opacity: 1;
  transition-delay: 0s;
}

.src-submissions__modal {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  position: relative;
}

.src-submissions__modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.src-submissions__modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
}

.src-submissions__modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #a0aec0;
  cursor: pointer;
  transition: color 0.2s;
}

.src-submissions__modal-close:hover {
  color: #4a5568;
}

.src-submissions__modal-body {
  margin-bottom: 20px;
}

.src-submissions__modal-section {
  margin-bottom: 20px;
}

.src-submissions__modal-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 10px;
}

.src-submissions__modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #e2e8f0;
}

.src-submissions__modal-footer button {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.src-submissions__modal-footer button.save {
  background: #3182ce;
  color: white;
  border: none;
}

.src-submissions__modal-footer button.save:hover {
  background: #2c5282;
}

.src-submissions__modal-footer button.cancel {
  background: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.src-submissions__modal-footer button.cancel:hover {
  background: #f7fafc;
}

.src-submissions__form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.src-submissions__form-group {
  flex: 1;
}

.src-submissions__form-group label {
  display: block;
  margin-bottom: 5px;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.9rem;
}

.src-submissions__form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s;
}

.src-submissions__form-group select:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.src-submissions__info-item {
  margin-bottom: 10px;
}

.src-submissions__info-label {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.9rem;
}

.src-submissions__info-value {
  color: #2d3748;
  margin-top: 3px;
}

/* Worklink Preview Styles */
.src-submissions__worklink {
  margin-top: 8px;
}

.src-submissions__worklink-preview {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 5px;
  height: 300px;
  width: 100%;
  position: relative;
}

.src-submissions__worklink-frame {
  width: 100%;
  height: 100%;
  border: none;
  position: relative;
  z-index: 1;
}

.iframe-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7fafc;
  z-index: 0;
}

.iframe-loading-text {
  color: #718096;
  font-size: 0.9rem;
  margin-top: 8px;
  animation: pulse 2s infinite;
}

.iframe-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7fafc;
  z-index: 0;
}

.iframe-error-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.iframe-error-message {
  color: #4a5568;
  font-size: 0.9rem;
  text-align: center;
  padding: 0 20px;
}

.iframe-error-message a {
  color: #3182ce;
  text-decoration: none;
  display: inline-block;
  margin-top: 8px;
  padding: 5px 10px;
  border: 1px solid #3182ce;
  border-radius: 4px;
  transition: all 0.2s;
}

.iframe-error-message a:hover {
  background-color: #3182ce;
  color: white;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.src-submissions__worklink-text {
  margin-top: 5px;
  word-break: break-all;
}

.src-submissions__worklink-text a {
  color: #3182ce;
  text-decoration: none;
}

.src-submissions__worklink-text a:hover {
  text-decoration: underline;
}

.src-submissions__actions {
  display: flex;
  gap: 10px;
}

.src-submissions__actions button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.src-submissions__actions .edit-btn {
  background: #3182ce;
  color: white;
}

.src-submissions__actions .edit-btn:hover {
  background: #2c5282;
}

.src-submissions__status-select {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-top: 4px;
}

.src-submissions__top-submission {
  position: relative;
  background-color: rgba(237, 242, 247, 0.5) !important;
}

.src-submissions__top-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #4299e1;
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  z-index: 10;
  transition: transform 0.2s;
}

.src-submissions__top-badge::before {
  content: "★ ";
}

.src-submissions__top-submission:hover .src-submissions__top-badge {
  transform: scale(1.1);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (max-width: 768px) {
  .src-submissions {
    padding: 10px;
  }
  .src-submissions__filters-grid {
    grid-template-columns: 1fr;
  }
  .src-submissions__filters-actions {
    flex-direction: column;
  }
  .src-submissions__table-container {
    overflow-x: auto;
  }
  .src-submissions__table {
    min-width: 800px;
  }
  .src-submissions__table th,
  .src-submissions__table td {
    padding: 8px;
    font-size: 0.8rem;
  }
  .src-submissions__pagination {
    flex-direction: column;
    gap: 15px;
  }
  .src-submissions__pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
  .src-submissions__form-row {
    flex-direction: column;
    gap: 10px;
  }
  .src-submissions__modal {
    width: 95%;
    padding: 16px;
  }
}
