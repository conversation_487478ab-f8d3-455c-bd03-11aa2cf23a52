@font-face {
  src: url("/fonts/Helvetica.ttf");
  font-family: "Helvetica";
}
:root {
  --primary: #10a37f;
  --secondary: #075c48;
  --white: #fff;
  --primaryLight: #f8f8f8;
  --primaryLightNavStroke: #f8f8f8;
  --primaryDark: #222222;
  --primaryDarkNavStroke: #222222;
  --secondaryLight: #dbdbdb;
  --secondaryLightActive: #cbcbcb;
  --secondaryDark: #ececec;
  --secondaryDarkShadow: rgba(0, 0, 0, 0.16);
  --textColor: #7bba81;
  --special: #d2af7a;
  --ternary: #c4c9d3;
  --error: #e74c3c;
  --maxPageWidth: 110rem;
  --discord: #7b40ff;
  --link: rgb(0, 71, 125);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Helvetica;
}

.ace_editor {
  background-color: var(--ui-element-secondary-color); /* Change this to your desired background color */
}

.ace_editor,
.ace_editor * {
  font-family: monospace !important;
  letter-spacing: 0 !important;
  font-weight: 400;
}

.ace_gutter-active-line {
  background: none !important;
}

.ace_editor .ace_marker-layer .ace_active-line {
  background: none;
}

.ace-monokai .ace_gutter {
  background-color: var(--ui-element-secondary-color); /* Change this to your desired gutter color */
}

.ace_autocomplete {
  background-color: var(--ui-element-color) !important; /* Change this to your desired background color */
}

html,
body {
  width: 100%;
  height: 100%;
  color: var(--text-color);
}

.code {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.code .overlaySubmission {
  position: fixed;
  top: 0;
  z-index: 99;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.318);
  -webkit-backdrop-filter: blur(2);
          backdrop-filter: blur(2);
  display: flex;
  justify-content: center;
  display: none;
  align-items: center;
}
.code .overlaySubmission .codeDiv {
  position: relative;
  background-color: var(--ui-element-secondary-color);
  padding: 1rem;
  border-radius: 5px;
  width: 50%;
  height: 70%;
}
.code .overlaySubmission .codeDiv i {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 1.3rem;
}
.code .overlaySubmission .codeDiv #submissionCode {
  width: 100%;
  height: 100%;
}
.code .overlaySubmission .codeDiv #submissionCode .ace_editor .ace_cursor {
  display: none !important;
}
.code .highlight {
  border: 1px solid rgba(128, 128, 128, 0.3) !important;
}
.code .heading {
  height: 45px;
  width: 100%;
  overflow-x: scroll;
  background-color: var(--ui-element-color);
  display: flex;
  padding: 0.5rem;
  align-items: center;
}
.code .heading .solutionHeading {
  position: relative;
}
.code .heading::-webkit-scrollbar {
  display: none;
}
.code .heading > div {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  padding: 0.5rem;
  margin: 0 0.5rem;
  border-radius: 5px;
  transition: 0.2s;
}
.code .heading > div:active {
  background-color: transparent;
}
.code .heading > div p {
  opacity: 0.5;
}
.code .heading > div.active p {
  opacity: 1;
  color: var(--text-color);
}
.code .heading > div.active i {
  opacity: 1;
}
.code .heading > div:nth-child(1) i {
  color: #227aff;
}
.code .heading > div:nth-child(2) i {
  color: #d2a152;
}
.code .heading > div:nth-child(3) i {
  color: #227aff;
}
.code .heading > div:nth-child(4) i {
  color: #a42d18;
}
.code .heading > div .testcaseHeading,
.code .heading > div .codeHeading {
  color: #428e7c !important;
  border: none;
}
.code .left {
  overflow: hidden;
  width: 40%;
  height: 100%;
  background-color: var(--ui-element-secondary-color);
  border-radius: 8px;
  border: 1px solid transparent;
  min-width: 40px;
  position: relative;
}
.code .left .confirmPopup,
.code .left .confirmPopupReview {
  position: absolute;
  display: flex;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.266);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  width: 100%;
  height: 100%;
  z-index: 9;
  opacity: 0;
  pointer-events: none;
}
.code .left .confirmPopup .overlay,
.code .left .confirmPopupReview .overlay {
  margin: 1rem;
  margin-top: 5rem;
  max-width: 25rem;
  min-width: 20rem;
  height: -moz-fit-content;
  height: fit-content;
  padding: 2rem;
  background-color: var(--ui-element-ternary-color);
}
.code .left .confirmPopup .overlay p,
.code .left .confirmPopupReview .overlay p {
  color: var(--text-color);
  opacity: 1;
  margin-bottom: 1rem;
}
.code .left .confirmPopup .overlay button:nth-child(1),
.code .left .confirmPopupReview .overlay button:nth-child(1) {
  padding: 0.3rem 1rem;
  font-size: 1rem;
  background-color: var(--error);
  border: none;
  margin-right: 1rem;
  border-radius: 5px;
}
.code .left .confirmPopup .overlay button:nth-child(2),
.code .left .confirmPopupReview .overlay button:nth-child(2) {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  background-color: var(--ui-element-color);
  border: none;
  margin-right: 1rem;
  border-radius: 5px;
}
.code .left .confirmPopup.active,
.code .left .confirmPopupReview.active {
  opacity: 1;
  pointer-events: all;
}
.code .left.onlyNav {
  width: 40px;
}
.code .left.onlyNav .heading {
  padding: 2.5rem 0;
  display: flex;
  flex-direction: column;
  gap: 3rem;
  width: 100%;
  height: 100%;
}
.code .left.onlyNav .heading div {
  display: flex;
  align-items: center;
  transform: rotate(90deg);
  white-space: nowrap; /* Prevent text from wrapping */
  margin: 20px 0; /* Adjust margin to ensure no overlap */
}
.code .left.onlyNav .content {
  display: none;
}
.code .left .content {
  width: 100%;
  height: calc(100% - 45px);
  overflow-y: scroll;
}
.code .left .content::-webkit-scrollbar {
  width: 3px; /* Width of the scrollbar */
  height: 3px;
}
.code .left .content::-webkit-scrollbar-thumb {
  border-radius: 50px;
  background: gray; /* Background of the scrollbar thumb */
}
.code .left .content::-webkit-scrollbar-thumb:hover {
  background: #555; /* Background of the scrollbar thumb when hovered */
}
.code .left .content > div {
  display: none;
}
.code .left .content .active {
  width: 100%;
  height: 100%;
  display: block;
}
.code .left .content .question {
  min-width: 250px;
  padding-right: 1rem;
  padding-bottom: 2rem;
  margin-top: 2rem;
  padding-left: 1.5rem;
}
.code .left .content .question .title p {
  font-weight: 500;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
}
.code .left .content .question .description {
  white-space: pre-wrap;
  color: var(--text-color);
  font-weight: 400;
  font-size: 1.05rem;
  opacity: 0.75;
}
.code .left .content .question .image {
  width: 25rem;
  height: 18rem;
  margin-top: 1rem;
}
.code .left .content .question .image img {
  -o-object-position: left;
     object-position: left;
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.code .left .content .question .sampleTestCases {
  margin-top: 4rem;
}
.code .left .content .question .sampleTestCases h3 {
  font-size: 1.3rem;
  font-weight: 400;
  margin-bottom: 1rem;
}
.code .left .content .question .sampleTestCases .testcases {
  margin-bottom: 2rem;
}
.code .left .content .question .sampleTestCases .testcases p {
  margin-top: 1rem;
  color: var(--text-color);
}
.code .left .content .question .sampleTestCases .testcases div {
  border-radius: 3px;
  margin-top: 0.6rem;
  padding: 0.5rem 1rem;
  width: 100%;
  background-color: var(--ui-element-color);
}
.code .left .content .question .sampleTestCases .testcases div pre {
  color: var(--text-color);
  opacity: 0.6;
  line-height: 1.4rem;
  margin-top: 0;
}
.code .left .content .question .inputFormat,
.code .left .content .question .outputFormat {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: rgb(17, 17, 17);
  border-radius: 4px;
  font-size: 0.85rem;
  line-height: 1.2rem;
}
.code .left .content .question .inputFormat pre,
.code .left .content .question .outputFormat pre {
  margin-top: 0;
  white-space: pre-wrap;
}
.code .left .content .question .inputFormat {
  margin-bottom: 1rem;
}
.code .left .content .question .explaination {
  margin-top: 2rem;
}
.code .left .content .question .explaination h3 {
  font-size: 1.3rem;
  font-weight: 400;
  margin-bottom: 1rem;
}
.code .left .content .question .explaination pre {
  white-space: pre-wrap;
  opacity: 0.75;
  margin-top: 0.5rem;
  color: var(--text-color);
}
.code .left .content .question .constrains {
  margin-top: 2rem;
  padding-bottom: 2rem;
}
.code .left .content .question .constrains h3 {
  font-size: 1.3rem;
  font-weight: 400;
  margin-bottom: 1rem;
}
.code .left .content .question .constrains ul {
  padding-left: 1.5rem;
}
.code .left .content .question .constrains ul li {
  opacity: 0.75;
  color: var(--text-color);
  margin-top: 0.5rem;
}
.code .left .content .submission .head {
  width: 100%;
  padding: 1rem 1rem;
  border-bottom: 1px solid #444;
  display: flex;
}
.code .left .content .submission .head p {
  width: 20%;
  min-width: 67px;
  color: #797979;
  text-align: center;
}
.code .left .content .submission .head p:nth-child(1) {
  text-align: left;
  min-width: 80px;
  width: 20%;
}
.code .left .content .submission .allSubmissions {
  width: 100%;
}
.code .left .content .submission .allSubmissions .loader {
  --uib-size: 25px;
  margin: auto;
  margin-top: 2rem;
}
.code .left .content .submission .allSubmissions .result {
  width: 100%;
  padding: 1rem 1rem;
  border-bottom: 1px solid rgba(129, 129, 129, 0.1215686275);
  display: flex;
  align-items: center;
  transition: 0.2s;
}
.code .left .content .submission .allSubmissions .result:hover {
  background-color: #222222;
}
.code .left .content .submission .allSubmissions .result small {
  display: block;
}
.code .left .content .submission .allSubmissions .result div {
  min-width: 80px;
  width: 20%;
}
.code .left .content .submission .allSubmissions .result p {
  width: 20%;
  color: var(--text-color);
  min-width: 67px;
  white-space: nowrap;
  text-align: center;
}
.code .left .content .submission .allSubmissions .result p:nth-child(3) {
  font-size: 0.9rem;
}
.code .left .content .submission .allSubmissions .result p.correct {
  color: #4cac5e;
}
.code .left .content .submission .allSubmissions .result p.incorrect {
  color: #d13e40;
}
.code .left .content .submission .allSubmissions .result p:nth-child(1) {
  margin-bottom: 0.2rem;
  text-align: left;
  min-width: 80px;
  font-size: 1.1rem;
  font-weight: 400;
}
.code .left .content .submission .allSubmissions .result small {
  white-space: nowrap;
  color: var(--text-color);
}
.code .left .content .solution {
  padding: 1rem;
  width: 100%;
  height: 100%;
}
.code .left .content .solution .solution-tabs {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 10px;
  border-bottom: 2px solid rgb(60, 60, 60);
  padding: 0;
}
.code .left .content .solution .solution-tab-btn {
  width: -moz-fit-content;
  width: fit-content;
  padding: 6px 20px;
  background: transparent;
  border: none;
  cursor: pointer;
}
.code .left .content .solution .solution-tab-btn:nth-child(1) {
  border-right: 1px solid rgb(60, 60, 60);
}
.code .left .content .solution .solution-tab-btn:nth-child(2) {
  border-right: 1px solid rgb(60, 60, 60);
}
.code .left .content .solution .solution-tab-btn:nth-child(3) {
  border-right: 1px solid rgb(60, 60, 60);
}
.code .left .content .solution .solution-tab-btn.active {
  background: rgb(60, 60, 60);
  color: white;
}
.code .left .content .solution #solutionEditor {
  width: 100%;
  height: 100%;
}
.code .left .content .solution #solutionEditor .ace_editor .ace_cursor {
  display: none !important;
}
.code .left .content .review .reviewEditor {
  padding: 1.5rem;
}
.code .left .content .review .reviewEditor ul {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}
.code .left .content .review .reviewEditor h2 {
  margin-bottom: 1rem;
}
.code .left .content .review .reviewEditor p {
  margin-bottom: 0.8rem;
}
.code .left .content .review .reviewEditor .reviewLoader {
  margin-top: 2rem;
  margin: auto;
  margin-top: 2rem;
  width: 25px;
  padding: 0 !important;
  height: 25px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.code .verticalResizer {
  width: 10px;
  height: 95%;
  border-radius: 10px;
  transition: 0.2s;
  margin: 0.1rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: col-resize;
}
.code .verticalResizer .line {
  background-color: var(--ui-element-ternary-color);
  width: 2px;
  height: 30px;
  transition: 0.1s;
}
.code .verticalResizer:active .line {
  height: 100%;
  background-color: #227aff;
}
.code .verticalResizer:hover .line {
  height: 100%;
  background-color: #227aff;
}
.code .right {
  min-width: 40px;
  width: 60%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.code .right .horizontalResizer {
  width: 95%;
  height: 8px;
  border-radius: 10px;
  transition: 0.2s;
  margin: 0.1rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: row-resize;
}
.code .right .horizontalResizer .line {
  background-color: var(--ui-element-ternary-color);
  width: 30px;
  height: 2px;
  transition: 0.1s;
}
.code .right .horizontalResizer:active .line {
  width: 100%;
  background-color: #227aff;
}
.code .right .horizontalResizer:hover .line {
  width: 100%;
  background-color: #227aff;
}
.code .right .right-top {
  min-height: 44px;
  overflow: hidden;
  border-radius: 8px;
  width: 100%;
  height: 70%;
  background-color: var(--ui-element-secondary-color);
  border: 1px solid transparent;
}
.code .right .right-top.onlyNav {
  width: 40px;
}
.code .right .right-top.onlyNav .heading {
  padding: 0.5rem 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.code .right .right-top.onlyNav .heading .editor-left {
  display: flex;
  align-items: center;
  transform: rotate(90deg);
  white-space: nowrap; /* Prevent text from wrapping */
  margin: 20px 0; /* Adjust margin to ensure no overlap */
}
.code .right .right-top.onlyNav .heading .editor-right {
  display: none;
}
.code .right .right-top.onlyNav .language_topic {
  display: none;
}
.code .right .right-top.onlyNav #editor {
  display: none;
}
.code .right .right-top .editor-left div {
  display: flex;
  gap: 0.5rem;
}
.code .right .right-top .editor-left div p {
  opacity: 1;
  color: var(--text-color);
}
.code .right .right-top .editor-right {
  position: relative;
}
.code .right .right-top .editor-right .mergeBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: 0.2s;
  position: absolute;
  background-color: var(--ui-element-ternary-color);
  z-index: 2;
  right: 0;
  width: 170px;
  padding: 0.5rem;
  border-radius: 5px;
  opacity: 0;
  transform: translateX(-200px);
  pointer-events: none;
}
.code .right .right-top .heading {
  padding-right: 0.5rem;
  display: flex;
  justify-content: space-between;
}
.code .right .right-top .heading .resetBtn {
  background-color: rgb(67, 67, 67);
}
.code .right .right-top .heading button {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  background-color: var(--ui-element-ternary-color);
  border: none;
  padding: 0.4rem 1rem;
  color: var(--text-color);
  border-radius: 4px;
  border: 1px solid transparent;
  transition: 0.2s;
}
.code .right .right-top .heading button .ri-play-fill {
  font-size: 1.2rem;
  color: #227aff;
}
.code .right .right-top .heading button .ri-logout-box-r-line {
  font-size: 1.1rem;
  color: #828282;
}
.code .right .right-top .heading button:hover {
  border: 1px solid #7e7e7e;
  color: var(--text-color);
}
.code .right .right-top .language_topic {
  height: 35px;
  padding: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.7rem;
  border-bottom: 1px solid var(--ui-element-ternary-color);
}
.code .right .right-top .language_topic p {
  color: #666;
}
.code .right .right-top .language_topic #languageSelect {
  background-color: transparent;
  border: none;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.2rem 0.5rem;
  border-radius: 5px;
  transition: 0.2s;
}
.code .right .right-top .language_topic #languageSelect:hover {
  background-color: var(--ui-element-ternary-color);
  color: var(--text-color);
}
.code .right .right-top .language_topic .line {
  width: 1px;
  height: 15px;
  background-color: #666;
}
.code .right .right-top #editor {
  margin: 0.5rem 0;
  font-family: monospace !important;
  width: 100%;
  height: calc(100% - (79px + 1rem));
}
.code .right .right-bottom {
  min-height: 44px;
  overflow: hidden;
  border-radius: 8px;
  width: 100%;
  height: 30%;
  background-color: var(--ui-element-secondary-color);
  border: 1px solid transparent;
}
.code .right .right-bottom .heading {
  display: flex;
  justify-content: space-between;
}
.code .right .right-bottom .heading .button.discord {
  font-size: 0.9rem;
  padding: 0.3rem 1rem;
  border-radius: 3px;
}
.code .right .right-bottom .heading .nextButton {
  border-radius: 3px;
  padding: 0.3rem 1rem;
  background-color: #10a37f;
  border: none;
  color: black;
}
.code .right .right-bottom.onlyNav {
  width: 40px;
}
.code .right .right-bottom.onlyNav .heading {
  padding: 2rem 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.code .right .right-bottom.onlyNav .heading .button {
  display: none;
}
.code .right .right-bottom.onlyNav .heading div {
  display: flex;
  align-items: center;
  transform: rotate(90deg);
  white-space: nowrap; /* Prevent text from wrapping */
  margin: 10px 0; /* Adjust margin to ensure no overlap */
}
.code .right .right-bottom.onlyNav .heading div .testCaseLoader {
  display: none;
}
.code .right .right-bottom.onlyNav .cases,
.code .right .right-bottom.onlyNav .selectedCase {
  display: none;
}
.code .right .right-bottom .output {
  width: 100%;
  height: calc(100% - 30px);
  overflow: scroll;
}
.code .right .right-bottom .output .result {
  display: none;
  font-size: 1.1rem;
  padding: 1rem;
  gap: 2rem;
  align-items: center;
}
.code .right .right-bottom .output .result small {
  opacity: 0.4;
}
.code .right .right-bottom .output .result h2 {
  font-weight: 400;
}
.code .right .right-bottom .output .result h2.accepted {
  color: #00d82f;
}
.code .right .right-bottom .output .result h2.rejected {
  color: var(--error);
}
.code .right .right-bottom .output .cases {
  display: none;
  overflow-x: scroll;
  gap: 1rem;
  padding: 1rem 1rem;
}
.code .right .right-bottom .output .cases p {
  white-space: nowrap;
  transition: 0.2s;
  padding: 0.5rem 1rem;
  padding-left: 1.5rem;
  border-radius: 5px;
  font-size: 1rem;
  color: #737373;
  position: relative;
}
.code .right .right-bottom .output .cases p::after {
  content: " ";
  position: absolute;
  left: 10px;
  top: 49%;
  z-index: 5;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  transform: translateY(-50%);
}
.code .right .right-bottom .output .cases p.correct::after {
  background-color: #00d82f;
}
.code .right .right-bottom .output .cases p.wrong::after {
  background-color: var(--error);
}
.code .right .right-bottom .output .cases p.active {
  color: var(--text-color);
  background-color: var(--ui-element-color);
}
.code .right .right-bottom .output .cases p:hover {
  color: var(--text-color);
  background-color: var(--ui-element-color);
}
.code .right .right-bottom .output .cases .skeleton {
  background-color: var(--ui-element-color);
  width: 80px;
  height: 30px;
  border-radius: 5px;
  animation: skeletonLoading 1.5s infinite ease-in-out;
}
.code .right .right-bottom .output .empty {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.code .right .right-bottom .output .empty p {
  padding: 0 3rem;
  text-align: center;
  color: var(--fuscous-gray);
}
.code .right .right-bottom .output .empty p.error {
  color: var(--error);
}
.code .right .right-bottom .output .selectedCase {
  display: none;
  width: 100%;
  height: 100%;
  padding: 1rem;
}
.code .right .right-bottom .output .selectedCase::-webkit-scrollbar {
  display: none;
}
.code .right .right-bottom .output .selectedCase > div {
  display: none;
  width: 100%;
}
.code .right .right-bottom .output .selectedCase > div.active {
  display: block;
}
.code .right .right-bottom .output .selectedCase > div div {
  margin-bottom: 2rem;
}
.code .right .right-bottom .output .selectedCase > div div.error p {
  font-size: 1.5rem;
  margin-bottom: 0.3rem;
  color: var(--error);
}
.code .right .right-bottom .output .selectedCase > div div.error module {
  color: var(--error);
}
.code .right .right-bottom .output .selectedCase > div div.error small {
  font-size: 1rem;
  color: var(--text-color);
  opacity: 0.6;
  display: block;
  margin-bottom: 1rem;
}
.code .right .right-bottom .output .selectedCase > div div.error pre {
  overflow-x: auto;
  padding: 1rem;
  border-radius: 5px;
  background-color: rgba(231, 77, 60, 0.1215686275);
  color: var(--error);
}
.code .right .right-bottom .output .selectedCase > div div p {
  opacity: 0.7;
  color: var(--text-color);
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}
.code .right .right-bottom .output .selectedCase > div div p.accepted {
  color: #00d82f;
}
.code .right .right-bottom .output .selectedCase > div div p.rejected {
  color: #a42d18;
}
.code .right .right-bottom .output .selectedCase > div div pre {
  overflow-x: auto;
  width: 100%;
  min-height: 30px;
  opacity: 0.7;
  padding: 0.5rem 1rem;
  background-color: var(--ui-element-color);
  font-size: 1.1rem;
}
.code .right .right-bottom .output .selectedCase > div.rejected div:nth-child(2) pre {
  color: #e74c3c;
}
.code .right .right-bottom .output .selectedCase > div.rejected div:nth-child(3) pre {
  color: #00d82f;
}
.code .right .right-bottom .output .selectedCase .skeleton {
  display: block;
}
.code .right .right-bottom .output .selectedCase .skeleton p {
  width: 100px;
  height: 15px;
  background-color: var(--ui-element-color);
  animation: skeletonLoading 1.5s infinite ease-in-out;
}
.code .right .right-bottom .output .selectedCase .skeleton pre {
  animation: skeletonLoading 1.5s infinite ease-in-out;
  background-color: var(--ui-element-color);
  width: 100%;
  height: 25px;
}

/* HTML: <div class="loader"></div> */
.testCaseLoader {
  width: 18px;
  padding: 0 !important;
  height: 18px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: none;
}

@media (max-width: 600px) {
  .code .left {
    width: 100%;
  }
  .code .left .heading div:nth-child(2) {
    display: none;
  }
  .code .left .heading div:nth-child(3) {
    display: none;
  }
  .code .verticalResizer {
    display: none;
  }
  .code .right {
    display: none;
  }
}
.superscript {
  vertical-align: super;
  font-size: smaller;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes skeletonLoading {
  0% {
    background-color: var(--ui-element-color); /* Dark gray */
  }
  50% {
    background-color: var(--ui-element-secondary-color); /* Light gray */
  }
  100% {
    background-color: var(--ui-element-color); /* Dark gray */
  }
}
.dotLoader {
  --uib-size: 18px;
  --uib-color: var(--text-color);
  --uib-speed: 1s;
  --uib-dot-size: calc(var(--uib-size) * 0.4);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--uib-dot-size);
  width: var(--uib-size);
}

.dotLoader::before,
.dotLoader::after {
  content: "";
  position: absolute;
  height: var(--uib-dot-size);
  width: var(--uib-dot-size);
  border-radius: 50%;
  background-color: var(--uib-color);
  flex-shrink: 0;
  transition: background-color 0.3s ease;
}

.dotLoader::before {
  animation: orbit var(--uib-speed) linear infinite;
}

.dotLoader::after {
  animation: orbit var(--uib-speed) linear calc(var(--uib-speed) / -2) infinite;
}

@keyframes orbit {
  0% {
    transform: translateX(calc(var(--uib-size) * 0.25)) scale(0.73684);
    opacity: 0.65;
  }
  5% {
    transform: translateX(calc(var(--uib-size) * 0.235)) scale(0.684208);
    opacity: 0.58;
  }
  10% {
    transform: translateX(calc(var(--uib-size) * 0.182)) scale(0.631576);
    opacity: 0.51;
  }
  15% {
    transform: translateX(calc(var(--uib-size) * 0.129)) scale(0.578944);
    opacity: 0.44;
  }
  20% {
    transform: translateX(calc(var(--uib-size) * 0.076)) scale(0.526312);
    opacity: 0.37;
  }
  25% {
    transform: translateX(0%) scale(0.47368);
    opacity: 0.3;
  }
  30% {
    transform: translateX(calc(var(--uib-size) * -0.076)) scale(0.526312);
    opacity: 0.37;
  }
  35% {
    transform: translateX(calc(var(--uib-size) * -0.129)) scale(0.578944);
    opacity: 0.44;
  }
  40% {
    transform: translateX(calc(var(--uib-size) * -0.182)) scale(0.631576);
    opacity: 0.51;
  }
  45% {
    transform: translateX(calc(var(--uib-size) * -0.235)) scale(0.684208);
    opacity: 0.58;
  }
  50% {
    transform: translateX(calc(var(--uib-size) * -0.25)) scale(0.73684);
    opacity: 0.65;
  }
  55% {
    transform: translateX(calc(var(--uib-size) * -0.235)) scale(0.789472);
    opacity: 0.72;
  }
  60% {
    transform: translateX(calc(var(--uib-size) * -0.182)) scale(0.842104);
    opacity: 0.79;
  }
  65% {
    transform: translateX(calc(var(--uib-size) * -0.129)) scale(0.894736);
    opacity: 0.86;
  }
  70% {
    transform: translateX(calc(var(--uib-size) * -0.076)) scale(0.947368);
    opacity: 0.93;
  }
  75% {
    transform: translateX(0%) scale(1);
    opacity: 1;
  }
  80% {
    transform: translateX(calc(var(--uib-size) * 0.076)) scale(0.947368);
    opacity: 0.93;
  }
  85% {
    transform: translateX(calc(var(--uib-size) * 0.129)) scale(0.894736);
    opacity: 0.86;
  }
  90% {
    transform: translateX(calc(var(--uib-size) * 0.182)) scale(0.842104);
    opacity: 0.79;
  }
  95% {
    transform: translateX(calc(var(--uib-size) * 0.235)) scale(0.789472);
    opacity: 0.72;
  }
  100% {
    transform: translateX(calc(var(--uib-size) * 0.25)) scale(0.73684);
    opacity: 0.65;
  }
}/*# sourceMappingURL=coding.css.map */