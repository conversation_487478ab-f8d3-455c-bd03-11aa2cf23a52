@font-face {
    src: url("/fonts/Helvetica.ttf");
    font-family: "Helvetica";
}

:root {
    --primary: #10a37f;
    --secondary: #075c48;
    --white: #fff;
    --primaryLight: #f8f8f8;
    --primaryLightNavStroke: #f8f8f8;
    --primaryDark: #222222;
    --primaryDarkNavStroke: #222222;
    --secondaryLight: #dbdbdb;
    --secondaryLightActive: #cbcbcb;
    --secondaryDark: #ececec;
    --secondaryDarkShadow: rgba(0, 0, 0, 0.16);
    --textColor: #7bba81;
    --special: #d2af7a;
    --ternary: #c4c9d3;
    --error: #e74c3c;
    --maxPageWidth: 110rem;
    --discord: #7b40ff;
    --link: rgb(0, 71, 125);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Helvetica;
}

.ace_editor {
    background-color: var(--ui-element-secondary-color); /* Change this to your desired background color */
}
.ace_editor,
.ace_editor * {
    font-family: monospace !important;
    letter-spacing: 0 !important;
    font-weight: 400;
}
.ace_gutter-active-line {
    background: none !important;
}
.ace_editor .ace_marker-layer .ace_active-line {
    background: none;
}
.ace-monokai .ace_gutter {
    background-color: var(--ui-element-secondary-color); /* Change this to your desired gutter color */
}
.ace_autocomplete {
    background-color: var(--ui-element-color) !important; /* Change this to your desired background color */
}

html,
body {
    width: 100%;
    height: 100%;
    color: var(--text-color);
}
.code {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .overlaySubmission {
        position: fixed;
        top: 0;
        z-index: 99;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.318);
        // opacity: 0.5;
        backdrop-filter: blur(2);
        display: flex;
        justify-content: center;
        display: none;
        align-items: center;
        .codeDiv {
            position: relative;
            background-color: var(--ui-element-secondary-color);
            padding: 1rem;
            border-radius: 5px;

            width: 50%;
            height: 70%;

            i {
                position: absolute;
                top: 5px;
                right: 5px;
                font-size: 1.3rem;
            }
            #submissionCode {
                width: 100%;
                height: 100%;
                .ace_editor .ace_cursor {
                    display: none !important;
                }
            }
        }
    }
    .highlight {
        border: 1px solid rgba(128, 128, 128, 0.3) !important;
    }
    .heading {
        height: 45px;
        // padding: 0.8rem 2rem;
        width: 100%;
        overflow-x: scroll;
        background-color: var(--ui-element-color);
        display: flex;
        padding: 0.5rem;
        align-items: center;
        .solutionHeading {
            position: relative;
        }
        &::-webkit-scrollbar {
            display: none;
        }
        & > div {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            padding: 0.5rem;
            margin: 0 0.5rem;
            border-radius: 5px;
            transition: 0.2s;
            // &:hover{
            //     background-color: rgba(128, 128, 128, 0.241);
            // }
            &:active {
                background-color: transparent;
            }

            p {
                opacity: 0.5;
            }

            &.active {
                p {
                    opacity: 1;
                    color: var(--text-color);
                }
                i {
                    opacity: 1;
                }
            }
            &:nth-child(1) {
                i {
                    color: #227aff;
                }
            }
            &:nth-child(2) {
                i {
                    color: #d2a152;
                }
            }
            &:nth-child(3) {
                i {
                    color: #227aff;
                }
            }
            &:nth-child(4) {
                i {
                    color: #a42d18;
                }
            }
            .testcaseHeading,
            .codeHeading {
                color: #428e7c !important;
                border: none;
            }
        }
    }

    .left {
        overflow: hidden;
        width: 40%;
        height: 100%;
        background-color: var(--ui-element-secondary-color);
        border-radius: 8px;
        border: 1px solid transparent;
        min-width: 40px;
        position: relative;

        .confirmPopup,
        .confirmPopupReview {
            position: absolute;
            display: flex;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.266);
            backdrop-filter: blur(2px);
            width: 100%;
            height: 100%;
            z-index: 9;
            opacity: 0;
            pointer-events: none;
            .overlay {
                margin: 1rem;
                margin-top: 5rem;
                max-width: 25rem;
                min-width: 20rem;
                height: fit-content;
                padding: 2rem;
                background-color: var(--ui-element-ternary-color);
                p {
                    color: var(--text-color);
                    opacity: 1;
                    margin-bottom: 1rem;
                }
                button:nth-child(1) {
                    padding: 0.3rem 1rem;
                    font-size: 1rem;
                    background-color: var(--error);
                    border: none;
                    margin-right: 1rem;
                    border-radius: 5px;
                }
                button:nth-child(2) {
                    padding: 0.5rem 1rem;
                    font-size: 1rem;
                    background-color: var(--ui-element-color);
                    border: none;
                    margin-right: 1rem;
                    border-radius: 5px;
                }
            }
            &.active {
                opacity: 1;
                pointer-events: all;
            }
        }
        &.onlyNav {
            width: 40px;
            .heading {
                padding: 2.5rem 0;
                display: flex;
                flex-direction: column;
                gap: 3rem;
                width: 100%;
                height: 100%;

                div {
                    display: flex;
                    align-items: center;
                    transform: rotate(90deg);
                    // transform-origin: left center; /* Rotate around the left side */
                    white-space: nowrap; /* Prevent text from wrapping */
                    margin: 20px 0; /* Adjust margin to ensure no overlap */
                }
            }

            .content {
                display: none;
            }
        }
        .content {
            width: 100%;
            height: calc(100% - 45px);
            overflow-y: scroll;

            &::-webkit-scrollbar {
                width: 3px; /* Width of the scrollbar */
                height: 3px;
            }

            &::-webkit-scrollbar-track {
                // background: var(--ui-element-ternary-color); /* Background of the scrollbar track */
            }

            &::-webkit-scrollbar-thumb {
                border-radius: 50px;
                background: gray; /* Background of the scrollbar thumb */
            }

            &::-webkit-scrollbar-thumb:hover {
                background: #555; /* Background of the scrollbar thumb when hovered */
            }

            & > div {
                // padding: 1rem;
                display: none;
            }
            .active {
                width: 100%;
                height: 100%;
                display: block;
            }
            .question {
                min-width: 250px;
                padding-right: 1rem;
                padding-bottom: 2rem;
                margin-top: 2rem;
                padding-left: 1.5rem;
                .title p {
                    font-weight: 500;
                    margin-bottom: 1.5rem;
                    font-size: 1.4rem;
                }
                .description {
                    white-space: pre-wrap;
                    color: var(--text-color);
                    font-weight: 400;
                    font-size: 1.05rem;
                    opacity: 0.75;
                }
                .image {
                    width: 25rem;
                    height: 18rem;
                    margin-top: 1rem;
                    img {
                        object-position: left;
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                }
                .sampleTestCases {
                    margin-top: 4rem;
                    h3 {
                        font-size: 1.3rem;
                        font-weight: 400;
                        margin-bottom: 1rem;
                    }
                    .testcases {
                        margin-bottom: 2rem;
                        p {
                            margin-top: 1rem;
                            color: var(--text-color);
                        }
                        div {
                            border-radius: 3px;
                            margin-top: 0.6rem;
                            padding: 0.5rem 1rem;
                            width: 100%;
                            background-color: var(--ui-element-color);
                            pre {
                                color: var(--text-color);
                                opacity: 0.6;
                                line-height: 1.4rem;
                                margin-top: 0;
                            }
                        }
                    }
                }
                .inputFormat,
                .outputFormat {
                    margin-top: 0.5rem;
                    padding: 0.5rem 1rem;
                    background-color: rgb(17, 17, 17);
                    border-radius: 4px;
                    font-size: 0.85rem;
                    line-height: 1.2rem;
                    pre {
                        margin-top: 0;
                        white-space: pre-wrap;
                    }
                }
                .inputFormat {
                    margin-bottom: 1rem;
                }
                .explaination {
                    margin-top: 2rem;
                    h3 {
                        font-size: 1.3rem;
                        font-weight: 400;
                        margin-bottom: 1rem;
                    }
                    pre {
                        white-space: pre-wrap;
                        opacity: 0.75;
                        margin-top: 0.5rem;
                        color: var(--text-color);
                    }
                }
                .constrains {
                    margin-top: 2rem;
                    padding-bottom: 2rem;
                    h3 {
                        font-size: 1.3rem;
                        font-weight: 400;
                        margin-bottom: 1rem;
                    }
                    ul {
                        padding-left: 1.5rem;
                        li {
                            opacity: 0.75;
                            color: var(--text-color);
                            margin-top: 0.5rem;
                        }
                    }
                }
            }
            .submission {
                .head {
                    width: 100%;
                    padding: 1rem 1rem;
                    border-bottom: 1px solid #444;
                    display: flex;
                    p {
                        width: 20%;
                        min-width: 67px;
                        color: #797979;
                        text-align: center;

                        &:nth-child(1) {
                            text-align: left;
                            min-width: 80px;
                            width: 20%;
                        }
                    }
                }
                .allSubmissions {
                    width: 100%;
                    .loader {
                        --uib-size: 25px;
                        margin: auto;
                        margin-top: 2rem;
                    }
                    .result {
                        width: 100%;
                        padding: 1rem 1rem;
                        border-bottom: 1px solid #8181811f;
                        display: flex;
                        align-items: center;
                        transition: 0.2s;
                        &:hover {
                            background-color: #222222;
                        }
                        small {
                            display: block;
                        }
                        div {
                            min-width: 80px;
                            width: 20%;
                        }
                        p {
                            width: 20%;
                            color: var(--text-color);
                            min-width: 67px;
                            white-space: nowrap;
                            text-align: center;
                            &:nth-child(3) {
                                font-size: 0.9rem;
                            }
                            &.correct {
                                color: #4cac5e;
                            }
                            &.incorrect {
                                color: #d13e40;
                            }
                            &:nth-child(1) {
                                margin-bottom: 0.2rem;
                                text-align: left;
                                min-width: 80px;
                                font-size: 1.1rem;
                                font-weight: 400;
                            }
                        }
                        small {
                            white-space: nowrap;
                            color: var(--text-color);
                        }
                    }
                }
            }
            .solution {
                padding: 1rem;
                width: 100%;
                height: 100%;
                .solution-tabs {
                    display: flex;
                    justify-content: flex-start;
                    width: 100%;
                    margin-bottom: 10px;
                    border-bottom: 2px solid rgb(60, 60, 60);
                    padding: 0;

                }

                .solution-tab-btn {
                    width: fit-content;
                    padding: 6px 20px;
                    background: transparent;
                    border: none;
                    cursor: pointer;
                    &:nth-child(1) {
                        border-right: 1px solid rgb(60, 60, 60);
                    }
                    &:nth-child(2) {
                        border-right: 1px solid rgb(60, 60, 60);
                    }
                    &:nth-child(3) {
                        border-right: 1px solid rgb(60, 60, 60);
                    }
                    
                }

                .solution-tab-btn.active {
                    background: rgb(60, 60, 60);
                    color: white;
                }
                #solutionEditor {
                    width: 100%;
                    height: 100%;
                    .ace_editor .ace_cursor {
                        display: none !important;
                    }
                }
            }
            .review {
                .reviewEditor {
                    padding: 1.5rem;
                    ul {
                        margin-left: 1.5rem;
                        margin-bottom: 1rem;
                    }
                    h2 {
                        margin-bottom: 1rem;
                    }
                    p {
                        margin-bottom: 0.8rem;
                    }
                    .reviewLoader {
                        margin-top: 2rem;
                        margin: auto;
                        margin-top: 2rem;
                        width: 25px;
                        padding: 0 !important;
                        height: 25px;
                        border: 2px solid transparent;
                        border-top: 2px solid var(--text-color);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                }
            }
        }
    }
    .verticalResizer {
        width: 10px;
        height: 95%;
        border-radius: 10px;
        transition: 0.2s;
        margin: 0.1rem 0;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: col-resize;
        .line {
            background-color: var(--ui-element-ternary-color);
            width: 2px;
            height: 30px;
            transition: 0.1s;
        }
        &:active .line {
            height: 100%;
            background-color: #227aff;
        }
        &:hover .line {
            height: 100%;
            background-color: #227aff;
        }
    }
    .right {
        min-width: 40px;
        width: 60%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        .horizontalResizer {
            width: 95%;
            height: 8px;
            border-radius: 10px;
            transition: 0.2s;
            margin: 0.1rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: row-resize;
            .line {
                background-color: var(--ui-element-ternary-color);
                width: 30px;
                height: 2px;
                transition: 0.1s;
            }
            &:active .line {
                width: 100%;
                background-color: #227aff;
            }
            &:hover .line {
                width: 100%;
                background-color: #227aff;
            }
        }
        .right-top {
            min-height: 44px;
            overflow: hidden;
            border-radius: 8px;
            width: 100%;
            height: 70%;
            background-color: var(--ui-element-secondary-color);
            border: 1px solid transparent;
            //   transition: 0.2s;
            &.onlyNav {
                width: 40px;
                .heading {
                    padding: 0.5rem 0;
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    height: 100%;

                    .editor-left {
                        display: flex;
                        align-items: center;
                        transform: rotate(90deg);
                        // transform-origin: left center; /* Rotate around the left side */
                        white-space: nowrap; /* Prevent text from wrapping */
                        margin: 20px 0; /* Adjust margin to ensure no overlap */
                    }
                    .editor-right {
                        display: none;
                    }
                }

                .language_topic {
                    display: none;
                }
                #editor {
                    display: none;
                }
            }
            .editor-left {
                div {
                    display: flex;
                    gap: 0.5rem;
                    p {
                        opacity: 1;
                        color: var(--text-color);
                    }
                }
            }
            .editor-right {
                position: relative;
                .mergeBtn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    transition: 0.2s;
                    position: absolute;
                    background-color: var(--ui-element-ternary-color);
                    z-index: 2;
                    right: 0;
                    width: 170px;
                    // text-align: center;
                    padding: 0.5rem;
                    border-radius: 5px;
                    opacity: 0;
                    transform: translateX(-200px);
                    pointer-events: none;
                }
            }
            .heading {
                padding-right: 0.5rem;
                display: flex;
                justify-content: space-between;
                .resetBtn {
                    background-color: rgb(67, 67, 67);
                }
                button {
                    display: flex;
                    gap: 0.5rem;
                    align-items: center;
                    background-color: var(--ui-element-ternary-color);
                    border: none;
                    padding: 0.4rem 1rem;
                    color: var(--text-color);
                    border-radius: 4px;
                    border: 1px solid transparent;
                    transition: 0.2s;
                    .ri-play-fill {
                        font-size: 1.2rem;
                        color: #227aff;
                    }
                    .ri-logout-box-r-line {
                        font-size: 1.1rem;
                        color: #828282;
                    }
                    &:hover {
                        border: 1px solid #7e7e7e;
                        color: var(--text-color);
                    }
                }
            }
            .language_topic {
                height: 35px;
                padding: 0.8rem;
                display: flex;
                align-items: center;
                gap: 0.7rem;
                border-bottom: 1px solid var(--ui-element-ternary-color);
                p {
                    color: #666;
                }
                #languageSelect {
                    background-color: transparent;
                    border: none;
                    color: var(--text-color);
                    font-size: 1rem;
                    font-weight: 500;
                    cursor: pointer;
                    padding: 0.2rem 0.5rem;
                    border-radius: 5px;
                    transition: 0.2s;
                    &:hover {
                        background-color: var(--ui-element-ternary-color);
                        color: var(--text-color);
                    }
                }
                .line {
                    width: 1px;
                    height: 15px;
                    background-color: #666;
                }
            }
            #editor {
                margin: 0.5rem 0;
                font-family: monospace !important;
                width: 100%;
                height: calc(100% - (35px + 44px + 1rem));
                // background-color: green;
            }
        }
        .right-bottom {
            min-height: 44px;
            overflow: hidden;
            border-radius: 8px;
            width: 100%;
            height: 30%;
            background-color: var(--ui-element-secondary-color);
            border: 1px solid transparent;
            .heading {
                display: flex;
                justify-content: space-between;
                .button.discord {
                    font-size: 0.9rem;
                    padding: 0.3rem 1rem;
                    border-radius: 3px;
                }
                .nextButton {
                    border-radius: 3px;
                    padding: 0.3rem 1rem;
                    background-color: #10a37f;
                    border: none;
                    color: black;
                }
            }
            &.onlyNav {
                width: 40px;
                .heading {
                    padding: 2rem 0;
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    height: 100%;
                    .button {
                        display: none;
                    }
                    div {
                        display: flex;
                        align-items: center;
                        transform: rotate(90deg);
                        // transform-origin: left center; /* Rotate around the left side */
                        white-space: nowrap; /* Prevent text from wrapping */
                        margin: 10px 0; /* Adjust margin to ensure no overlap */
                        .testCaseLoader {
                            display: none;
                        }
                    }
                }
                .cases,
                .selectedCase {
                    display: none;
                }
            }
            .output {
                width: 100%;
                height: calc(100% - 30px);
                overflow: scroll;
                .result {
                    display: none;
                    font-size: 1.1rem;
                    padding: 1rem;
                    gap: 2rem;
                    align-items: center;
                    small {
                        opacity: 0.4;
                    }
                    h2 {
                        font-weight: 400;
                        &.accepted {
                            color: #00d82f;
                        }
                        &.rejected {
                            color: var(--error);
                        }
                    }
                }

                .cases {
                    display: none;
                    // display: flex;
                    overflow-x: scroll;
                    gap: 1rem;
                    // height: 60px;
                    padding: 1rem 1rem;
                    // &.correct{
                    // }
                    p {
                        white-space: nowrap;
                        transition: 0.2s;
                        padding: 0.5rem 1rem;
                        padding-left: 1.5rem;
                        border-radius: 5px;
                        font-size: 1rem;
                        color: #737373;
                        position: relative;
                        &::after {
                            content: " ";
                            position: absolute;
                            left: 10px;
                            top: 49%;
                            z-index: 5;
                            width: 5px;
                            height: 5px;
                            border-radius: 50%;
                            transform: translateY(-50%);
                        }
                        &.correct::after {
                            background-color: #00d82f;
                        }
                        &.wrong::after {
                            background-color: var(--error);
                        }
                        &.active {
                            color: var(--text-color);
                            background-color: var(--ui-element-color);
                        }
                        &:hover {
                            color: var(--text-color);
                            background-color: var(--ui-element-color);
                        }
                    }
                    .skeleton {
                        background-color: var(--ui-element-color);
                        width: 80px;
                        height: 30px;
                        border-radius: 5px;
                        animation: skeletonLoading 1.5s infinite ease-in-out;
                    }
                }
                .empty {
                    height: 100%;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    p {
                        padding: 0 3rem;
                        text-align: center;
                        color: var(--fuscous-gray);

                        &.error {
                            color: var(--error);
                        }
                    }
                }
                .selectedCase {
                    display: none;
                    // overflow: scroll;
                    width: 100%;
                    height: calc(100%);
                    padding: 1rem;
                    &::-webkit-scrollbar {
                        display: none;
                    }
                    & > div {
                        display: none;
                        width: 100%;
                        &.active {
                            display: block;
                        }
                        div {
                            margin-bottom: 2rem;
                            &.error {
                                p {
                                    font-size: 1.5rem;
                                    margin-bottom: 0.3rem;
                                    color: var(--error);
                                }
                                module {
                                    color: var(--error);
                                }
                                small {
                                    font-size: 1rem;
                                    color: var(--text-color);
                                    opacity: 0.6;
                                    display: block;
                                    margin-bottom: 1rem;
                                }
                                pre {
                                    overflow-x: auto;
                                    padding: 1rem;
                                    border-radius: 5px;
                                    background-color: #e74d3c1f;
                                    color: var(--error);
                                }
                            }
                            p {
                                opacity: 0.7;
                                color: var(--text-color);
                                font-size: 1.1rem;
                                margin-bottom: 0.5rem;
                                &.accepted {
                                    color: #00d82f;
                                }
                                &.rejected {
                                    color: #a42d18;
                                }
                            }

                            pre {
                                overflow-x: auto;
                                width: 100%;
                                min-height: 30px;
                                opacity: 0.7;
                                padding: 0.5rem 1rem;
                                background-color: var(--ui-element-color);
                                font-size: 1.1rem;
                            }
                        }
                        &.rejected {
                            div:nth-child(2) {
                                pre {
                                    color: #e74c3c;
                                }
                            }
                            div:nth-child(3) {
                                pre {
                                    color: #00d82f;
                                }
                            }
                        }
                        // &.accepted{
                        //     pre{
                        //         color: #00d82f;
                        //     }
                        // }
                    }
                    .skeleton {
                        display: block;
                        p {
                            width: 100px;
                            height: 15px;
                            background-color: var(--ui-element-color);
                            animation: skeletonLoading 1.5s infinite ease-in-out;
                        }
                        pre {
                            animation: skeletonLoading 1.5s infinite ease-in-out;
                            background-color: var(--ui-element-color);
                            width: 100%;
                            height: 25px;
                        }
                    }
                }
            }
        }
    }
}

/* HTML: <div class="loader"></div> */
.testCaseLoader {
    width: 18px;
    padding: 0 !important;
    height: 18px;
    border: 2px solid transparent;
    border-top: 2px solid var(--text-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: none;
}
@media (max-width: 600px) {
    .code {
        .left {
            width: 100%;
            .heading {
                div:nth-child(2) {
                    display: none;
                }
                div:nth-child(3) {
                    display: none;
                }
            }
        }
        .verticalResizer {
            display: none;
        }
        .right {
            display: none;
        }
    }
}
.superscript {
    vertical-align: super;
    font-size: smaller;
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes skeletonLoading {
    0% {
        background-color: var(--ui-element-color); /* Dark gray */
    }
    50% {
        background-color: var(--ui-element-secondary-color); /* Light gray */
    }
    100% {
        background-color: var(--ui-element-color); /* Dark gray */
    }
}

.dotLoader {
    --uib-size: 18px;
    --uib-color: var(--text-color);
    --uib-speed: 1s;
    --uib-dot-size: calc(var(--uib-size) * 0.4);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: var(--uib-dot-size);
    width: var(--uib-size);
}

.dotLoader::before,
.dotLoader::after {
    content: "";
    position: absolute;
    height: var(--uib-dot-size);
    width: var(--uib-dot-size);
    border-radius: 50%;
    background-color: var(--uib-color);
    flex-shrink: 0;
    transition: background-color 0.3s ease;
}

.dotLoader::before {
    animation: orbit var(--uib-speed) linear infinite;
}

.dotLoader::after {
    animation: orbit var(--uib-speed) linear calc(var(--uib-speed) / -2) infinite;
}

@keyframes orbit {
    0% {
        transform: translateX(calc(var(--uib-size) * 0.25)) scale(0.73684);
        opacity: 0.65;
    }
    5% {
        transform: translateX(calc(var(--uib-size) * 0.235)) scale(0.684208);
        opacity: 0.58;
    }
    10% {
        transform: translateX(calc(var(--uib-size) * 0.182)) scale(0.631576);
        opacity: 0.51;
    }
    15% {
        transform: translateX(calc(var(--uib-size) * 0.129)) scale(0.578944);
        opacity: 0.44;
    }
    20% {
        transform: translateX(calc(var(--uib-size) * 0.076)) scale(0.526312);
        opacity: 0.37;
    }
    25% {
        transform: translateX(0%) scale(0.47368);
        opacity: 0.3;
    }
    30% {
        transform: translateX(calc(var(--uib-size) * -0.076)) scale(0.526312);
        opacity: 0.37;
    }
    35% {
        transform: translateX(calc(var(--uib-size) * -0.129)) scale(0.578944);
        opacity: 0.44;
    }
    40% {
        transform: translateX(calc(var(--uib-size) * -0.182)) scale(0.631576);
        opacity: 0.51;
    }
    45% {
        transform: translateX(calc(var(--uib-size) * -0.235)) scale(0.684208);
        opacity: 0.58;
    }
    50% {
        transform: translateX(calc(var(--uib-size) * -0.25)) scale(0.73684);
        opacity: 0.65;
    }
    55% {
        transform: translateX(calc(var(--uib-size) * -0.235)) scale(0.789472);
        opacity: 0.72;
    }
    60% {
        transform: translateX(calc(var(--uib-size) * -0.182)) scale(0.842104);
        opacity: 0.79;
    }
    65% {
        transform: translateX(calc(var(--uib-size) * -0.129)) scale(0.894736);
        opacity: 0.86;
    }
    70% {
        transform: translateX(calc(var(--uib-size) * -0.076)) scale(0.947368);
        opacity: 0.93;
    }
    75% {
        transform: translateX(0%) scale(1);
        opacity: 1;
    }
    80% {
        transform: translateX(calc(var(--uib-size) * 0.076)) scale(0.947368);
        opacity: 0.93;
    }
    85% {
        transform: translateX(calc(var(--uib-size) * 0.129)) scale(0.894736);
        opacity: 0.86;
    }
    90% {
        transform: translateX(calc(var(--uib-size) * 0.182)) scale(0.842104);
        opacity: 0.79;
    }
    95% {
        transform: translateX(calc(var(--uib-size) * 0.235)) scale(0.789472);
        opacity: 0.72;
    }
    100% {
        transform: translateX(calc(var(--uib-size) * 0.25)) scale(0.73684);
        opacity: 0.65;
    }
}
