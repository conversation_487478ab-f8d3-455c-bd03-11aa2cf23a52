@charset "UTF-8";
.src-submissions {
    font-family: "gilroy", -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    color: #2d3748;
    background: #f8fafc;
    min-height: 100vh;
}
.src-submissions__header {
    margin-bottom: 30px;
}
.src-submissions__header h1 {
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 2.5rem; /* Consider slightly reducing if too large */
    font-weight: 700; /* Maintained for strong heading */
}
.src-submissions__header p {
    color: #718096;
    font-size: 1.1rem; /* Good size for a subtitle */
    font-weight: 400; /* Standard weight for readability */
}
.src-submissions__filters {
    background: #ffffff; /* Changed to white */
    padding: 25px;
    border-radius: 10px; /* Softer radius */
    margin-bottom: 35px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* Softer shadow */
}
.src-submissions__filters-title {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 600; /* Slightly bolder for emphasis */
}
.src-submissions__filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px; /* Increased gap */
    margin-bottom: 20px;
}
.src-submissions__filters-group label {
    display: block;
    margin-bottom: 5px;
    color: #4a5568;
    font-weight: 500; /* Medium weight for labels */
    font-size: 0.9rem;
}
.src-submissions__filters-group select,
.src-submissions__filters-group input {
    width: 100%;
    padding: 10px 14px; /* Increased padding */
    border: 1px solid #d1d5db; /* Lighter border */
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.2s;
}
.src-submissions__filters-group select:focus,
.src-submissions__filters-group input:focus {
    outline: none;
    border-color: #2563eb; /* Adjusted focus color */
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
}
.src-submissions__filters-actions {
    display: flex;
    gap: 10px;
}
.src-submissions__filters-actions button {
    padding: 10px 22px; /* Adjusted padding */
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}
.src-submissions__filters-actions button.apply {
    background: #2563eb; /* Adjusted blue */
    color: white;
}
.src-submissions__filters-actions button.apply:hover {
    background: #1d4ed8; /* Darker blue for hover */
}
.src-submissions__filters-actions button.clear {
    background: #e2e8f0;
    color: #4a5568;
}
.src-submissions__filters-actions button.clear:hover {
    background: #cbd5e0;
}
.src-submissions__table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}
.src-submissions__table {
    width: 100%;
    border-collapse: collapse;
}
.src-submissions__table thead {
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
}
.src-submissions__table thead th {
    padding: 16px 12px;
    text-align: left;
    font-weight: 600;
    color: #2d3748;
    border-bottom: none;
    cursor: pointer;
    position: relative;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.2s ease;
}
.src-submissions__table thead th:hover {
    background: #edf2f7;
    color: #1a202c;
}
.src-submissions__table thead th.sortable::after {
    content: "↕";
    position: absolute;
    right: 8px;
    opacity: 0.5;
}
.src-submissions__table thead th.sort-asc::after {
    content: "↑";
    opacity: 1;
}
.src-submissions__table thead th.sort-desc::after {
    content: "↓";
    opacity: 1;
}
.src-submissions__table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e2e8f0;
}
.src-submissions__table tbody tr:hover {
    background: #f7fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}
.src-submissions__table tbody tr:nth-child(even) {
    background: #f9fafb;
}
.src-submissions__table tbody tr:nth-child(even):hover {
    background: #f1f5f9;
}
.src-submissions__table tbody tr td {
    padding: 14px 12px;
    border-bottom: 1px solid #e2e8f0;
    color: #4a5568;
    font-weight: 400;
    vertical-align: top;
    position: relative;
}
.src-submissions__status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500; /* Medium weight for status badges */
    text-transform: capitalize;
}
.src-submissions__status--pending {
    background: #fef5e7;
    color: #d69e2e;
}
.src-submissions__status--good-fit {
    background: #e6fffa;
    color: #319795;
}
.src-submissions__status--best-fit {
    background: #e6ffed;
    color: #38a169;
}
.src-submissions__status--not-fit {
    background: #fed7d7;
    color: #e53e3e;
}
.src-submissions__status--accepted {
    background: #e6ffed;
    color: #38a169;
}
.src-submissions__status--rejected {
    background: #fed7d7;
    color: #e53e3e;
}
.src-submissions__pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #ffffff;
    border-top: 1px solid #e2e8f0;
}
.src-submissions__pagination-info {
    color: #718096;
    font-size: 0.875rem;
    font-weight: 500;
}
.src-submissions__pagination-controls {
    display: flex;
    gap: 6px;
    align-items: center;
}
/* Remove old pagination button styles - they're replaced by the new .pagination-button class */
.src-submissions__loading {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
    font-size: 1rem;
    background: white;
    border-radius: 8px;
    margin: 20px 0;
}
.src-submissions__loading::before {
    content: "";
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #3182ce;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
    vertical-align: middle;
}
.src-submissions__error {
    background: #fed7d7;
    color: #c53030;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    border: 1px solid #feb2b2;
    font-weight: 500;
}
.src-submissions__error::before {
    content: "⚠️";
    margin-right: 8px;
}
.src-submissions__empty {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
    font-size: 1rem;
    background: white;
    border-radius: 8px;
    margin: 20px 0;
    border: 2px dashed #e2e8f0;
}
.src-submissions__empty::before {
    content: "📊";
    display: block;
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}
/* Improved table scrolling on mobile */
@media (max-width: 768px) {
    .src-submissions__table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .src-submissions__table {
        min-width: 800px;
        font-size: 0.875rem;
    }

    .src-submissions__table thead th,
    .src-submissions__table tbody td {
        padding: 10px 8px;
        white-space: nowrap;
    }

    .src-submissions__table thead th {
        font-size: 0.8rem;
    }
}
/* Focus improvements for accessibility */
.src-submissions__table thead th:focus,
.src-submissions__filters-group select:focus,
.src-submissions__filters-group input:focus,
.pagination-button:focus,
.button-primary:focus {
    outline: 2px solid #3182ce;
    outline-offset: 2px;
}
/* Smooth transitions for all interactive elements */
* {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
/* Clean spacing utilities */
.mb-2 {
    margin-bottom: 8px;
}
.mb-3 {
    margin-bottom: 12px;
}
.mb-4 {
    margin-bottom: 16px;
}
.mt-2 {
    margin-top: 8px;
}
.mt-3 {
    margin-top: 12px;
}
.mt-4 {
    margin-top: 16px;
}
/* Text utilities */
.text-sm {
    font-size: 0.875rem;
}
.text-xs {
    font-size: 0.75rem;
}
.font-medium {
    font-weight: 500;
}
.font-semibold {
    font-weight: 600;
}
.text-gray-500 {
    color: #718096;
}
.text-gray-700 {
    color: #4a5568;
}
/* Clean card styling for consistency */
.card {
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

.card-padding {
    padding: 20px;
}
/* Improved visual hierarchy */
.src-submissions__main-content {
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}
/* Enhanced sort indicators */
.src-submissions__table thead th.sortable::after {
    content: "↕";
    position: absolute;
    right: 8px;
    opacity: 0.3;
    font-size: 0.875rem;
    transition: opacity 0.2s ease;
}

.src-submissions__table thead th.sortable:hover::after {
    opacity: 0.6;
}

.src-submissions__table thead th.sort-asc::after {
    content: "↑";
    opacity: 1;
    color: #3182ce;
}

.src-submissions__table thead th.sort-desc::after {
    content: "↓";
    opacity: 1;
    color: #3182ce;
}
/* Clean scrollbar styling */
.src-submissions__table-container::-webkit-scrollbar {
    height: 8px;
}

.src-submissions__table-container::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.src-submissions__table-container::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

.src-submissions__table-container::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}
/* Print styles */
@media print {
    .src-submissions__filters,
    .src-submissions__pagination,
    .table-actions {
        display: none !important;
    }

    .src-submissions {
        padding: 0;
        background: white;
    }

    .src-submissions__table {
        font-size: 12px;
    }

    .table-row-top-submission {
        background: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}
