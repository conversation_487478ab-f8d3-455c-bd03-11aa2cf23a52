@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/<PERSON><PERSON>-Extrabold_YC6oQDa3Ix.ttf?updatedAt=1697720439904");
    font-family: "<PERSON><PERSON>";
    font-weight: 700;
}

@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/<PERSON><PERSON>-Bold_JK4cTGJOE.ttf?updatedAt=1697720175118");
    font-family: "<PERSON>roy";
    font-weight: 600;
}

@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/Gilroy-Semibold_zTZ2PgJOF.ttf?updatedAt=1697720387357");
    font-family: "Gilroy";
    font-weight: 500;
}

@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/<PERSON><PERSON>-Medium_eHDK2RllDd.ttf?updatedAt=1697720216741");
    font-family: "<PERSON><PERSON>";
    font-weight: 400;
}

@font-face {
    src: url("https://ik.imagekit.io/sheryians/Fonts/Gilroy-UltraLight_DLYQkGL0g.ttf?updatedAt=1705175454233");
    font-family: "Gilroy";
    font-weight: 100;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Regular_5Mzhp8KlA8.ttf?updatedAt=1709189838954');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Thin_J0zaDW5kUV.ttf?updatedAt=1709189838905');
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Light_M4nzWhve6k.ttf?updatedAt=1709189838276');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Medium_2wEhlmo-TD.ttf?updatedAt=1709189838650');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Bold_VMHV5twUMR.ttf?updatedAt=1709189835487');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Hairline_9Jz6JFa3S.ttf?updatedAt=1709189838405');
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-LightIta_mLZfHYSDzL.ttf?updatedAt=1709189838941');
    font-weight: 300;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-RegIta_ZQZ0ISuECo.ttf?updatedAt=1709189838814');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ThinIta__6kihetaOz.ttf?updatedAt=1709189838563');
    font-weight: 100;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-MedIta_s4wTiA5al.ttf?updatedAt=1709189838311');
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-HairlineIta_UXSDxUaUw.ttf?updatedAt=1709189838597');
    font-weight: 100;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/Helvetica_NTUXwLmxSC.ttf?updatedAt=1708769204058');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtBlkIta_f0giU-O0Yd.ttf?updatedAt=1709189835297');
    font-weight: 900;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-BoldIta_O5gX7g1L5.ttf?updatedAt=1709189835238');
    font-weight: bold;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Black_-pjI4fOp4E.ttf?updatedAt=1709189835338');
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-BlackIta_y1WeOftIb.ttf?updatedAt=1709189835136');
    font-weight: 900;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtBlk_yunH-AhU2b.ttf?updatedAt=1709189835336');
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtLtIta_GJzq7onfxt.ttf?updatedAt=1709189835244');
    font-weight: 200;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtBdIta__MPkMs25CD.ttf?updatedAt=1709189835153');
    font-weight: 800;
    font-style: italic;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtraBold_Qqt_sGOvuc.ttf?updatedAt=1709189835200');
    font-weight: 800;
    font-style: normal;
}

@font-face {
    font-family: 'Helvetica';
    src: url('https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtLt_R50yORjQ9.ttf?updatedAt=1709189835320');
    font-weight: 200;
    font-style: normal;
}

@font-face {
    font-family: 'Mauline';
    src: url('https://ik.imagekit.io/sheryians/Fonts/Mauline/Mauline_Nl5uNkMO4.ttf');
    font-style: normal;
}

@font-face {
    font-family: 'Bellina';
    src: url('https://ik.imagekit.io/sheryians/Fonts/Bellina/Bellina%20Slant%20Condensed_OesY1bYek.ttf');
    font-style: normal;
}


:root {
    --primary: #10a37f;
    --placementPrimary: #02e797;
    --secondary: #009560;
    --white: #fff;
    --primaryLight: #F8F8F8;
    --primaryDark: #0c0c0c;
    --secondaryLight: #dbdbdb;
    --activeDark: #212121;
    --secondaryLightActive: #cbcbcb;
    --secondaryDark: #ececec;
    --secondaryDarkShadow: rgba(0, 0, 0, 0.16);
    --textColor: #7BBA81;
    --special: #d2af7a;
    --ternary: #c4c9d3;
    --error: #e74c3c;
    --maxPageWidth: 110rem;
    --discord: #7b40ff;
    --link: rgb(0, 71, 125);
}

* {
    margin: 0%;
    padding: 0%;
    box-sizing: border-box;
    font-family: Helvetica;
    user-select: none;
    text-rendering: optimizeSpeed;
}

.highlight {
    color: var(--placementPrimary);
}

html,
body {
    height: 100%;
    width: 100%;
    scroll-behavior: smooth;
}

html {
    font-size: clamp(14px, 1.06vw, 19px);
}

.feather {
    width: 24px;
    height: 24px;
    stroke: currentColor;
    stroke-width: 3.5;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
}

#main {

    .cta-syllabus {

        display: flex;
        justify-content: center;
        align-items: center;


        .buy,
        .buy-now {
            width: 100%;
            display: flex;
            align-items: center;
            flex-direction: column;
            font-family: Helvetica;
            background-color: var(--secondary);
            font-weight: 500;
            text-align: center;
            color: var(--white);
            font-size: 1.5em;
            text-transform: capitalize;
            cursor: pointer;
            gap: 1em;
            padding: 0.8em;
            border-radius: 0.4rem;
            max-width: 30rem;
        }

    }

    .page {
        padding: 5vh 4vw;
    }

    #page1 {
        padding-top: 13vh;
        display: flex;
        width: 100%;
        justify-content: space-between;
        padding-bottom: 0;
        color: var(--primaryLight);
        min-height: min(100vh, 760px);
        padding-top: min(20vh, 10rem);
        gap: 3rem;

        .left {
            width: 50%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 1.5rem;

            .title {
                width: 90%;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;

                h1 {
                    font-size: 3.5em;
                    line-height: 1em;
                    font-weight: 600;
                    letter-spacing: -0.02em;
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;


                    .bloomImages {
                        display: flex;
                        flex-direction: row;
                        justify-content: flex-start;
                        width: fit-content;

                        .bloomImage {
                            width: 3rem;
                            position: relative;
                            aspect-ratio: 1/1;
                            isolation: isolate;

                            img,
                            picture {
                                width: 100%;
                                height: 100%;
                                aspect-ratio: 1/1;
                                object-fit: contain;
                                object-position: center;
                            }

                            .bloom {
                                position: absolute;
                                top: 0;
                                left: 0;
                                // z-index: -1;
                                filter: blur(10px) brightness(1.2);
                            }

                        }
                    }
                }



                .numEnrollStudent {
                    // color: white;
                    // background-color: black;
                    // padding: 5px;
                    width: fit-content;
                    font-size: 1em;
                    font-weight: 600;
                    gap: 0.6em;
                    align-items: center;
                    text-transform: capitalize;
                    color: var(--primary);

                    span {
                        opacity: 0.5;
                        color: var(--primaryDark);
                    }

                    &:hover {
                        i {
                            display: initial;
                        }
                    }

                    i {
                        font-size: 1em;
                        display: none;
                    }

                    .num {
                        font-size: 1.1em;
                    }
                }

                .mask-bg {
                    background-image: url("https://ik.imagekit.io/sheryians/three.js/mask_5gcMWG8mG.jpg");
                    background-size: cover;
                    background-position: top left;
                    background-clip: text;
                    position: relative;
                    isolation: isolate;

                    span {
                        color: transparent;
                    }


                    &:not(:has(.bloom)) {
                        background-image: none;

                        span {

                            color: #fff !important;
                        }

                    }

                    .bloom {
                        position: absolute;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        z-index: -1;
                        filter: blur(20px);
                        background-image: url("https://ik.imagekit.io/sheryians/three.js/mask_5gcMWG8mG.jpg");
                        background-size: cover;
                        background-position: center;
                        opacity: 1;
                        background-clip: text;
                        color: transparent;
                    }
                }
            }

            .tags {
                display: flex;
                align-items: center;
                gap: 1.5em;
                flex-wrap: nowrap;

                .tag {
                    flex-shrink: 0;
                    font-size: 1.1em;
                    font-weight: 600;
                    text-transform: capitalize;
                    padding: 0.65em 1.5em;
                    border-radius: 5px;
                    background-color: var(--activeDark);

                    // @media (prefers-color-scheme:light) {
                    //     color: white;
                    // }
                }
            }

            .activeSupport {
                font-size: 1em;
                font-weight: 600;
                text-transform: capitalize;
                padding: 0.7em 1.3em;
                border-radius: 5px;
                background-color: #353535;
                display: flex;
                align-items: center;
                gap: 1em;

                // @media (prefers-color-scheme:light) {
                //     color: white;
                // }

                img {
                    height: 1.5em;
                    width: 1.5em;
                    object-fit: cover;
                }
            }

            .instructor {
                display: flex;
                align-items: center;
                justify-content: space-between;
                background-color: var(--secondaryLight);
                padding: 0.4rem 0.8rem;
                width: 50%;
                color: var(--primaryDark);
                border-radius: 4px;

                p {
                    font-weight: 500;
                    font-size: 0.9rem;
                    opacity: 0.6;
                    text-transform: capitalize;
                }

                h1 {
                    text-transform: capitalize;
                    font-size: 0.9rem;
                    font-weight: 600;
                }
            }

            .price {
                align-items: center;
                margin-top: 1.5em;

                * {
                    font-family: "Gilroy";
                }

                p {
                    font-size: 2.5em;
                    font-weight: 600;
                    text-transform: capitalize;
                }

                .priceValue {
                    color: var(--placementPrimary);
                    font-size: 0.83em;

                    span {
                        color: var(--placementPrimary);

                        // @media (prefers-color-scheme:light) {
                        //     color: var(--primary);
                        // }
                    }

                    // @media (prefers-color-scheme:light) {
                    //     color: var(--primary);
                    // }
                }

                .originalValue {
                    font-size: 0.73em;
                    position: relative;
                    color: var(--primaryLight);

                    &::after {
                        content: "";
                        width: 110%;
                        height: max(2.5px, 0.07em);
                        background-color: var(--error);
                        position: absolute;
                        transform: translate(-50%, -50%);
                        top: 50%;
                        left: 50%;

                        // @media (prefers-color-scheme:light) {
                        //     background-color: var(--primaryDark);
                        // }

                    }

                    // @media (prefers-color-scheme:light) {
                    //     color: var(--primaryDark);
                    //     ;
                    // }
                }

                .discount {
                    font-size: 0.45em;
                    font-weight: 400;
                }
            }

            .buy {
                display: flex;
                align-items: center;
                gap: 2em;

                .buyNow {
                    font-family: "Gilroy";
                    background-color: var(--secondary);
                    font-weight: 400;
                    color: var(--white);
                    padding: 0.8em 1.2em;
                    font-size: 1.5em;
                    text-transform: capitalize;
                    border-radius: 0.4rem;
                    cursor: pointer;
                    font-weight: bold;

                }

                .seatFull {
                    background-color: var(--error);
                    font-weight: 500;
                    color: var(--white);
                    padding: 0.6rem 1.2rem;
                    font-size: 1rem;
                    text-transform: capitalize;
                    border-radius: 1.5rem;
                    cursor: not-allowed;
                }

                .notify {
                    background-color: var(--primary);
                    font-weight: 500;
                    color: var(--white);
                    padding: 0.6rem 1.2rem;
                    font-size: 1rem;
                    box-shadow: none;
                    text-transform: capitalize;
                    border-radius: 1.5rem;
                    cursor: pointer;
                }

                .commingSoon {
                    background-color: var(--secondaryLight);
                    font-weight: 500;
                    color: var(--primaryDark);
                    padding: 0.6rem 1.2rem;
                    font-weight: 600;
                    opacity: 0.5;
                    font-size: 1rem;
                    text-transform: capitalize;
                    border-radius: 0.5rem;

                    &.seatLeft {
                        opacity: 1 !important;
                    }

                    span {
                        font-size: 1.1em;
                    }

                    .sign {
                        font-weight: 900;
                    }
                }

                .tryFree {
                    border-radius: 0.2rem;
                    background-color: var(--primaryDark);
                    font-weight: 400;
                    color: var(--white);
                    padding: 1.2rem 3rem;
                    font-size: 1.25rem;
                    text-transform: capitalize;
                    border-radius: 0.5rem;
                    cursor: pointer;
                }
            }

            .perks {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                margin-top: 2rem;
                gap: 1.5rem;
                justify-content: space-between;

                .perk {
                    width: 45%;
                    flex-shrink: 0;
                    gap: 0.8rem;


                    .name {
                        font-size: 1.05rem;
                        font-family: "Gilroy";

                        span {
                            font-weight: 600;
                        }

                        font-weight: 100;

                    }
                }
            }

            .description {
                width: 90%;
                font-weight: 400;
                opacity: 0.8;
                margin-top: 1rem;
                font-size: 1.3rem;
                line-height: 2.3rem;
            }
        }

        .right {
            width: 41rem;
            aspect-ratio: 1.42/1 !important;
            height: fit-content;
            display: flex;
            justify-content: center;
            position: relative;
            border-radius: 0.9rem;
            transition: 0.2s;
            overflow: hidden;
            cursor: pointer;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 15px;
                object-position: center;
            }

            #playOverlay {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 100%;
                height: 100%;
                display: flex;
                background-color: #1b1b1b;
                justify-content: center;
                align-items: center;

                i {
                    transition: 0.2s;
                    font-size: 5rem;
                    color: #a9a9a9;
                    cursor: pointer;
                    position: absolute;

                }

                &:hover i {
                    font-size: 6rem;
                }
            }

            .tags {
                position: absolute;
                top: 6%;
                right: 6%;

                .tag {
                    flex-shrink: 0;
                    font-size: 0.8rem;
                    font-weight: 700;
                    text-transform: uppercase;
                    padding: 0.4rem 0.8rem;
                    border-radius: 5px;
                    background-color: var(--background-color);


                }
            }

            #embedded-promo {
                width: 100%;
                height: 100%;
            }
        }
    }

    .page2 {
        color: var(--primaryLight);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: min(10vh, 7rem);
        padding: min(10vh, 7rem) 0;
        padding-top: 5vh;
        padding-left: 12vw;
        padding-right: 12vw;


        .text {
            display: flex;
            flex-direction: column;
            text-align: center;

            .poster-theme {
                max-width: 30rem;
                align-self: center;
                margin-bottom: 4rem;
            }



            * {
                text-transform: capitalize;
            }

            .maskText {
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                max-width: 900px;

                h1 {
                    background-image: url(https://ik.imagekit.io/sheryians/Aptitude%20&%20Reasoning/mask%20Large_ZB4lGN8hWZ.png);
                    background-position: 10% 100%;
                    background-clip: text;
                    color: transparent;
                    font-size: 10.5em;
                    letter-spacing: -0.05em;
                    text-transform: capitalize;
                    line-height: 1.15;
                    background-position: center;
                    background-size: contain;
                    width: fit-content !important;
                    align-self: center;
                }


            }

            .subHeading {
                text-transform: capitalize;
                font-size: 5em;
                font-weight: 600;
                letter-spacing: -0.06em;
                line-height: 0.5;
                position: relative;

                // * {
                //     font-family: "Mauline";
                // }

                .bloom {
                    filter: blur(30px);
                }

                .pointer {
                    position: absolute;
                    bottom: 60%;
                    transform: translateX(-50%);
                    left: 55%;
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    // align-items: center;

                    span {
                        font-size: 3.5rem;
                        font-weight: 100;
                        font-style: italic;
                        font-family: "Bellina";
                    }

                    img {
                        height: 2.5rem;
                        width: 2.5rem;
                        object-fit: cover;
                    }
                }

                span {
                    font-size: 0.3em;
                    font-weight: 500;
                    letter-spacing: -0.05em;
                }
            }


        }

        video,
        img {
            width: 100%;
            object-fit: cover;
        }

        isolation: isolate;

        .bloom {
            position: absolute;
            transform: translate(-50%, -50%) scale(1);
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            filter: blur(60px) brightness(1);
            z-index: -1;

            // opacity: 0.5;
        }
    }

    .page3 {
        padding-left: 15vw;
        padding-right: 15vw;
        color: var(--primaryLight);
        padding-top: 15vh;
        display: flex;
        flex-direction: column;
        gap: 5rem;


        .project {


            display: flex;
            gap: 2rem;
            flex-direction: column;

            .heading {

                display: flex;

                align-items: center;
                font-size: 0.9rem;

                .buffer {
                    height: 3em;
                    width: 0.7em;
                    background-color: #fbd11a;
                    border-radius: 0.3em;
                }

                h1 {
                    font-size: 1.5em;
                    font-weight: 200;
                    letter-spacing: -0.04em;
                    text-transform: capitalize;
                    background-color: #000000;
                    border-radius: 0.8rem;
                    padding: 0.25em 1em;
                    text-transform: uppercase;
                }
            }

            .sub-heading {
                font-size: 3em;
                font-weight: 500;
                letter-spacing: -0.02em;
                line-height: 1em;
                text-transform: capitalize;
            }

            &:nth-child(2) {
                margin-top: 3rem;
            }
        }

        .text {
            display: flex;
            flex-direction: column;


            h1 {
                font-size: 10.5em;
                text-transform: capitalize;
                letter-spacing: -0.04em;
                line-height: 1em;
                // white-space: nowrap;
            }

            .maskText {
                position: relative;

                color: transparent;

                span {
                    background-image: url(https://ik.imagekit.io/sheryians/BackEnd%20Donation/maskImage_W7a6T_MXGY.png);
                    background-clip: text;
                }

                .bloom {
                    position: absolute;
                    transform: translate(-50%, -50%);
                    top: 50%;
                    left: 50%;
                    width: 100%;
                    filter: blur(100px);
                }

            }

            .subHeading {
                font-size: 3.4rem;
                letter-spacing: -0.2rem;
                font-weight: 300;
                margin-top: 2rem;
            }
        }

        video {
            width: 100%;
            object-fit: cover;
        }
    }

    .page4 {
        padding-left: 13vw;
        padding-right: 13vw;
        display: flex;
        flex-direction: column;
        padding-top: 15vh;
        font-size: clamp(10px, 1.08vw, 19px);

        * {
            color: var(--primaryLight);
        }

        .text {

            .heading {
                --bgi-position: -7ch;
                font-size: 13em;
                letter-spacing: -0.04em;
                margin-left: -0.06em;
                font-weight: 600;
                line-height: 1em;
                background-image: url("https://ik.imagekit.io/sheryians/three.js/mask_5gcMWG8mG.jpg");
                // background-color: white;
                background-clip: text;
                color: transparent;
                background-repeat: no-repeat;
                background-position: calc(var(--bgi-position) * 0.92);
                width: fit-content !important;
                background-size: cover;


                // @media (prefers-color-scheme:light) {
                //     color: var(--primaryDark);
                // }
            }

            .subTitle {
                font-size: 5.5em;
                text-transform: capitalize;
                line-height: 1em;
                opacity: 0.3;
                letter-spacing: -0.03em;
                font-weight: 600;

                // @media (prefers-color-scheme:light) {
                //     color: var(--primaryDark);
                // }
            }
        }
    }

    .page5 {
        padding-left: 15vw;
        padding-right: 15vw;
        color: var(--primaryLight);
        padding-top: 10vh;
        font-size: 3rem;

        .accordian {
            width: 100%;
            padding-left: 0.5em;
            border-top: 1px solid #f8f8f832;
            font-size: 1em;
            overflow: hidden;
            position: relative;
            z-index: 10;

            .floatingImage {
                position: fixed;
                transform: translate(-50%, -50%) scale(0);
                transform-origin: center;
                transition: 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275), transform 0.7s cubic-bezier(0.23, 1, 0.320, 1);
                top: 0;
                left: 0;
                width: 10rem;
                aspect-ratio: 3/4;
                object-fit: cover;
                pointer-events: none;
                z-index: -1;
                border-radius: 0.75rem;
            }

            .accordian {
                font-size: 0.85em;
                padding-left: 0;
                border: none;

                .top {
                    padding-top: 0;
                }
            }

            .top {
                width: 100%;
                display: flex;
                justify-content: space-between;
                padding: 0.9em 0;
                font-size: 1em;
                cursor: pointer;

                h1 {
                    font-weight: 600;
                    font-size: 0.8em;
                }

                button {
                    display: flex;
                    align-items: center;
                    gap: 1em;
                    background-color: transparent;
                    border: none;
                    outline: none;
                    color: var(--primaryLight);
                    font-size: 1rem;
                    cursor: pointer;


                    .icon {
                        padding: 0.15rem;
                        border-radius: 50%;
                        color: var(--primaryLight);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        padding-top: 0.25em;
                        transition: 0.7s cubic-bezier(0.23, 1, 0.320, 1);

                        svg {
                            // @media (prefers-color-scheme:light) {
                            //     color: red;
                            // }
                        }

                    }
                }
            }

            .panel {
                padding-left: 0.75em;
                display: grid;
                grid-template-rows: 0fr;
                transition: 1.3s cubic-bezier(0.23, 1, 0.320, 1);
                padding-bottom: 0;

                div {
                    overflow: hidden;
                }

                ol,
                ul {
                    list-style-type: none;
                }

                li {
                    font-size: 0.7em;
                    margin-left: 0.4em;
                    font-weight: 300;
                }

                p {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 0.6em;
                    line-height: 1.7em;
                    font-weight: 400;

                    .time {
                        font-size: 0.7em;
                    }
                }
            }

            &.active {
                &>.top {
                    button {
                        .icon {
                            transform: translate(0, 0) rotate(180deg);
                        }
                    }
                }

                &>.panel {
                    grid-template-rows: 1fr;
                    // max-height: 100vh;
                    flex-shrink: 1;
                    padding-bottom: 0.6em;
                }
            }

            &:hover {
                &:has(.accordian:hover) {
                    &>.floatingImage {
                        transform: translate(-50%, -50%) scale(0);
                    }
                }

                &>.floatingImage {
                    transform: translate(-50%, -50%) scale(1);
                }
            }

        }
    }

    .page6 {
        margin-top: 5rem;
        padding-left: 9vw;
        padding-right: 9vw;

        .text {
            h1 {
                font-size: 4rem;
            }
        }

        .faqs {
            padding-top: 3rem;

            // padding: 2rem;
            .indi-faqs {
                margin-bottom: 2rem;
                padding: 1rem;
                padding-top: 0;
                padding-bottom: 2rem;
                border-bottom: 3px solid var(--Shark);

                h3 {
                    font-size: 2.5rem;
                    margin-bottom: 1rem;
                }

                p {
                    width: 80%;
                    color: var(--boulder);
                    font-size: 1.5rem;
                }
            }
        }
    }

    .page7 {
        padding-left: 15vw;
        padding-right: 15vw;
        color: var(--primaryLight);
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 5rem;
        font-size: clamp(11px, 1vw, 19px);

        .text {
            width: 100%;

            .heading {
                font-size: 13em;
                letter-spacing: -0.06em;
                text-transform: capitalize;
                margin-left: -0.04em;
                line-height: 0.98em;
            }

            .subHeading {
                margin-top: 1.5rem;
                font-weight: 300;
                font-size: 1.9rem;
                text-transform: capitalize;
                line-height: 1.5em;

                span {
                    color: var(--placementPrimary);
                }
            }
        }

        .scratcher {
            small {
                font-weight: 200;
                font-size: 1.3rem;
                margin-bottom: 0.7rem;
                display: block;

            }

            .coupon {

                border-radius: 1rem;
                height: 20rem;
                width: 30rem;
                max-width: 85vw;
                aspect-ratio: 2/3;
                // border: 2px dashed #9c9c9c;
                padding: 1rem;
                overflow: hidden;
                background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='26' ry='26' stroke='%23CDCDCDFF' stroke-width='3' stroke-dasharray='5 10' stroke-dashoffset='3' stroke-linecap='square'/%3e%3c/svg%3e");
                border-radius: 26px;

                .container {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100%;
                    width: 100%;
                    position: relative;
                }

                .base {
                    color: var(--primaryLight);
                    border-radius: 10px;
                    padding: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: absolute;
                    transform: translate(-50%, -50%);
                    top: 50%;
                    left: 50%;
                    height: 100%;
                    width: 100%;

                    h3 {

                        font-size: 5em;

                        @media screen and (max-width:600px) {
                            font-size: 6em;
                        }
                    }

                }

                #scratch {
                    position: absolute;
                    transform: translate(-50%, -50%);
                    top: 50%;
                    left: 50%;
                    height: 100%;
                    width: 100%;
                    border-radius: 1rem;
                    transition: all 0.5s ease;
                }

            }
        }

        .comparision {
            width: 100%;

            .heading {
                display: flex;
                gap: 1em;
                justify-content: center;

                div {
                    margin-top: 2em;
                }

                h1 {
                    text-align: center;
                    font-size: 3em;
                    font-weight: 600;
                }
            }

            .compareDivs {
                margin-top: 0.5em;
                display: flex;
                width: 100%;
                gap: 5em;

                .leftCompare {
                    height: fit-content;
                    width: min(33em, 100%);
                    background-color: #222222;

                    .heading {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 2px solid black;
                        padding: 2em;

                        p {
                            color: #878787;
                            font-size: 2em;
                            font-weight: 600;
                        }
                    }

                    .content {

                        padding: 2.2em;
                        padding-top: 2.5em;
                        display: flex;
                        flex-direction: column;
                        gap: 2.5em;

                        div {
                            h1 {
                                font-size: 1.85em;
                                font-weight: 600;
                            }

                            p {
                                color: #6B6B6B;
                                font-size: 1.3em;
                                font-weight: 500;
                            }
                        }
                    }

                    .footer {
                        text-align: center;
                        background-color: #151515;
                        padding: 2em;
                        text-transform: capitalize;
                        color: #6B6B6B;
                        font-weight: 500;
                        font-size: 1.3em;
                    }
                }

                .rightCompare {
                    width: min(33em, 100%);
                    background-color: #222222;

                    .heading {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 2px solid black;
                        padding: 2em;

                        p {
                            color: #35E892;
                            font-size: 2em;
                            font-weight: 600;
                        }
                    }

                    .content {
                        padding: 2.2em;
                        padding-top: 2.5em;
                        display: flex;
                        flex-direction: column;
                        gap: 2.5em;

                        div {
                            h1 {
                                font-size: 1.85em;
                                font-weight: 600;

                                span {
                                    color: #35E892;
                                }
                            }

                            div {
                                display: flex;
                                flex-direction: column;
                                gap: 1em;
                                padding-top: 1em;
                                padding-left: 1em;

                                p {
                                    margin: 0;
                                }
                            }

                            p {
                                margin-left: 1em;
                                text-transform: capitalize;
                                margin-top: 1em;
                                color: #949494;
                                font-size: 1.3em;
                                font-weight: 500;
                            }
                        }

                        .breakLine {
                            margin: auto;
                            width: 100%;
                            height: 1px;
                            border-top: 1px dashed #6B6B6B;
                        }

                        .forPlacement {
                            border-radius: 10px;
                            width: fit-content;
                            padding: 1em;
                            background-color: #353535;
                            color: #6B6B6B;

                            p {
                                font-size: 1.2em;
                                font-weight: 600;
                                margin: 0;
                            }
                        }
                    }

                }
            }
        }

        .start-learning {
            padding: 1em 2em;
            border-radius: 3em;
            font-size: 1.5em;
            border: none;
            outline: none;
            background-image: url('https://ik.imagekit.io/sheryians/BackEnd%20Donation/maskImage_W7a6T_MXGY.png');
            background-position: 15% 80%;
            background-size: 600%;
            color: var(--primaryLight);
            cursor: pointer;

            &.bloom {
                position: relative;
                isolation: isolate;

                &::after {
                    all: inherit;
                    content: "";
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    transform: translate(-50%, -50%);
                    top: 50%;
                    left: 50%;
                    z-index: -1;

                    filter: blur(50px);

                }
            }

        }
    }

    .page8 {
        padding: 0;

        .image-wrapper {
            width: 100%;
            height: 70vh;
            // margin-top: -9%;

            img {
                width: 100%;
                object-fit: cover;
                height: 150%;
                pointer-events: none;
            }


            &:nth-child(even) {
                img {
                    clip-path: polygon(0 25%, 100% 0, 100% 100%, 0 75%);
                }
            }

            &:nth-child(odd) {
                img {

                    clip-path: polygon(0 0%, 100% 25%, 100% 75%, 0 100%);
                }
            }

            &:nth-child(1) {
                height: 30vh;

                img {
                    height: 200%;
                    clip-path: polygon(0 0%, 100% 0, 100% 75%, 0 100%);
                }
            }

            &:nth-child(4) {
                height: 90vh;

                img {
                    object-fit: cover;
                    clip-path: polygon(0 20%, 100% 0, 100% 100%, 0 75%);
                }



            }

            &:nth-child(5) {
                overflow: hidden;
                height: 80vh;

                img {
                    clip-path: polygon(0 0%, 100% 25%, 100% 60%, 0 100%);
                    height: 100%;
                }
            }
        }
    }
}

@media screen and (max-width:900px) {


    #main {
        #page1 {
            flex-direction: column-reverse;
            justify-content: flex-end;
            gap: 2rem;

            .left {
                width: 100%;

                .title {
                    width: 100%;

                }
            }

            .right {
                width: 100%;
            }
        }
    }
}


@media screen and (max-width:600px) {
    html {
        font-size: clamp(12px, 1.06vw, 19px);
    }



    #main {
        #page1 {
            font-size: 0.85rem;

            .left {
                .tags {
                    .tag {
                        font-size: 1.3em;
                    }
                }
            }

            .right {
                width: 100%;
            }
        }

        .page {
            padding-left: 7vw !important;
            padding-right: 7vw !important;
        }

        .page {
            font-size: 0.8rem;
            position: relative;

            .text {
                .subHeading {
                    font-size: 4em;
                }

                h1 {
                    white-space: initial;
                }
            }

            video {
                aspect-ratio: initial;
            }

        }

        .page2 {



            padding: 10vh;
            padding-top: 5vh;
            padding-bottom: 5vh;

            .text {
                .poster-theme {
                    max-width: 20rem;

                }
            }
        }

        .page3 {
            font-size: 0.65rem;
            padding-top: 10vh;

            .text {
                margin-bottom: 3rem;

                .subHeading {
                    font-size: 2.5em;
                    font-weight: 400;
                    letter-spacing: -0.03em;
                }
            }

            .project {
                gap: 1rem;
                margin-bottom: 2rem;

                .heading {
                    .buffer {
                        height: 1.5em;
                        width: 0.4em;
                        border-radius: 0.3em;
                    }

                    h1 {
                        font-size: 1.2em;
                        font-weight: 500;
                        padding: 0.25em .6em;
                    }
                }

                .sub-heading {
                    font-size: 3rem;
                    font-weight: 500;
                    letter-spacing: 0;
                }

                .image-wrapper,
                video {
                    margin-top: 4em;

                    &.no-scale {
                        img {
                            transform: scale(1);

                        }
                    }

                    img {
                        transform: scale(1.3);
                    }
                }

            }
        }

        .page4 {
            padding-top: 4vh;
            font-size: 0.5rem;
        }

        .page5 {
            font-size: 1.7rem;
            padding-top: 0vh;

            .accordian {
                .top {
                    h1 {
                        font-size: 1em;
                    }
                }
            }

            .floatingImage {
                display: none;
            }

            button {
                span {
                    display: none;
                }

                .feather {
                    height: 1.3rem;
                    width: 1.3rem;
                    stroke-width: 2.5px;
                }
            }

        }

        .page6 {
            font-size: 0.45rem;
            position: relative;
            padding-top: 10vh;

            h1 {
                font-size: 8em;
            }

            P {
                font-size: 4em;
            }

            button {
                font-size: 4em;
                padding: 0.4em 2em;
            }

            .text {
                width: 100%;

                .subHeading {
                    font-size: 2.5em;
                }
            }

            .comparision {
                font-size: 1rem;

                .heading {
                    display: none;
                }

                .leftCompare {
                    display: none !important;
                }

                .rightCompare {
                    background-color: transparent !important;
                    padding-left: 0 !important;
                    padding-right: 0 !important;

                    .heading {
                        display: none !important;
                    }

                    .content {
                        padding-left: 0 !important;
                        padding-right: 0 !important;
                    }
                }
            }

        }

        .page7 {
            padding-top: 5vh;

            .text {
                .heading {
                    font-size: 8em;
                }

                .subHeading {
                    font-size: 2em;


                }

                .validity {
                    font-size: 1rem;

                }

                br {
                    display: none;
                }
            }

            .scratcher {

                .coupon {
                    .container {
                        .base {
                            h3 {
                                font-size: 6em;
                            }
                        }
                    }
                }
            }


        }

        .page8 {
            padding: 0 !important;

            .image-wrapper {
                height: 20vh;

                &:nth-child(1) {
                    height: 15vh;
                }

                &:nth-child(4) {
                    height: 20vh;
                }

                &:nth-child(5) {
                    height: 20vh;
                }

            }
        }


    }
}