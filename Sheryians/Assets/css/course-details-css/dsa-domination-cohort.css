@font-face {
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/<PERSON><PERSON>-Extrabold_YC6oQDa3Ix.ttf?updatedAt=1697720439904");
  font-family: "<PERSON><PERSON>";
  font-weight: 700;
}
@font-face {
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/<PERSON><PERSON>-Bold_JK4cTGJOE.ttf?updatedAt=1697720175118");
  font-family: "<PERSON>roy";
  font-weight: 600;
}
@font-face {
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/Gilroy-Semibold_zTZ2PgJOF.ttf?updatedAt=1697720387357");
  font-family: "Gilroy";
  font-weight: 500;
}
@font-face {
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/<PERSON><PERSON>-Medium_eHDK2RllDd.ttf?updatedAt=1697720216741");
  font-family: "<PERSON><PERSON>";
  font-weight: 400;
}
@font-face {
  src: url("https://ik.imagekit.io/sheryians/Fonts/Gilroy-UltraLight_DLYQkGL0g.ttf?updatedAt=1705175454233");
  font-family: "Gilroy";
  font-weight: 100;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Regular_5Mzhp8KlA8.ttf?updatedAt=1709189838954");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Thin_J0zaDW5kUV.ttf?updatedAt=1709189838905");
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Light_M4nzWhve6k.ttf?updatedAt=1709189838276");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Medium_2wEhlmo-TD.ttf?updatedAt=1709189838650");
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Bold_VMHV5twUMR.ttf?updatedAt=1709189835487");
  font-weight: bold;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Hairline_9Jz6JFa3S.ttf?updatedAt=1709189838405");
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-LightIta_mLZfHYSDzL.ttf?updatedAt=1709189838941");
  font-weight: 300;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-RegIta_ZQZ0ISuECo.ttf?updatedAt=1709189838814");
  font-weight: normal;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ThinIta__6kihetaOz.ttf?updatedAt=1709189838563");
  font-weight: 100;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-MedIta_s4wTiA5al.ttf?updatedAt=1709189838311");
  font-weight: 500;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-HairlineIta_UXSDxUaUw.ttf?updatedAt=1709189838597");
  font-weight: 100;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/Helvetica_NTUXwLmxSC.ttf?updatedAt=1708769204058");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtBlkIta_f0giU-O0Yd.ttf?updatedAt=1709189835297");
  font-weight: 900;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-BoldIta_O5gX7g1L5.ttf?updatedAt=1709189835238");
  font-weight: bold;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-Black_-pjI4fOp4E.ttf?updatedAt=1709189835338");
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-BlackIta_y1WeOftIb.ttf?updatedAt=1709189835136");
  font-weight: 900;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtBlk_yunH-AhU2b.ttf?updatedAt=1709189835336");
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtLtIta_GJzq7onfxt.ttf?updatedAt=1709189835244");
  font-weight: 200;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtBdIta__MPkMs25CD.ttf?updatedAt=1709189835153");
  font-weight: 800;
  font-style: italic;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtraBold_Qqt_sGOvuc.ttf?updatedAt=1709189835200");
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: "Helvetica";
  src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/HelveticaNowDisplay-ExtLt_R50yORjQ9.ttf?updatedAt=1709189835320");
  font-weight: 200;
  font-style: normal;
}
@font-face {
  font-family: "Mauline";
  src: url("https://ik.imagekit.io/sheryians/Fonts/Mauline/Mauline_Nl5uNkMO4.ttf");
  font-style: normal;
}
@font-face {
  font-family: "Bellina";
  src: url("https://ik.imagekit.io/sheryians/Fonts/Bellina/Bellina%20Slant%20Condensed_OesY1bYek.ttf");
  font-style: normal;
}
:root {
  --primary: #10a37f;
  --placementPrimary: #02e797;
  --secondary: #009560;
  --white: #fff;
  --primaryLight: #F8F8F8;
  --primaryDark: #0c0c0c;
  --secondaryLight: #dbdbdb;
  --activeDark: #212121;
  --secondaryLightActive: #cbcbcb;
  --secondaryDark: #ececec;
  --secondaryDarkShadow: rgba(0, 0, 0, 0.16);
  --textColor: #7BBA81;
  --special: #d2af7a;
  --ternary: #c4c9d3;
  --error: #e74c3c;
  --maxPageWidth: 110rem;
  --discord: #7b40ff;
  --link: rgb(0, 71, 125);
}

* {
  margin: 0%;
  padding: 0%;
  box-sizing: border-box;
  font-family: Helvetica;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  text-rendering: optimizeSpeed;
}

.highlight {
  color: var(--placementPrimary);
}

html,
body {
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}

html {
  font-size: clamp(14px, 1.06vw, 19px);
}

.feather {
  width: 24px;
  height: 24px;
  stroke: currentColor;
  stroke-width: 3.5;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

#main .cta-syllabus {
  display: flex;
  justify-content: center;
  align-items: center;
}
#main .cta-syllabus .buy,
#main .cta-syllabus .buy-now {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  font-family: Helvetica;
  background-color: var(--secondary);
  font-weight: 500;
  text-align: center;
  color: var(--white);
  font-size: 1.5em;
  text-transform: capitalize;
  cursor: pointer;
  gap: 1em;
  padding: 0.8em;
  border-radius: 0.4rem;
  max-width: 30rem;
}
#main .page {
  padding: 5vh 4vw;
}
#main #page1 {
  padding-top: 13vh;
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding-bottom: 0;
  color: var(--primaryLight);
  min-height: min(100vh, 760px);
  padding-top: min(20vh, 10rem);
  gap: 3rem;
}
#main #page1 .left {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1.5rem;
}
#main #page1 .left .title {
  width: 90%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
#main #page1 .left .title h1 {
  font-size: 3.5em;
  line-height: 1em;
  font-weight: 600;
  letter-spacing: -0.02em;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}
#main #page1 .left .title h1 .bloomImages {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  width: -moz-fit-content;
  width: fit-content;
}
#main #page1 .left .title h1 .bloomImages .bloomImage {
  width: 3rem;
  position: relative;
  aspect-ratio: 1/1;
  isolation: isolate;
}
#main #page1 .left .title h1 .bloomImages .bloomImage img,
#main #page1 .left .title h1 .bloomImages .bloomImage picture {
  width: 100%;
  height: 100%;
  aspect-ratio: 1/1;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: center;
     object-position: center;
}
#main #page1 .left .title h1 .bloomImages .bloomImage .bloom {
  position: absolute;
  top: 0;
  left: 0;
  filter: blur(10px) brightness(1.2);
}
#main #page1 .left .title .numEnrollStudent {
  width: -moz-fit-content;
  width: fit-content;
  font-size: 1em;
  font-weight: 600;
  gap: 0.6em;
  align-items: center;
  text-transform: capitalize;
  color: var(--primary);
}
#main #page1 .left .title .numEnrollStudent span {
  opacity: 0.5;
  color: var(--primaryDark);
}
#main #page1 .left .title .numEnrollStudent:hover i {
  display: initial;
}
#main #page1 .left .title .numEnrollStudent i {
  font-size: 1em;
  display: none;
}
#main #page1 .left .title .numEnrollStudent .num {
  font-size: 1.1em;
}
#main #page1 .left .title .mask-bg {
  background-image: url("https://ik.imagekit.io/sheryians/three.js/mask_5gcMWG8mG.jpg");
  background-size: cover;
  background-position: top left;
  -webkit-background-clip: text;
          background-clip: text;
  position: relative;
  isolation: isolate;
}
#main #page1 .left .title .mask-bg span {
  color: transparent;
}
#main #page1 .left .title .mask-bg:not(:has(.bloom)) {
  background-image: none;
}
#main #page1 .left .title .mask-bg:not(:has(.bloom)) span {
  color: #fff !important;
}
#main #page1 .left .title .mask-bg .bloom {
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  z-index: -1;
  filter: blur(20px);
  background-image: url("https://ik.imagekit.io/sheryians/three.js/mask_5gcMWG8mG.jpg");
  background-size: cover;
  background-position: center;
  opacity: 1;
  -webkit-background-clip: text;
          background-clip: text;
  color: transparent;
}
#main #page1 .left .tags {
  display: flex;
  align-items: center;
  gap: 1.5em;
  flex-wrap: nowrap;
}
#main #page1 .left .tags .tag {
  flex-shrink: 0;
  font-size: 1.1em;
  font-weight: 600;
  text-transform: capitalize;
  padding: 0.65em 1.5em;
  border-radius: 5px;
  background-color: var(--activeDark);
}
#main #page1 .left .activeSupport {
  font-size: 1em;
  font-weight: 600;
  text-transform: capitalize;
  padding: 0.7em 1.3em;
  border-radius: 5px;
  background-color: #353535;
  display: flex;
  align-items: center;
  gap: 1em;
}
#main #page1 .left .activeSupport img {
  height: 1.5em;
  width: 1.5em;
  -o-object-fit: cover;
     object-fit: cover;
}
#main #page1 .left .instructor {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--secondaryLight);
  padding: 0.4rem 0.8rem;
  width: 50%;
  color: var(--primaryDark);
  border-radius: 4px;
}
#main #page1 .left .instructor p {
  font-weight: 500;
  font-size: 0.9rem;
  opacity: 0.6;
  text-transform: capitalize;
}
#main #page1 .left .instructor h1 {
  text-transform: capitalize;
  font-size: 0.9rem;
  font-weight: 600;
}
#main #page1 .left .price {
  align-items: center;
  margin-top: 1.5em;
}
#main #page1 .left .price * {
  font-family: "Gilroy";
}
#main #page1 .left .price p {
  font-size: 2.5em;
  font-weight: 600;
  text-transform: capitalize;
}
#main #page1 .left .price .priceValue {
  color: var(--placementPrimary);
  font-size: 0.83em;
}
#main #page1 .left .price .priceValue span {
  color: var(--placementPrimary);
}
#main #page1 .left .price .originalValue {
  font-size: 0.73em;
  position: relative;
  color: var(--primaryLight);
}
#main #page1 .left .price .originalValue::after {
  content: "";
  width: 110%;
  height: max(2.5px, 0.07em);
  background-color: var(--error);
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
}
#main #page1 .left .price .discount {
  font-size: 0.45em;
  font-weight: 400;
}
#main #page1 .left .buy {
  display: flex;
  align-items: center;
  gap: 2em;
}
#main #page1 .left .buy .buyNow {
  font-family: "Gilroy";
  background-color: var(--secondary);
  font-weight: 400;
  color: var(--white);
  padding: 0.8em 1.2em;
  font-size: 1.5em;
  text-transform: capitalize;
  border-radius: 0.4rem;
  cursor: pointer;
  font-weight: bold;
}
#main #page1 .left .buy .seatFull {
  background-color: var(--error);
  font-weight: 500;
  color: var(--white);
  padding: 0.6rem 1.2rem;
  font-size: 1rem;
  text-transform: capitalize;
  border-radius: 1.5rem;
  cursor: not-allowed;
}
#main #page1 .left .buy .notify {
  background-color: var(--primary);
  font-weight: 500;
  color: var(--white);
  padding: 0.6rem 1.2rem;
  font-size: 1rem;
  box-shadow: none;
  text-transform: capitalize;
  border-radius: 1.5rem;
  cursor: pointer;
}
#main #page1 .left .buy .commingSoon {
  background-color: var(--secondaryLight);
  font-weight: 500;
  color: var(--primaryDark);
  padding: 0.6rem 1.2rem;
  font-weight: 600;
  opacity: 0.5;
  font-size: 1rem;
  text-transform: capitalize;
  border-radius: 0.5rem;
}
#main #page1 .left .buy .commingSoon.seatLeft {
  opacity: 1 !important;
}
#main #page1 .left .buy .commingSoon span {
  font-size: 1.1em;
}
#main #page1 .left .buy .commingSoon .sign {
  font-weight: 900;
}
#main #page1 .left .buy .tryFree {
  border-radius: 0.2rem;
  background-color: var(--primaryDark);
  font-weight: 400;
  color: var(--white);
  padding: 1.2rem 3rem;
  font-size: 1.25rem;
  text-transform: capitalize;
  border-radius: 0.5rem;
  cursor: pointer;
}
#main #page1 .left .perks {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 2rem;
  gap: 1.5rem;
  justify-content: space-between;
}
#main #page1 .left .perks .perk {
  width: 45%;
  flex-shrink: 0;
  gap: 0.8rem;
}
#main #page1 .left .perks .perk .name {
  font-size: 1.05rem;
  font-family: "Gilroy";
  font-weight: 100;
}
#main #page1 .left .perks .perk .name span {
  font-weight: 600;
}
#main #page1 .left .description {
  width: 90%;
  font-weight: 400;
  opacity: 0.8;
  margin-top: 1rem;
  font-size: 1.3rem;
  line-height: 2.3rem;
}
#main #page1 .right {
  width: 41rem;
  aspect-ratio: 1.42/1 !important;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  justify-content: center;
  position: relative;
  border-radius: 0.9rem;
  transition: 0.2s;
  overflow: hidden;
  cursor: pointer;
}
#main #page1 .right img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 15px;
  -o-object-position: center;
     object-position: center;
}
#main #page1 .right #playOverlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #1b1b1b;
  justify-content: center;
  align-items: center;
}
#main #page1 .right #playOverlay i {
  transition: 0.2s;
  font-size: 5rem;
  color: #a9a9a9;
  cursor: pointer;
  position: absolute;
}
#main #page1 .right #playOverlay:hover i {
  font-size: 6rem;
}
#main #page1 .right .tags {
  position: absolute;
  top: 6%;
  right: 6%;
}
#main #page1 .right .tags .tag {
  flex-shrink: 0;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  padding: 0.4rem 0.8rem;
  border-radius: 5px;
  background-color: var(--background-color);
}
#main #page1 .right #embedded-promo {
  width: 100%;
  height: 100%;
}
#main .page2 {
  color: var(--primaryLight);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: min(10vh, 7rem);
  padding: min(10vh, 7rem) 0;
  padding-top: 5vh;
  padding-left: 12vw;
  padding-right: 12vw;
  isolation: isolate;
}
#main .page2 .text {
  display: flex;
  flex-direction: column;
  text-align: center;
}
#main .page2 .text .poster-theme {
  max-width: 30rem;
  align-self: center;
  margin-bottom: 4rem;
}
#main .page2 .text * {
  text-transform: capitalize;
}
#main .page2 .text .maskText {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 900px;
}
#main .page2 .text .maskText h1 {
  background-image: url(https://ik.imagekit.io/sheryians/Aptitude%20&%20Reasoning/mask%20Large_ZB4lGN8hWZ.png);
  background-position: 10% 100%;
  -webkit-background-clip: text;
          background-clip: text;
  color: transparent;
  font-size: 10.5em;
  letter-spacing: -0.05em;
  text-transform: capitalize;
  line-height: 1.15;
  background-position: center;
  background-size: contain;
  width: -moz-fit-content !important;
  width: fit-content !important;
  align-self: center;
}
#main .page2 .text .subHeading {
  text-transform: capitalize;
  font-size: 5em;
  font-weight: 600;
  letter-spacing: -0.06em;
  line-height: 0.5;
  position: relative;
}
#main .page2 .text .subHeading .bloom {
  filter: blur(30px);
}
#main .page2 .text .subHeading .pointer {
  position: absolute;
  bottom: 60%;
  transform: translateX(-50%);
  left: 55%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
#main .page2 .text .subHeading .pointer span {
  font-size: 3.5rem;
  font-weight: 100;
  font-style: italic;
  font-family: "Bellina";
}
#main .page2 .text .subHeading .pointer img {
  height: 2.5rem;
  width: 2.5rem;
  -o-object-fit: cover;
     object-fit: cover;
}
#main .page2 .text .subHeading span {
  font-size: 0.3em;
  font-weight: 500;
  letter-spacing: -0.05em;
}
#main .page2 video,
#main .page2 img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
#main .page2 .bloom {
  position: absolute;
  transform: translate(-50%, -50%) scale(1);
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  filter: blur(60px) brightness(1);
  z-index: -1;
}
#main .page3 {
  padding-left: 15vw;
  padding-right: 15vw;
  color: var(--primaryLight);
  padding-top: 15vh;
  display: flex;
  flex-direction: column;
  gap: 5rem;
}
#main .page3 .project {
  display: flex;
  gap: 2rem;
  flex-direction: column;
}
#main .page3 .project .heading {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}
#main .page3 .project .heading .buffer {
  height: 3em;
  width: 0.7em;
  background-color: #fbd11a;
  border-radius: 0.3em;
}
#main .page3 .project .heading h1 {
  font-size: 1.5em;
  font-weight: 200;
  letter-spacing: -0.04em;
  text-transform: capitalize;
  background-color: #000000;
  border-radius: 0.8rem;
  padding: 0.25em 1em;
  text-transform: uppercase;
}
#main .page3 .project .sub-heading {
  font-size: 3em;
  font-weight: 500;
  letter-spacing: -0.02em;
  line-height: 1em;
  text-transform: capitalize;
}
#main .page3 .project:nth-child(2) {
  margin-top: 3rem;
}
#main .page3 .text {
  display: flex;
  flex-direction: column;
}
#main .page3 .text h1 {
  font-size: 10.5em;
  text-transform: capitalize;
  letter-spacing: -0.04em;
  line-height: 1em;
}
#main .page3 .text .maskText {
  position: relative;
  color: transparent;
}
#main .page3 .text .maskText span {
  background-image: url(https://ik.imagekit.io/sheryians/BackEnd%20Donation/maskImage_W7a6T_MXGY.png);
  -webkit-background-clip: text;
          background-clip: text;
}
#main .page3 .text .maskText .bloom {
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  width: 100%;
  filter: blur(100px);
}
#main .page3 .text .subHeading {
  font-size: 3.4rem;
  letter-spacing: -0.2rem;
  font-weight: 300;
  margin-top: 2rem;
}
#main .page3 video {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
#main .page4 {
  padding-left: 13vw;
  padding-right: 13vw;
  display: flex;
  flex-direction: column;
  padding-top: 15vh;
  font-size: clamp(10px, 1.08vw, 19px);
}
#main .page4 * {
  color: var(--primaryLight);
}
#main .page4 .text .heading {
  --bgi-position: -7ch;
  font-size: 13em;
  letter-spacing: -0.04em;
  margin-left: -0.06em;
  font-weight: 600;
  line-height: 1em;
  background-image: url("https://ik.imagekit.io/sheryians/three.js/mask_5gcMWG8mG.jpg");
  -webkit-background-clip: text;
          background-clip: text;
  color: transparent;
  background-repeat: no-repeat;
  background-position: calc(var(--bgi-position) * 0.92);
  width: -moz-fit-content !important;
  width: fit-content !important;
  background-size: cover;
}
#main .page4 .text .subTitle {
  font-size: 5.5em;
  text-transform: capitalize;
  line-height: 1em;
  opacity: 0.3;
  letter-spacing: -0.03em;
  font-weight: 600;
}
#main .page5 {
  padding-left: 15vw;
  padding-right: 15vw;
  color: var(--primaryLight);
  padding-top: 10vh;
  font-size: 3rem;
}
#main .page5 .accordian {
  width: 100%;
  padding-left: 0.5em;
  border-top: 1px solid rgba(248, 248, 248, 0.1960784314);
  font-size: 1em;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
#main .page5 .accordian .floatingImage {
  position: fixed;
  transform: translate(-50%, -50%) scale(0);
  transform-origin: center;
  transition: 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
  top: 0;
  left: 0;
  width: 10rem;
  aspect-ratio: 3/4;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  z-index: -1;
  border-radius: 0.75rem;
}
#main .page5 .accordian .accordian {
  font-size: 0.85em;
  padding-left: 0;
  border: none;
}
#main .page5 .accordian .accordian .top {
  padding-top: 0;
}
#main .page5 .accordian .top {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0.9em 0;
  font-size: 1em;
  cursor: pointer;
}
#main .page5 .accordian .top h1 {
  font-weight: 600;
  font-size: 0.8em;
}
#main .page5 .accordian .top button {
  display: flex;
  align-items: center;
  gap: 1em;
  background-color: transparent;
  border: none;
  outline: none;
  color: var(--primaryLight);
  font-size: 1rem;
  cursor: pointer;
}
#main .page5 .accordian .top button .icon {
  padding: 0.15rem;
  border-radius: 50%;
  color: var(--primaryLight);
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 0.25em;
  transition: 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
#main .page5 .accordian .panel {
  padding-left: 0.75em;
  display: grid;
  grid-template-rows: 0fr;
  transition: 1.3s cubic-bezier(0.23, 1, 0.32, 1);
  padding-bottom: 0;
}
#main .page5 .accordian .panel div {
  overflow: hidden;
}
#main .page5 .accordian .panel ol,
#main .page5 .accordian .panel ul {
  list-style-type: none;
}
#main .page5 .accordian .panel li {
  font-size: 0.7em;
  margin-left: 0.4em;
  font-weight: 300;
}
#main .page5 .accordian .panel p {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.6em;
  line-height: 1.7em;
  font-weight: 400;
}
#main .page5 .accordian .panel p .time {
  font-size: 0.7em;
}
#main .page5 .accordian.active > .top button .icon {
  transform: translate(0, 0) rotate(180deg);
}
#main .page5 .accordian.active > .panel {
  grid-template-rows: 1fr;
  flex-shrink: 1;
  padding-bottom: 0.6em;
}
#main .page5 .accordian:hover:has(.accordian:hover) > .floatingImage {
  transform: translate(-50%, -50%) scale(0);
}
#main .page5 .accordian:hover > .floatingImage {
  transform: translate(-50%, -50%) scale(1);
}
#main .page6 {
  margin-top: 5rem;
  padding-left: 9vw;
  padding-right: 9vw;
}
#main .page6 .text h1 {
  font-size: 4rem;
}
#main .page6 .faqs {
  padding-top: 3rem;
}
#main .page6 .faqs .indi-faqs {
  margin-bottom: 2rem;
  padding: 1rem;
  padding-top: 0;
  padding-bottom: 2rem;
  border-bottom: 3px solid var(--Shark);
}
#main .page6 .faqs .indi-faqs h3 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}
#main .page6 .faqs .indi-faqs p {
  width: 80%;
  color: var(--boulder);
  font-size: 1.5rem;
}
#main .page7 {
  padding-left: 15vw;
  padding-right: 15vw;
  color: var(--primaryLight);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5rem;
  font-size: clamp(11px, 1vw, 19px);
}
#main .page7 .text {
  width: 100%;
}
#main .page7 .text .heading {
  font-size: 13em;
  letter-spacing: -0.06em;
  text-transform: capitalize;
  margin-left: -0.04em;
  line-height: 0.98em;
}
#main .page7 .text .subHeading {
  margin-top: 1.5rem;
  font-weight: 300;
  font-size: 1.9rem;
  text-transform: capitalize;
  line-height: 1.5em;
}
#main .page7 .text .subHeading span {
  color: var(--placementPrimary);
}
#main .page7 .scratcher small {
  font-weight: 200;
  font-size: 1.3rem;
  margin-bottom: 0.7rem;
  display: block;
}
#main .page7 .scratcher .coupon {
  border-radius: 1rem;
  height: 20rem;
  width: 30rem;
  max-width: 85vw;
  aspect-ratio: 2/3;
  padding: 1rem;
  overflow: hidden;
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='26' ry='26' stroke='%23CDCDCDFF' stroke-width='3' stroke-dasharray='5 10' stroke-dashoffset='3' stroke-linecap='square'/%3e%3c/svg%3e");
  border-radius: 26px;
}
#main .page7 .scratcher .coupon .container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  position: relative;
}
#main .page7 .scratcher .coupon .base {
  color: var(--primaryLight);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  height: 100%;
  width: 100%;
}
#main .page7 .scratcher .coupon .base h3 {
  font-size: 5em;
}
@media screen and (max-width: 600px) {
  #main .page7 .scratcher .coupon .base h3 {
    font-size: 6em;
  }
}
#main .page7 .scratcher .coupon #scratch {
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  height: 100%;
  width: 100%;
  border-radius: 1rem;
  transition: all 0.5s ease;
}
#main .page7 .comparision {
  width: 100%;
}
#main .page7 .comparision .heading {
  display: flex;
  gap: 1em;
  justify-content: center;
}
#main .page7 .comparision .heading div {
  margin-top: 2em;
}
#main .page7 .comparision .heading h1 {
  text-align: center;
  font-size: 3em;
  font-weight: 600;
}
#main .page7 .comparision .compareDivs {
  margin-top: 0.5em;
  display: flex;
  width: 100%;
  gap: 5em;
}
#main .page7 .comparision .compareDivs .leftCompare {
  height: -moz-fit-content;
  height: fit-content;
  width: min(33em, 100%);
  background-color: #222222;
}
#main .page7 .comparision .compareDivs .leftCompare .heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid black;
  padding: 2em;
}
#main .page7 .comparision .compareDivs .leftCompare .heading p {
  color: #878787;
  font-size: 2em;
  font-weight: 600;
}
#main .page7 .comparision .compareDivs .leftCompare .content {
  padding: 2.2em;
  padding-top: 2.5em;
  display: flex;
  flex-direction: column;
  gap: 2.5em;
}
#main .page7 .comparision .compareDivs .leftCompare .content div h1 {
  font-size: 1.85em;
  font-weight: 600;
}
#main .page7 .comparision .compareDivs .leftCompare .content div p {
  color: #6B6B6B;
  font-size: 1.3em;
  font-weight: 500;
}
#main .page7 .comparision .compareDivs .leftCompare .footer {
  text-align: center;
  background-color: #151515;
  padding: 2em;
  text-transform: capitalize;
  color: #6B6B6B;
  font-weight: 500;
  font-size: 1.3em;
}
#main .page7 .comparision .compareDivs .rightCompare {
  width: min(33em, 100%);
  background-color: #222222;
}
#main .page7 .comparision .compareDivs .rightCompare .heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid black;
  padding: 2em;
}
#main .page7 .comparision .compareDivs .rightCompare .heading p {
  color: #35E892;
  font-size: 2em;
  font-weight: 600;
}
#main .page7 .comparision .compareDivs .rightCompare .content {
  padding: 2.2em;
  padding-top: 2.5em;
  display: flex;
  flex-direction: column;
  gap: 2.5em;
}
#main .page7 .comparision .compareDivs .rightCompare .content div h1 {
  font-size: 1.85em;
  font-weight: 600;
}
#main .page7 .comparision .compareDivs .rightCompare .content div h1 span {
  color: #35E892;
}
#main .page7 .comparision .compareDivs .rightCompare .content div div {
  display: flex;
  flex-direction: column;
  gap: 1em;
  padding-top: 1em;
  padding-left: 1em;
}
#main .page7 .comparision .compareDivs .rightCompare .content div div p {
  margin: 0;
}
#main .page7 .comparision .compareDivs .rightCompare .content div p {
  margin-left: 1em;
  text-transform: capitalize;
  margin-top: 1em;
  color: #949494;
  font-size: 1.3em;
  font-weight: 500;
}
#main .page7 .comparision .compareDivs .rightCompare .content .breakLine {
  margin: auto;
  width: 100%;
  height: 1px;
  border-top: 1px dashed #6B6B6B;
}
#main .page7 .comparision .compareDivs .rightCompare .content .forPlacement {
  border-radius: 10px;
  width: -moz-fit-content;
  width: fit-content;
  padding: 1em;
  background-color: #353535;
  color: #6B6B6B;
}
#main .page7 .comparision .compareDivs .rightCompare .content .forPlacement p {
  font-size: 1.2em;
  font-weight: 600;
  margin: 0;
}
#main .page7 .start-learning {
  padding: 1em 2em;
  border-radius: 3em;
  font-size: 1.5em;
  border: none;
  outline: none;
  background-image: url("https://ik.imagekit.io/sheryians/BackEnd%20Donation/maskImage_W7a6T_MXGY.png");
  background-position: 15% 80%;
  background-size: 600%;
  color: var(--primaryLight);
  cursor: pointer;
}
#main .page7 .start-learning.bloom {
  position: relative;
  isolation: isolate;
}
#main .page7 .start-learning.bloom::after {
  all: inherit;
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  z-index: -1;
  filter: blur(50px);
}
#main .page8 {
  padding: 0;
}
#main .page8 .image-wrapper {
  width: 100%;
  height: 70vh;
}
#main .page8 .image-wrapper img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  height: 150%;
  pointer-events: none;
}
#main .page8 .image-wrapper:nth-child(even) img {
  -webkit-clip-path: polygon(0 25%, 100% 0, 100% 100%, 0 75%);
          clip-path: polygon(0 25%, 100% 0, 100% 100%, 0 75%);
}
#main .page8 .image-wrapper:nth-child(odd) img {
  -webkit-clip-path: polygon(0 0%, 100% 25%, 100% 75%, 0 100%);
          clip-path: polygon(0 0%, 100% 25%, 100% 75%, 0 100%);
}
#main .page8 .image-wrapper:nth-child(1) {
  height: 30vh;
}
#main .page8 .image-wrapper:nth-child(1) img {
  height: 200%;
  -webkit-clip-path: polygon(0 0%, 100% 0, 100% 75%, 0 100%);
          clip-path: polygon(0 0%, 100% 0, 100% 75%, 0 100%);
}
#main .page8 .image-wrapper:nth-child(4) {
  height: 90vh;
}
#main .page8 .image-wrapper:nth-child(4) img {
  -o-object-fit: cover;
     object-fit: cover;
  -webkit-clip-path: polygon(0 20%, 100% 0, 100% 100%, 0 75%);
          clip-path: polygon(0 20%, 100% 0, 100% 100%, 0 75%);
}
#main .page8 .image-wrapper:nth-child(5) {
  overflow: hidden;
  height: 80vh;
}
#main .page8 .image-wrapper:nth-child(5) img {
  -webkit-clip-path: polygon(0 0%, 100% 25%, 100% 60%, 0 100%);
          clip-path: polygon(0 0%, 100% 25%, 100% 60%, 0 100%);
  height: 100%;
}

@media screen and (max-width: 900px) {
  #main #page1 {
    flex-direction: column-reverse;
    justify-content: flex-end;
    gap: 2rem;
  }
  #main #page1 .left {
    width: 100%;
  }
  #main #page1 .left .title {
    width: 100%;
  }
  #main #page1 .right {
    width: 100%;
  }
}
@media screen and (max-width: 600px) {
  html {
    font-size: clamp(12px, 1.06vw, 19px);
  }
  #main #page1 {
    font-size: 0.85rem;
  }
  #main #page1 .left .tags .tag {
    font-size: 1.3em;
  }
  #main #page1 .right {
    width: 100%;
  }
  #main .page {
    padding-left: 7vw !important;
    padding-right: 7vw !important;
  }
  #main .page {
    font-size: 0.8rem;
    position: relative;
  }
  #main .page .text .subHeading {
    font-size: 4em;
  }
  #main .page .text h1 {
    white-space: initial;
  }
  #main .page video {
    aspect-ratio: initial;
  }
  #main .page2 {
    padding: 10vh;
    padding-top: 5vh;
    padding-bottom: 5vh;
  }
  #main .page2 .text .poster-theme {
    max-width: 20rem;
  }
  #main .page3 {
    font-size: 0.65rem;
    padding-top: 10vh;
  }
  #main .page3 .text {
    margin-bottom: 3rem;
  }
  #main .page3 .text .subHeading {
    font-size: 2.5em;
    font-weight: 400;
    letter-spacing: -0.03em;
  }
  #main .page3 .project {
    gap: 1rem;
    margin-bottom: 2rem;
  }
  #main .page3 .project .heading .buffer {
    height: 1.5em;
    width: 0.4em;
    border-radius: 0.3em;
  }
  #main .page3 .project .heading h1 {
    font-size: 1.2em;
    font-weight: 500;
    padding: 0.25em 0.6em;
  }
  #main .page3 .project .sub-heading {
    font-size: 3rem;
    font-weight: 500;
    letter-spacing: 0;
  }
  #main .page3 .project .image-wrapper,
  #main .page3 .project video {
    margin-top: 4em;
  }
  #main .page3 .project .image-wrapper.no-scale img,
  #main .page3 .project video.no-scale img {
    transform: scale(1);
  }
  #main .page3 .project .image-wrapper img,
  #main .page3 .project video img {
    transform: scale(1.3);
  }
  #main .page4 {
    padding-top: 4vh;
    font-size: 0.5rem;
  }
  #main .page5 {
    font-size: 1.7rem;
    padding-top: 0vh;
  }
  #main .page5 .accordian .top h1 {
    font-size: 1em;
  }
  #main .page5 .floatingImage {
    display: none;
  }
  #main .page5 button span {
    display: none;
  }
  #main .page5 button .feather {
    height: 1.3rem;
    width: 1.3rem;
    stroke-width: 2.5px;
  }
  #main .page6 {
    font-size: 0.45rem;
    position: relative;
    padding-top: 10vh;
  }
  #main .page6 h1 {
    font-size: 8em;
  }
  #main .page6 P {
    font-size: 4em;
  }
  #main .page6 button {
    font-size: 4em;
    padding: 0.4em 2em;
  }
  #main .page6 .text {
    width: 100%;
  }
  #main .page6 .text .subHeading {
    font-size: 2.5em;
  }
  #main .page6 .comparision {
    font-size: 1rem;
  }
  #main .page6 .comparision .heading {
    display: none;
  }
  #main .page6 .comparision .leftCompare {
    display: none !important;
  }
  #main .page6 .comparision .rightCompare {
    background-color: transparent !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  #main .page6 .comparision .rightCompare .heading {
    display: none !important;
  }
  #main .page6 .comparision .rightCompare .content {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  #main .page7 {
    padding-top: 5vh;
  }
  #main .page7 .text .heading {
    font-size: 8em;
  }
  #main .page7 .text .subHeading {
    font-size: 2em;
  }
  #main .page7 .text .validity {
    font-size: 1rem;
  }
  #main .page7 .text br {
    display: none;
  }
  #main .page7 .scratcher .coupon .container .base h3 {
    font-size: 6em;
  }
  #main .page8 {
    padding: 0 !important;
  }
  #main .page8 .image-wrapper {
    height: 20vh;
  }
  #main .page8 .image-wrapper:nth-child(1) {
    height: 15vh;
  }
  #main .page8 .image-wrapper:nth-child(4) {
    height: 20vh;
  }
  #main .page8 .image-wrapper:nth-child(5) {
    height: 20vh;
  }
}/*# sourceMappingURL=dsa-domination-cohort.css.map */