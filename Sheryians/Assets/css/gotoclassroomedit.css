:root {
    --primary: #10a37f;
    --secondary: #075c48;
    --white: #fff;
    --primaryLight: #f8f8f8;
    --primaryDark: #222222;
    --secondaryLight: #dbdbdb;
    --secondaryDark: #ececec;
    --textColor: #7bba81;
    --special: #d2af7a;
    --ternary: #c4c9d3;
    --error: #e74c3c;
}

* {
    margin: 0%;
    padding: 0%;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
*:focus {
    outline: none;
}

/* Language toggle styles */
.languageToggles {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 8px;
}
.language-toggle-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.language-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border: 1px solid #ccc;
  border-radius: 6px;
  cursor: pointer;
  user-select: none;
  transition: background 0.2s;
}

.language-toggle input[type="checkbox"] {
  accent-color: #4f46e5;
}

.language-toggle:hover {
  background-color: #f3f4f6;
}
.languageToggleItem {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border: 1px solid var(--secondaryLight);
    border-radius: 4px;
}

.languageToggleItem input[type="checkbox"] {
    cursor: pointer;
}

.languageToggleItem label {
    font-size: 0.85rem;
    cursor: pointer;
}

.languageToggleItem.disabled {
    opacity: 0.5;
    background-color: var(--secondaryLight);
}

html,
body {
    height: 100%;
    width: 100%;
}

nav {
    padding-top: 0.3rem !important;
}

.popup {
    height: 100dvh;
    width: 100vw;
    position: fixed;
    top: 0;
    background-color: rgba(46, 46, 46, 0.167);
    -webkit-backdrop-filter: blur(1px);
    backdrop-filter: blur(1px);
    z-index: 9999;
    display: none;
    opacity: 0;
    align-items: center;
    justify-content: center;
    transition: all 0.3s linear;
}
.popup .center {
    background-color: var(--primaryLight);
    padding: 2rem;
    border-radius: 0.25rem;
    max-width: 23rem;
    text-align: center;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 1rem;
    position: relative;
}
.popup .center img {
    max-height: 3.5rem;
    -o-object-fit: contain;
    object-fit: contain;
}
.popup .center button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    background-color: var(--primary);
    color: var(--white);
}
.popup .center p {
    opacity: 0.8;
}
.popup .center i {
    font-size: 1.1rem;
}
.popup .center .closePopup {
    position: absolute;
    transform: translateX(-50%);
    left: 95%;
    top: 5%;
    cursor: pointer;
}

#main {
    width: 100%;
    height: 100%;
}
#main .page1 {
    width: 100%;
    height: 100%;
    padding-bottom: 0;
    padding-top: 9vh;
    display: flex;
    gap: 2.5rem;
    justify-content: space-between;
}
#main .page1 .heroArea {
    height: 100%;
    width: 65%;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    padding-bottom: 10vh;
    overflow-y: auto;
}
#main .page1 .heroArea::-webkit-scrollbar {
    display: none;
}
#main .page1 .heroArea > * {
    flex-shrink: 0;
}
#main .page1 .heroArea #heroArea-code {
    display: none;
}
#main .page1 .heroArea #heroArea-code.active {
    display: initial;
}
#main .page1 .heroArea #heroArea-code .heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
}
#main .page1 .heroArea #heroArea-code .problemId {
    width: 50%;
    position: relative;
    margin-bottom: 1rem;
}
#main .page1 .heroArea #heroArea-code .problemId .problemDiv {
    z-index: 9;
    position: absolute;
    background-color: #dedede;
    top: 100%;
    width: 100%;
    left: 0;
}
#main .page1 .heroArea #heroArea-code .problemId .problemDiv p {
    margin-bottom: 0;
    padding: 0.5rem;
    border-bottom: 1px solid rgb(185, 185, 185);
    font-size: 0.9rem;
    transition: 0.1s;
}
#main .page1 .heroArea #heroArea-code .problemId .problemDiv p:nth-last-child(1) {
    border: none;
}
#main .page1 .heroArea #heroArea-code .problemId .problemDiv p:hover {
    background-color: #000000;
    color: white;
}
#main .page1 .heroArea #heroArea-code .input p {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}
#main .page1 .heroArea #heroArea-code .input textarea {
    width: 100%;
    border: none;
    border-radius: 6px;
    resize: none;
    padding: 0.5rem;
}
#main .page1 .heroArea #heroArea-code .input input {
    width: 100%;
    border: none;
    border-radius: 6px;
    padding: 0.5rem;
}
#main .page1 .heroArea #heroArea-code .input select {
    width: 100%;
    border: none;
    color: white;
    background-color: #000000;
    border-radius: 6px;
    padding: 0.5rem 2rem 0.5rem 0.5rem;
}
#main .page1 .heroArea #heroArea-code .codeInput {
    margin-top: 2rem;
    margin-bottom: 2rem;
}
#main .page1 .heroArea #heroArea-code .codeInput .codeEditorHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#main .page1 .heroArea #heroArea-code .codeInput .codeEditorHeader .headLinks {
    display: flex;
}
#main .page1 .heroArea #heroArea-code .codeInput .codeEditorHeader .headLinks p {
    padding: 0.5rem 1rem;
    border-bottom: 2px solid transparent;
    transition: 0.3s;
}
#main .page1 .heroArea #heroArea-code .codeInput .codeEditorHeader .headLinks p.active {
    background-color: #202020;
    color: white;
}
#main .page1 .heroArea #heroArea-code .codeInput .codeEditorHeader select {
    background-color: #222222;
    color: white;
    border-radius: 3px;
    padding: 0.2rem 0.8rem;
}
#main .page1 .heroArea #heroArea-code .codeInput #mainCode,
#main .page1 .heroArea #heroArea-code .codeInput #templateCode,
#main .page1 .heroArea #heroArea-code .codeInput #solutionCode {
    height: 25rem;
    display: none;
    width: 100%;
    border: none;
    border-radius: 0px 6px 6px 6px;
    resize: none;
    padding: 0.5rem;
}
#main .page1 .heroArea #heroArea-code .codeInput #mainCode.active,
#main .page1 .heroArea #heroArea-code .codeInput #templateCode.active,
#main .page1 .heroArea #heroArea-code .codeInput #solutionCode.active {
    display: block;
}
#main .page1 .heroArea #heroArea-code .footer {
    gap: 3rem;
    display: flex;
}
#main .page1 .heroArea #heroArea-mcq {
    display: none;
}
#main .page1 .heroArea #heroArea-mcq.active {
    display: initial;
}
#main .page1 .heroArea #heroArea-mcq .heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
}
#main .page1 .heroArea #heroArea-mcq .multipleChoice {
    margin-bottom: 2rem;
}
#main .page1 .heroArea #heroArea-mcq .multipleChoice p {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}
#main .page1 .heroArea #heroArea-mcq .multipleChoice .choice {
    margin-bottom: 1rem;
    width: 100%;
    border: none;
    background-color: #dbdbdb;
    border-radius: 6px;
    resize: none;
    padding: 0.5rem;
}
#main .page1 .heroArea #heroArea-mcq .multipleChoice .choice .answer {
    display: flex;
    gap: 3px;
    align-items: center;
}
#main .page1 .heroArea #heroArea-mcq .multipleChoice .choice .answer label {
    margin-bottom: 0;
    font-weight: 400;
    font-size: 0.8rem;
}
#main .page1 .heroArea #heroArea-mcq .multipleChoice textarea {
    font-size: 1rem;
    width: 100%;
    border: none;
    background-color: transparent;
    border-radius: 6px;
    resize: none;
    padding: 0.5rem;
}
#main .page1 .heroArea #heroArea-mcq .input {
    margin-bottom: 2rem;
}
#main .page1 .heroArea #heroArea-mcq .input p {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}
#main .page1 .heroArea #heroArea-mcq .input textarea {
    width: 100%;
    border: none;
    background-color: #dbdbdb;
    border-radius: 6px;
    resize: none;
    padding: 0.5rem;
}
#main .page1 .heroArea #heroArea-mcq .input input {
    width: 100%;
    border: none;
    background-color: #dbdbdb;
    border-radius: 6px;
    padding: 0.5rem;
}
#main .page1 .heroArea #heroArea-mcq .input #snippetCode {
    width: 100%;
    height: 400px;
}
#main .page1 .heroArea #heroArea-video {
    display: none;
}
#main .page1 .heroArea #heroArea-video.active {
    display: initial;
}
#main .page1 .heroArea .video {
    width: 100%;
    aspect-ratio: 16/9;
    border-radius: 7px;
}
#main .page1 .heroArea .metaData {
    display: flex;
    justify-content: center;
    flex-direction: column;
    gap: 0.7rem;
    margin-top: 0.9rem;
}
#main .page1 .heroArea .metaData .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
#main .page1 .heroArea .metaData .title h3,
#main .page1 .heroArea .metaData .title h2,
#main .page1 .heroArea .metaData .title h1 {
    font-weight: 500;
}
#main .page1 .heroArea .metaData .title .buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.2rem;
    padding-right: 1px;
}
#main .page1 .heroArea .metaData .title .buttons button {
    background-color: var(--primary);
    color: var(--white) !important;
    gap: 0.3rem;
    outline: 1px solid var(--primary);
    padding: 7px 1.4rem;
}
#main .page1 .heroArea .metaData .courseDetail {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}
#main .page1 .heroArea .metaData .courseDetail .text h4 {
    font-weight: 600;
}
#main .page1 .heroArea .metaData .courseDetail .text small {
    font-weight: 600;
    opacity: 0.6;
}
#main .page1 .heroArea .metaData .courseDetail .like {
    display: flex;
    align-items: center;
    justify-content: center;
}
#main .page1 .heroArea .metaData .courseDetail .like i {
    display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .like i.active {
    display: none;
}
#main .page1 .heroArea .metaData .courseDetail .like.liked .likes i {
    display: none;
}
#main .page1 .heroArea .metaData .courseDetail .like.liked .likes i.active {
    display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .like.liked .dislikes i {
    display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .like.liked .dislikes i.active {
    display: none;
}
#main .page1 .heroArea .metaData .courseDetail .like.disLiked .likes i {
    display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .like.disLiked .likes i.active {
    display: none;
}
#main .page1 .heroArea .metaData .courseDetail .like.disLiked .dislikes i {
    display: none;
}
#main .page1 .heroArea .metaData .courseDetail .like.disLiked .dislikes i.active {
    display: initial;
}
#main .page1 .heroArea .metaData .courseDetail .like .likes {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid rgba(0, 0, 0, 0.158);
    padding: 0.3rem 0.6rem;
    background-color: var(--secondaryLight);
    border-top-left-radius: 1.3rem;
    border-bottom-left-radius: 1.3rem;
    gap: 0.3rem;
    height: 2.1rem;
    font-size: 0.95rem;
    transition: all 0.3s linear;
}
#main .page1 .heroArea .metaData .courseDetail .like .likes:active {
    scale: 0.9;
}
#main .page1 .heroArea .metaData .courseDetail .like .likes small {
    font-weight: 600;
}
#main .page1 .heroArea .metaData .courseDetail .like .dislikes {
    transition: all 0.3s linear;
    cursor: pointer;
    font-size: 0.85rem;
    height: 2.1rem;
    border-top-right-radius: 1.3rem;
    border-bottom-right-radius: 1.3rem;
    padding: 0.3rem 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--secondaryLight);
}
#main .page1 .heroArea .metaData .courseDetail .like .dislikes:active {
    scale: 0.9;
}
#main .page1 .heroArea .links {
    width: 100%;
    background-color: var(--secondaryLight);
    padding: 1rem;
    border-radius: 7px;
    display: flex;
    justify-content: center;
    flex-direction: column;
}
#main .page1 .heroArea .links p {
    margin-bottom: 0.4rem;
    font-weight: 500;
    font-size: 0.85rem;
}
#main .page1 .heroArea .links .link small {
    font-weight: 600;
}
#main .page1 .heroArea .links .link small a {
    color: var(--primaryDark);
}
#main .page1 .heroArea .description {
    background-color: var(--secondaryLight);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
    position: relative;
    border-radius: 7px;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    padding-bottom: 4rem;
    flex-shrink: 0;
}
#main .page1 .heroArea .description small {
    font-weight: 500;
    font-size: 0.8rem;
    opacity: 0.8;
}
#main .page1 .heroArea .description p {
    font-weight: 500;
    line-height: 1.3rem;
    font-size: 0.95rem;
}
#main .page1 .heroArea .description .showMore {
    cursor: pointer;
    height: 3rem;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background-image: linear-gradient(to top, var(--secondaryLight), transparent);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 0.2rem;
    color: var(--primaryDark);
    transition: all 0.3s linear;
}
#main .page1 .heroArea .description .showMore:active p {
    transition: all 0.3s linear;
    scale: 0.9;
}
#main .page1 .heroArea .comments {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 0.5rem;
    overflow-y: hidden;
}
#main .page1 .heroArea .comments .addComment,
#main .page1 .heroArea .comments .addCommentDummy {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}
#main .page1 .heroArea .comments .addComment .top,
#main .page1 .heroArea .comments .addCommentDummy .top {
    display: flex;
    justify-content: space-between;
}
#main .page1 .heroArea .comments .addComment .top .arrow,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow {
    display: none;
}
#main .page1 .heroArea .comments .addComment .top .arrow.close .ri-arrow-down-s-line,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow.close .ri-arrow-down-s-line {
    display: none;
}
#main .page1 .heroArea .comments .addComment .top .arrow.close .ri-arrow-up-s-line,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow.close .ri-arrow-up-s-line {
    display: initial;
}
#main .page1 .heroArea .comments .addComment .top .arrow.open .ri-arrow-down-s-line,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow.open .ri-arrow-down-s-line {
    display: initial;
}
#main .page1 .heroArea .comments .addComment .top .arrow.open .ri-arrow-up-s-line,
#main .page1 .heroArea .comments .addCommentDummy .top .arrow.open .ri-arrow-up-s-line {
    display: none;
}
#main .page1 .heroArea .comments .addComment .bottom,
#main .page1 .heroArea .comments .addCommentDummy .bottom {
    display: flex;
    align-items: center;
    gap: 0.7rem;
}
#main .page1 .heroArea .comments .addComment .bottom .user,
#main .page1 .heroArea .comments .addCommentDummy .bottom .user {
    display: flex;
    align-self: flex-start;
    padding-top: 0.3rem;
}
#main .page1 .heroArea .comments .addComment .bottom .user img,
#main .page1 .heroArea .comments .addCommentDummy .bottom .user img {
    height: 1.5rem;
    width: 1.5rem;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 50%;
}
#main .page1 .heroArea .comments .addComment .bottom textarea,
#main .page1 .heroArea .comments .addCommentDummy .bottom textarea {
    width: 100%;
    padding: 0.8rem 0.4rem;
    resize: none;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid var(--primaryDark);
    opacity: 0.8;
}
#main .page1 .heroArea .comments .addComment .bottom textarea::-webkit-scrollbar,
#main .page1 .heroArea .comments .addCommentDummy .bottom textarea::-webkit-scrollbar {
    display: none;
}
#main .page1 .heroArea .comments .addComment:has(textarea:focus) .actions,
#main .page1 .heroArea .comments .addCommentDummy:has(textarea:focus) .actions {
    display: flex;
}
#main .page1 .heroArea .comments .addComment .post,
#main .page1 .heroArea .comments .addCommentDummy .post {
    opacity: 0;
    pointer-events: none;
    transition: all 0.1s linear;
}
#main .page1 .heroArea .comments .addComment .actions,
#main .page1 .heroArea .comments .addCommentDummy .actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 1em;
    display: none;
}
#main .page1 .heroArea .comments .addComment .actions .action,
#main .page1 .heroArea .comments .addCommentDummy .actions .action {
    width: -moz-fit-content;
    width: fit-content;
    padding: 0.7rem 1rem;
    background-color: var(--ternary);
    opacity: 0.7;
    border-radius: 1.7rem;
}
#main .page1 .heroArea .comments .addComment .actions .addcomment,
#main .page1 .heroArea .comments .addCommentDummy .actions .addcomment {
    background-color: transparent;
    pointer-events: none;
}
#main .page1 .heroArea .comments .addComment.valid,
#main .page1 .heroArea .comments .addCommentDummy.valid {
    display: flex !important;
}
#main .page1 .heroArea .comments .addComment.valid .actions,
#main .page1 .heroArea .comments .addCommentDummy.valid .actions {
    display: flex !important;
}
#main .page1 .heroArea .comments .addComment.valid .actions .addcomment,
#main .page1 .heroArea .comments .addCommentDummy.valid .actions .addcomment {
    background-color: var(--primary);
    color: var(--primaryLight);
    pointer-events: initial;
}
#main .page1 .heroArea .comments .comment {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.9rem;
}
#main .page1 .heroArea .comments .comment .left img {
    height: 1.5rem;
    width: 1.5rem;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 50%;
}
#main .page1 .heroArea .comments .comment .right {
    width: 100%;
}
#main .page1 .heroArea .comments .comment .right .metaData {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 0.2rem;
    align-items: flex-end;
    margin-bottom: 0.2rem;
    margin-top: 0;
}
#main .page1 .heroArea .comments .comment .right .metaData p {
    width: -moz-fit-content;
    width: fit-content;
    display: inline-block;
    opacity: 0.7;
    font-size: 0.8rem;
}
#main .page1 .heroArea .comments .comment .right .metaData .user {
    font-weight: 500;
    font-size: 1rem;
}
#main .page1 .heroArea .comments .comment .right .metaData .time {
    opacity: 0.4;
}
#main .page1 .heroArea .comments .comment .right p {
    font-size: 0.95rem;
}
#main .page1 .heroArea .comments .comment .delete {
    opacity: 0;
    pointer-events: none;
    transition: all 0.1s linear;
    position: relative;
}
#main .page1 .heroArea .comments .comment .delete .options {
    display: none;
    flex-direction: column;
    position: absolute;
    transform: translate(0%, -50%);
    right: 100%;
    top: 50%;
    background-color: var(--secondaryLight);
    padding: 0.7rem;
    border-radius: 0.3rem;
}
#main .page1 .heroArea .comments .comment .delete:hover .options {
    display: flex;
}
#main .page1 .heroArea .comments .comment:hover .delete {
    opacity: 1;
    pointer-events: initial;
}
#main .page1 .chapters {
    width: calc(35% - 2.5rem);
    display: flex;
    height: 100%;
    flex-direction: column;
    gap: 0.7rem;
    padding: 0 0.7rem;
}
#main .page1 .chapters .live_class_notes {
    width: 100%;
    padding: 0.5rem;
    border: none;
    border-radius: 3px;
    background-color: var(--secondaryLight);
}
#main .page1 .chapters .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 0.9rem;
}
#main .page1 .chapters .modules {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding-bottom: 10vh;
}
#main .page1 .chapters .modules::-webkit-scrollbar {
    display: none;
}
#main .page1 .chapters .modules .moduleWrapper {
    overflow: hidden;
    height: 40px;
    margin-bottom: 10px;
    transition: 0.2s;
}
#main .page1 .chapters .modules .moduleWrapper.open {
    height: -moz-fit-content;
    height: fit-content;
}
#main .page1 .chapters .modules .moduleWrapper.open .moduleWrapperArrow {
    transform: rotate(90deg);
}
#main .page1 .chapters .modules .moduleWrapper > .title {
    cursor: pointer;
    padding: 0.6rem 0.7rem;
    background-color: var(--primaryDark);
    border-radius: 4px;
    width: 100%;
    color: white;
    display: flex;
    gap: 0.5rem;
    flex-direction: column;
    justify-content: space-between;
    top: 0%;
    z-index: 99;
}
#main .page1 .chapters .modules .moduleWrapper > .title .controlls {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#main .page1 .chapters .modules .moduleWrapper > .title .controlls .name {
    width: 100%;
    font-weight: 500;
    font-size: 0.9rem;
}
#main .page1 .chapters .modules .moduleWrapper > .title .controlls .name input {
    width: 100%;
    background-color: transparent;
    border: none;
    outline: none;
    color: var(--primaryLight);
    opacity: 1;
}
#main .page1 .chapters .modules .moduleWrapper > .title .controlls button {
    all: initial;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 0.8rem;
    gap: 8px;
}
#main .page1 .chapters .modules .moduleWrapper > .title .controlls button i {
    cursor: pointer;
    color: var(--primaryLight);
}
#main .page1 .chapters .modules .moduleWrapper > .title .controlls .ri-arrow-right-s-line {
    transition: 0.2s;
}
#main .page1 .chapters .modules .moduleWrapper > .title .seperator {
    width: 100%;
    height: 1px;
    background-color: var(--primaryLight);
}
#main .page1 .chapters .modules .moduleWrapper > .title .comingSoonModuleDate {
    width: 100%;
    gap: 0.5rem;
    justify-content: right;
    align-items: center;
    display: flex;
}
#main .page1 .chapters .modules .moduleWrapper > .title .comingSoonModuleDate input {
    font-size: 0.8rem;
    border: none;
    padding: 0.2rem 0.2rem 0.1rem 0.2rem;
    border-radius: 2px;
    outline: none;
    background-color: var(--primaryLight);
    color: var(--primaryDark);
}
#main .page1 .chapters .modules .moduleWrapper > .title .comingSoonModuleDate p {
    font-size: 0.9rem;
}
#main .page1 .chapters .modules .moduleWrapper .module {
    overflow: hidden;
    height: 40px;
    margin-top: 10px;
    padding-left: 10px;
    width: 100%;
    margin-bottom: 5px;
}
#main .page1 .chapters .modules .moduleWrapper .module.open {
    height: -moz-fit-content;
    height: fit-content;
}
#main .page1 .chapters .modules .moduleWrapper .module.open .moduleArrow {
    transform: rotate(90deg);
}
#main .page1 .chapters .modules .moduleWrapper .module .addModule {
    background-color: rgb(0, 0, 0) !important;
    text-align: center;
    color: white;
    justify-content: center !important;
}
#main .page1 .chapters .modules .moduleWrapper .module .title {
    cursor: pointer;
    padding: 0.6rem 0.7rem;
    background-color: var(--secondaryLight);
    border-radius: 4px;
    width: 100%;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    top: 6%;
    gap: 0.5rem;
    z-index: 90;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .controlls {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .controlls .name {
    width: 100%;
    font-weight: 500;
    font-size: 0.9rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .controlls .name input {
    width: 100%;
    background-color: transparent;
    border: none;
    outline: none;
    color: black;
    opacity: 1;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .controlls button {
    all: initial;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 0.8rem;
    gap: 8px;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .controlls button i {
    cursor: pointer;
    color: var(--primaryDark);
}
#main .page1 .chapters .modules .moduleWrapper .module .title .seperator {
    width: 100%;
    height: 1px;
    background-color: #202020;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .comingSoonDate {
    width: 100%;
    gap: 0.5rem;
    justify-content: right;
    align-items: center;
    display: flex;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .comingSoonDate input {
    font-size: 0.8rem;
    border: none;
    padding: 0.2rem 0.2rem 0.1rem 0.2rem;
    border-radius: 2px;
    outline: none;
    background-color: #222222;
    color: white;
}
#main .page1 .chapters .modules .moduleWrapper .module .title .comingSoonDate p {
    font-size: 0.9rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures {
    padding: 0 0.7rem;
    padding-top: 0.7rem;
    padding-right: 0;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    flex-direction: column;
    height: -moz-fit-content !important;
    height: fit-content !important;
    transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
    overflow: hidden;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .currentPlaying {
    background-color: #cbcbcb;
    pointer-events: none;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .currentPlaying .left .thumbnail {
    position: relative;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .currentPlaying .left .thumbnail::before {
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
    content: "\efd6";
    z-index: 2;
    font-size: 2.5rem;
    color: var(--secondaryLight);
    font-family: remixicon !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .currentPlaying .left .thumbnail img {
    opacity: 0.8 !important;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures.close {
    height: 0 !important;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture[data-type="comingSoon"] {
    color: rgb(90, 90, 90);
    background-color: rgb(189, 189, 189);
    height: 10rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture {
    border: 2px solid transparent;
    margin-bottom: 10px;
    background-color: rgba(185, 185, 185, 0.744);
    padding: 0.65rem;
    width: 100%;
    gap: 0.7rem;
    position: relative;
    transition: all 0.3s linear;
    cursor: pointer;
    border-radius: 0.25rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture small {
    font-size: 0.7rem;
    font-weight: 600;
    margin-bottom: 3px;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.lock {
    cursor: default;
    background-color: #cbcbcb;
    position: relative;
    margin-bottom: 0.25rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.lock::before {
    content: "\eecc";
    font-family: remixicon !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
    z-index: 25;
    color: #ffffff;
    font-size: 3.5rem;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(24, 24, 24, 0);
    -webkit-backdrop-filter: blur(1px);
    backdrop-filter: blur(1px);
    border-radius: 0.25rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.lock .rgiht {
    opacity: 0.5;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture:active {
    scale: 0.98;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture.completed::before {
    content: "\eb80";
    font-family: "remixicon" !important;
    font-style: normal;
    position: absolute;
    top: 4%;
    left: 0.7%;
    z-index: 98;
    color: var(--primary);
    border-radius: 50%;
    background: radial-gradient(#fff, #fff, transparent, transparent);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left {
    width: 8rem;
    height: 4.5rem;
    border-radius: 0.25rem;
    overflow: hidden;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail {
    all: inherit;
    position: relative;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail img {
    all: inherit;
    border-radius: 4px;
    -o-object-fit: cover;
    object-fit: cover;
    transition: all 0.3s linear;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail img:hover {
    transform: scale(1.05);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .left .thumbnail .duration {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    right: 2%;
    bottom: 2%;
    color: var(--primaryLight);
    background-color: rgba(34, 34, 34, 0.5490196078);
    padding: 0.1rem 0.4rem;
    border-radius: 5px;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .righ {
    height: -moz-fit-content;
    height: fit-content;
    width: 100%;
    display: flex;
    gap: 0.6rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #newCheck {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 5px;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #newCheck div {
    display: flex;
    align-items: center;
    gap: 5px;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #newCheck label {
    font-size: 0.8rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht {
    display: flex;
    gap: 0.6rem;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .inputFields {
    width: 100%;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .tags {
    display: flex;
    align-items: center;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .tags .tag {
    padding: 0.3rem 0.8rem;
    border-radius: 1rem;
    color: var(--primaryLight);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .tags .tag.code {
    background-color: var(--error);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .tags .tag.assesment {
    background-color: var(--primary);
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .comingSoon {
    overflow: visible !important;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .lectureTitle {
    margin-bottom: 0.5rem;
    display: flex;
    flex-direction: column;
    font-size: 0.95rem;
    line-height: 0.9rem;
    font-weight: 500;
    height: -webkit-fill-available;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture .rgiht .lectureTitle input {
    width: 100%;
    display: block;
    background-color: var(--secondaryLight);
    border: none;
    margin-bottom: 3px;
    border-radius: 5px;
    padding: 5px;
    outline: none;
    color: black;
    opacity: 1;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #allLinks #links {
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(0, 0, 0, 0.184);
    margin-top: 15px;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #allLinks #links .linkName,
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #allLinks #links .linkLink {
    width: 100%;
    background-color: var(--secondaryLight);
    border: none;
    margin-bottom: 3px;
    border-radius: 5px;
    padding: 5px;
    outline: none;
    color: black;
    opacity: 1;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #allLinks #links > div {
    display: flex;
    flex-direction: column;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #allLinks #links > div > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
#main .page1 .chapters .modules .moduleWrapper .module .lectures .lecture #allLinks #links > div > div input {
    width: 85% !important;
}

.codeInput .ace_editor,
.ace_editor * {
    font-family: "Monaco", "Menlo", "Ubuntu Mono", "Droid Sans Mono", "Consolas", monospace !important;
    font-size: 0.85rem !important;
    font-weight: 400 !important;
    letter-spacing: 0 !important;
}

.lectureBtn {
    display: flex;
    justify-content: center;
    align-items: center;
}
.lectureBtn i {
    font-size: 0.9rem;
}

.blue-background-class {
    background-color: pink;
} /*# sourceMappingURL=gotoclassroomedit.css.map */
