/**
 * This script is used to test the email sending functionality.
 * It will send both a plain HTML email and a template-based email using the existing functions.
 */

require("dotenv").config(); // Load environment variables
const { testEmailSending } = require("./utils/nodemailer");

// Email to receive the test emails
const recipientEmail = process.argv[2];

if (!recipientEmail) {
    console.error("Please provide an email address as an argument.");
    console.log("Usage: node test-email.js <EMAIL>");
    process.exit(1);
}

async function runTest() {
    console.log(`Starting email test to ${recipientEmail}...`);

    try {
        const result = await testEmailSending(recipientEmail);

        if (result.success) {
            console.log("\n✅ Email Test Successful!");
            console.log("==============================================");
            console.log("Plain HTML Email ID:", result.plainEmailResult.messageId);
            console.log("Template Email ID:", result.templateEmailResult.messageId);
            console.log("==============================================");
            console.log(`Two test emails have been sent to ${recipientEmail}`);
            console.log("Please check your inbox (and spam folder) to confirm receipt.");
        } else {
            console.error("\n❌ Email Test Failed!");
            console.error("==============================================");
            console.error("Error:", result.error);
            console.error("==============================================");
            console.error("Check your environment variables and network connection.");
        }
    } catch (error) {
        console.error("\n❌ Unexpected error during test:", error);
    }
}

runTest().catch(console.error);
