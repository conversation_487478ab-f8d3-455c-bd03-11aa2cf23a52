const mongoose = require("mongoose");
require("dotenv").config();

// Connect to MongoDB using the same config as the main app
mongoose.connect(process.env.MONGODB_URI || "mongodb://localhost:27017/sheryians", {
    useNewUrlParser: true,
    useUnifiedTopology: true,
});

// Import the SRC model
const Src = require("./Models/src");

// Dummy data arrays
const firstNames = ["<PERSON>ara<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ihaan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>har<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"];

con<PERSON> last<PERSON><PERSON><PERSON> = ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Thakkar", "Vyas", "Pandya", "Reddy", "Rao", "Naidu", "Chandra", "Prasad", "Sai", "Ravi", "Krishna", "Venkata", "Sree", "Khan", "Ali", "Ahmed", "Rahman", "Hassan", "Hussain", "Ansari", "Qureshi", "Malik", "Sheikh", "Das", "Sen", "Roy", "Ghosh", "Bose", "Mukherjee", "Banerjee", "Chakraborty", "Bhattacharya", "Dutta"];

const fields = ["Web Development", "Mobile App Development", "Data Science", "Machine Learning", "AI Development", "Backend Development", "Frontend Development", "Full Stack Development", "DevOps", "Cybersecurity", "Game Development", "UI/UX Design", "Digital Marketing", "Cloud Computing", "Blockchain Development"];

const colleges = ["IIT Delhi", "IIT Bombay", "IIT Kanpur", "IIT Madras", "IIT Kharagpur", "IIT Roorkee", "IIT Guwahati", "NIT Trichy", "NIT Warangal", "NIT Surathkal", "NIT Calicut", "NIT Kurukshetra", "NIT Jaipur", "BITS Pilani", "BITS Goa", "BITS Hyderabad", "VIT Vellore", "VIT Chennai", "SRM University", "Manipal University", "Amity University", "LPU Jalandhar", "Chitkara University", "Thapar University", "Delhi University", "Mumbai University", "Pune University", "Anna University", "Jadavpur University", "BIT Mesra", "IIIT Hyderabad", "IIIT Bangalore", "IIIT Delhi", "JSS Academy", "PES University"];

const cities = ["Mumbai", "Delhi", "Bangalore", "Hyderabad", "Chennai", "Kolkata", "Pune", "Ahmedabad", "Jaipur", "Surat", "Lucknow", "Kanpur", "Nagpur", "Indore", "Thane", "Bhopal", "Visakhapatnam", "Pimpri", "Patna", "Vadodara", "Ghaziabad", "Ludhiana", "Agra", "Nashik", "Faridabad", "Meerut", "Rajkot", "Kalyan", "Vasai", "Varanasi", "Srinagar", "Aurangabad", "Dhanbad", "Amritsar", "Navi Mumbai", "Allahabad", "Ranchi", "Howrah", "Coimbatore", "Jabalpur", "Gwalior", "Vijayawada", "Jodhpur", "Madurai", "Raipur", "Kota", "Chandigarh", "Guwahati", "Solapur", "Hubli", "Tiruchirappalli"];

const degrees = ["B.Tech Computer Science", "B.Tech Information Technology", "B.Tech Electronics", "B.Tech Mechanical", "B.Tech Civil", "B.Tech Electrical", "BCA", "B.Sc Computer Science", "B.Sc IT", "MCA", "M.Tech Computer Science", "M.Tech Software Engineering", "MBA Technology", "B.Com Computer Application", "BBA", "B.Sc Mathematics", "B.Sc Physics", "B.Sc Chemistry", "Diploma Computer Science", "Diploma IT"];

const yearsOfStudy = ["1st Year", "2nd Year", "3rd Year", "4th Year", "Final Year", "Graduated"];

const statuses = ["pending", "good fit", "best fit", "not fit"];
const phaseStatuses = ["pending", "accepted", "rejected"];

const domains = ["gmail.com", "yahoo.com", "outlook.com", "hotmail.com", "student.edu"];

// Generate random data
function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomPhone() {
    return (
        "9" +
        Math.floor(Math.random() * 1000000000)
            .toString()
            .padStart(9, "0")
    );
}

function getRandomEmail(firstName, lastName) {
    const variations = [`${firstName.toLowerCase()}.${lastName.toLowerCase()}`, `${firstName.toLowerCase()}${lastName.toLowerCase()}`, `${firstName.toLowerCase()}_${lastName.toLowerCase()}`, `${firstName.toLowerCase()}${Math.floor(Math.random() * 100)}`, `${lastName.toLowerCase()}.${firstName.toLowerCase()}`];
    return `${getRandomElement(variations)}@${getRandomElement(domains)}`;
}

function generateUniqueId() {
    const { customAlphabet } = require("nanoid");
    const base62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    const nanoid = customAlphabet(base62, 8);
    return nanoid();
}

function getRandomSocialProfile(firstName) {
    const platforms = ["linkedin", "github", "twitter", "instagram"];
    const platform = getRandomElement(platforms);
    return `https://${platform}.com/${firstName.toLowerCase()}${Math.floor(Math.random() * 1000)}`;
}

function getRandomWorkLink() {
    const platforms = ["github.com", "gitlab.com", "codepen.io", "replit.com"];
    return `https://${getRandomElement(platforms)}/project${Math.floor(Math.random() * 10000)}`;
}

function generateRandomDate(daysBack) {
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * daysBack));
    return date;
}

// Generate dummy submissions
async function generateDummyData() {
    try {
        console.log("Starting to generate dummy SRC data...");

        // Clear existing data (optional - comment out if you want to keep existing data)
        // await Src.deleteMany({});
        // console.log('Cleared existing data');

        const submissions = [];
        const usedEmails = new Set();
        const usedPhones = new Set();

        for (let i = 0; i < 500; i++) {
            const firstName = getRandomElement(firstNames);
            const lastName = getRandomElement(lastNames);
            const fullname = `${firstName} ${lastName}`;

            // Ensure unique email
            let email;
            do {
                email = getRandomEmail(firstName, lastName);
            } while (usedEmails.has(email));
            usedEmails.add(email);

            // Ensure unique phone
            let phone;
            do {
                phone = getRandomPhone();
            } while (usedPhones.has(phone));
            usedPhones.add(phone);

            const submission = {
                fullname,
                phone,
                email,
                field: getRandomElement(fields),
                college: getRandomElement(colleges),
                city: getRandomElement(cities),
                degree: getRandomElement(degrees),
                yearOfStudy: getRandomElement(yearsOfStudy),
                socialProfile: Math.random() > 0.3 ? getRandomSocialProfile(firstName) : undefined,
                techBuild: Math.random() > 0.5 ? `Built a ${getRandomElement(["web app", "mobile app", "game", "AI model", "chatbot"])} using ${getRandomElement(["React", "Flutter", "Unity", "Python", "Node.js"])}` : undefined,
                worklink: Math.random() > 0.4 ? getRandomWorkLink() : undefined,
                status: getRandomElement(statuses),
                phases: {
                    phase1: {
                        status: getRandomElement(phaseStatuses),
                        updatedAt: generateRandomDate(30),
                    },
                    phase2: {
                        status: getRandomElement(phaseStatuses),
                        updatedAt: generateRandomDate(20),
                    },
                    phase3: {
                        status: getRandomElement(phaseStatuses),
                        updatedAt: generateRandomDate(10),
                    },
                },
                createdAt: generateRandomDate(60),
                updatedAt: generateRandomDate(30),
            };

            submissions.push(submission);

            // Progress indicator
            if ((i + 1) % 100 === 0) {
                console.log(`Generated ${i + 1}/500 submissions...`);
            }
        }

        // Insert all submissions
        console.log("Inserting submissions into database...");
        await Src.insertMany(submissions);

        console.log("✅ Successfully generated and inserted 500 dummy SRC submissions!");

        // Show some statistics
        const stats = await Src.aggregate([
            {
                $group: {
                    _id: "$status",
                    count: { $sum: 1 },
                },
            },
        ]);

        console.log("\n📊 Status Distribution:");
        stats.forEach((stat) => {
            console.log(`${stat._id}: ${stat.count} submissions`);
        });

        const phaseStats = await Src.aggregate([
            {
                $group: {
                    _id: {
                        phase1: "$phases.phase1.status",
                        phase2: "$phases.phase2.status",
                        phase3: "$phases.phase3.status",
                    },
                    count: { $sum: 1 },
                },
            },
            { $limit: 10 },
        ]);

        console.log("\n📈 Sample Phase Combinations:");
        phaseStats.forEach((stat) => {
            console.log(`Phase1: ${stat._id.phase1}, Phase2: ${stat._id.phase2}, Phase3: ${stat._id.phase3} - ${stat.count} submissions`);
        });

        process.exit(0);
    } catch (error) {
        console.error("❌ Error generating dummy data:", error);
        process.exit(1);
    }
}

// Example: Insert/update a single SRC document based on provided data
async function upsertSingleSRC() {
    const data = {
        fullname: "Aayush",
        phone: "7024920816",
        email: "<EMAIL>",
        expertise: ["Web Development", "Web 3.0", "Product Thinker", "Electronics & IoT"],
        worklink: ["www.google.com", "www.facebook.com", "www.twitter.com"],
        city: "Bhopal",
        thoughts: "Bhosdipanti",
        applied_for_role: "none",
        status: "pending",
        phases: {
            phase1: { status: "accepted" },
            phase2: { status: "pending" },
        },
        uniqueId: "Bo510nT8",
    };
    await Src.findOneAndUpdate({ uniqueId: data.uniqueId }, { $set: data }, { upsert: true, new: true });
    console.log("Upserted SRC for uniqueId:", data.uniqueId);
}

// Run the script
generateDummyData();
// upsertSingleSRC().then(() => process.exit(0)).catch(err => { console.error(err); process.exit(1); });
