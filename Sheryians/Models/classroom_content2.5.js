const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const lectureSchema = new Schema({
    type: { type: String, required: true, enum: ['video', 'code', 'mcq', 'pdf'] },
    order: { type: Number, required: true },
    // For Video lectures:
    guid: { type: String },
    mentioned_links: { type: Array, default: [] },
    views : { type: Number, default: 0 },
    comments: [{ type: Schema.Types.ObjectId, ref: 'Comments_Classroom2' }],
    likes: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    dislikes: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    links:[{naam:{type:String},link:{type:String}}],

    // For coding problems:
    problemId: { type: String },
    problem_name: { type: String },
    main_code: { type: String },
    code_template: { type: String },
    solution_code: { type: String },
    compilerId: { type: String },
    difficulty: { type: String },
    code: {
        type: Map,
        of: new mongoose.Schema({
            mainCode: String,
            templateCode: String,
            solutionCode: String,
            compilerId: String,
            enabled: { type: Boolean, default: true },
            default: { type: Boolean, default: false },
        }, { _id: false })
    },
    // For MCQ or PDF:
    title: { type: String},
    question: { type: String },
    questionImage: { type: String },
    options: { type: Array, default: [] },
    explanation: { type: String },
    explanationVideo: { type: String },
    correctOption: { type: Number },
    codeSnippet:{type:String},
    codeSnippetLanguage:{type:String},
    pdf: { type: String },

    // common fields
    comingSoon:{type:Boolean , default:false},
    comingSoonDetails:{
        title:{
            type:String,
        },
        date:{
            type:Number
        }
    },
    newLecture:{type:Boolean,default:false},
    points: { type: String },
}, {
    timestamps: true
});

const moduleSchema = new Schema({
    courseId: { type: Schema.Types.ObjectId, ref : 'Course', required: true },
    title: { type: String, required: true },
    order: { type: Number, required: true },
    lectures: [lectureSchema], 
    comingSoonDate:{ type:Date }
}, {
    timestamps: true
});

const moduleWrapperSchema = new Schema({
    courseId: { type: Schema.Types.ObjectId, ref: 'Course', required: true },
    title: { type: String, required: true },
    order: { type: Number, required: true },
    modules: [moduleSchema],
    comingSoonDate:{ type:Date }
}, {
    timestamps: true
});

//create index for courseId
moduleWrapperSchema.index({ courseId: 1 });

const classroom_content2 = mongoose.model('classroom_content_2.5', moduleWrapperSchema);
module.exports = classroom_content2;