const mongoose = require("mongoose")

const testCaseSchema = mongoose.Schema({
    stdin: String,
    stdout: String,
    expectedOutput: String,
    passed: Boolean,
    error:String,
    executionTime: Number, // in milliseconds
    memoryUsage: Number // in kilobytes
});



const submissionSchema = mongoose.Schema({
    userId:{
        type:mongoose.Schema.Types.ObjectId,
        ref:"users"
    },
    questionId:{
        type:mongoose.Schema.Types.ObjectId,
        ref:"classroom_content_2.5"
    },
    language:{
        type:String
    },
    hasSeenSolution:{
        type:Boolean,
        default:false
    },
    submission:[{
        testcase:[testCaseSchema],
        date:String,
        language:String,
        code:String
    }],
    status:{
        type:String
    },
    code:{
        type:String
    },
    seenSolution:{
        type:Boolean,
        default:false
    },
    isExecuting:{
        type:Boolean,
        default:false
    }
},{timestamps:true})

submissionSchema.index({ userId: 1,questionId: 1 });
submissionSchema.index({ userId: 1,questionId: 1,language:1 });

module.exports = mongoose.model("submissionStorage",submissionSchema)