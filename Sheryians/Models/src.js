const mongoose = require("mongoose");
let nanoid;

// Use lazy dynamic import to avoid top-level await
const generateBase62Id = () => {
    if (!nanoid) {
        const { customAlphabet } = require('nanoid');
        nanoid = customAlphabet(base62, 8);
    }
    return nanoid();
};

const srcSchema = new mongoose.Schema({
    fullname: { type: String, required: [true, "Full name is required."] },
    phone: { type: String, required: [true, "Phone number is required."], unique: true },
    email: { type: String, required: [true, "Email is required."], unique: true },
    expertise: [{ type: String, enum: ["AI/ML", "Web Development", "Cybersecurity", "DevOps", "Product Thinker", "UI/UX", "Cinematography", "Video Editing", "Social Strategist", "Electronics & IoT", "Other", "Web 3.0"], default: "SRC", required: [true, "expertise is required."] }],
    worklink: [{ type: String }],
    uniqueId: { type: String, unique: true, default: generateBase62Id },
    city: { type: String, required: [true, "City is required."] },
    thoughts: { type: String, maxLength: 600 },
    applied_for_role: {
        type: String,
        enum: ["none", "Web Developer", "AI / ML Engineer", "Cybersecurity Lead", "DevOps Engineer", "Creative Technologist", "Product Thinker", "UI/UX Designer", "Video Editor / Media Lead", "Content & Social Strategist", "Operations Lead", "Vision & Strategy Lead"],
        required: [true, "Applied for role is required."],
        default: "none",
    },
    status: {
        type: String,
        enum: ["pending", "good fit", "best fit", "not fit"],
        default: "pending",
    },
    phases: {
        phase1: {
            status: { type: String, default: "accepted" },
        },
        phase2: {
            status: { type: String, enum: ["pending", "accepted", "rejected"], default: "pending" },
        },
    },
});

// Create indexes for email and phone fields for faster lookups
srcSchema.index({ email: 1 });
srcSchema.index({ phone: 1 });

module.exports = mongoose.model("Src", srcSchema);
