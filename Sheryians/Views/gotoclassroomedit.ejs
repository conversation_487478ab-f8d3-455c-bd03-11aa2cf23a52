<%- include('Partials/newHeaderPart1.ejs') %>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/simple-notify@0.5.5/dist/simple-notify.min.css" />

  <link rel="stylesheet" href="/css/gotoclassroomedit.css">
  <script src="/ace-builds/src/ace.js" type="text/javascript" charset="utf-8"></script>
  <script src="/ace-builds/src-noconflict/ext-language_tools.js"></script>
  <script src="/ace-builds/src-noconflict/ext-emmet.js"></script>
  <script type="text/javascript" src="/ace-builds/src-noconflict/mode-c_cpp.js"></script>
  <script type="text/javascript" src="/ace-builds/src-noconflict/mode-java.js"></script>
  <script type="text/javascript" src="/ace-builds/src-noconflict/mode-python.js"></script>
  <script type="text/javascript" src="/ace-builds/src-noconflict/mode-javascript.js"></script>
  <script type="text/javascript" src="/ace-builds/src-noconflict/mode-html.js"></script>
  <script type="text/javascript" src="/ace-builds/src-noconflict/mode-css.js"></script>
  <%- include('Partials/newHeaderPart2.ejs') %>

    <section class="page1 page">

      <div class="heroArea">
        <div class="loader"
          style="display: none; justify-content: center; align-items: center; height: 100%; width: 100%;">
          <img src="https://ik.imagekit.io/sheryians/Logo/shery-katana_Kb-_q1uXZ.gif" alt="">
        </div>
        <div id="heroArea-mcq">
          <div class="heading">
            <h1>MCQ title</h1>
            <button onclick="saveMcqLecture(this)">Create Lecture</button>
          </div>
          <div class="input">
            <p>Title</p>
            <input placeholder="Enter the title of the lecture" class="title" type="text">
          </div>
          <div class="input">
            <p>Question</p>
            <textarea placeholder="Enter the question" class="question" cols="30" rows="4"></textarea>
          </div>
          <div class="input">
            <p>Question Image</p>
            <input name="questionImage" type="file" class="questionImage">
          </div>
          <div class="multipleChoice">
            <p>Choices</p>
            <div class="choice">
              <textarea placeholder="Enter the choice text" name="" id="" cols="30" rows="2"
                oninput="resizeTextarea(this)"></textarea>
              <div class="answer">
                <input type="radio" name="option" id="checkbox1">
                <label for="checkbox1">Mark this as answer</label>
              </div>
            </div>
            <div class="choice">
              <textarea placeholder="Enter the choice text" name="" id="" cols="30" rows="2"
                oninput="resizeTextarea(this)"></textarea>
              <div class="answer">
                <input type="radio" name="option" id="checkbox2">
                <label for="checkbox2">Mark this as answer</label>
              </div>
            </div>
            <div class="choice">
              <textarea placeholder="Enter the choice text" name="" id="" cols="30" rows="2"
                oninput="resizeTextarea(this)"></textarea>
              <div class="answer">
                <input type="radio" name="option" id="checkbox3">
                <label for="checkbox3">Mark this as answer</label>
              </div>
            </div>
            <div class="choice">
              <textarea placeholder="Enter the choice text" name="" id="" cols="30" rows="2"
                oninput="resizeTextarea(this)"></textarea>
              <div class="answer">
                <input type="radio" name="option" id="checkbox4">
                <label for="checkbox4">Mark this as answer</label>
              </div>
            </div>
          </div>
          <div class="input">
            <p>Code Snippet <span>(Optional)</span></p>
            <div class="codeInputDiv active" id="snippetCode"></div>
          </div>
          <div class="input">
            <p>Code snippet language <span>(if snippet is given)</span></p>
            <input placeholder="Java, Python, etc..." type="text" class="snippetLanguage">
          </div>
          <div class="input">
            <p>Explaination</p>
            <textarea class="explaination" placeholder="Explaination goes here" oninput="resizeTextarea(this)"
              rows="5"></textarea>
          </div>
          <div class="input">
            <p>Explaination Video</p>
            <input name="explainationVideo" type="file" class="explainationVideo">
          </div>
          <div class="input">
            <p>Marks</p>
            <input type="text" class="marks" class="marks">
          </div>
        </div>
        <div id="heroArea-code">
          <div class=" heading">
          <h1>Coding Lecture Title</h1>
          <button onclick="saveCodeLecture(this)">Create Lecture</button>
          </div>
          <div class="input">
            <p>Title</p>
            <input placeholder="Enter the title of the lecture" class="title" type="text">
          </div>
          <div class="codeInput">
            <div class="codeEditorHeader">
      <div class="headLinks">
        <p onclick="changeTab(this, 'mainCode')" class="active">Main Code</p>
        <p onclick="changeTab(this, 'templateCode')">Template Code</p>
        <p onclick="changeTab(this, 'solutionCode')">Solution Code</p>
      </div>
      <div>
        <select onchange="changeLanguage(this)">
          <option value="python">Python</option>
          <option value="java">Java</option>
          <option value="c_cpp">C</option>
          <option value="javascript">JavaScript</option>
        </select>
      </div>
            </div>

            <!-- Code Editor Areas -->
            <div id="mainCode" class="codeInputDiv active"></div>
            <div id="templateCode" class="codeInputDiv"></div>
            <div id="solutionCode" class="codeInputDiv"></div>
          </div>
          <div class="input problemId">
            <p>Problem ID</p>
            <input oninput="renderCodingQuestions(this)" type="text" class="problemIdInput">
            <div class="problemDiv">

            </div>
          </div>
          <div class="footer">
            <div class="input">
              <p>Default</p>
              <select name="" onchange="changeDefaultValue(this)" class="defaultValue" id="">
                <option value="62">Java</option>
                <option value="71">Python</option>
                <option value="49">C</option>
                <option value="63">JavaScript</option>
              </select>
            </div>
            <div class="input">
              <p>Difficulty</p>
              <select name="" id="difficulty">
                <option value="Easy">Easy</option>
                <option value="Medium">Medium</option>
                <option value="Hard">Hard</option>
              </select>
            </div>
            <div class="input">
              <p>Marks</p>
              <input placeholder="Enter the Marks" class="marks" type="text">
            </div>
            <div class="input">
          <p>Languages to Enable</p>
          <div class="language-toggle-group">
            <label class="language-toggle" for="lang-java">
              <input type="checkbox" id="lang-java" value="62" />
              <span>Java</span>
            </label>

            <label class="language-toggle" for="lang-python">
              <input type="checkbox" id="lang-python" value="71" />
              <span>Python</span>
            </label>

            <label class="language-toggle" for="lang-cpp">
              <input type="checkbox" id="lang-cpp" value="49" />
              <span>C</span>
            </label>

            <label class="language-toggle" for="lang-js">
              <input type="checkbox" id="lang-js" value="63" />
              <span>JavaScript</span>
            </label>
          </div>
        </div>
          </div>
        </div>
      <div id="heroArea-video" class="active">
        <div class="video"></div>
        <div class="metaData">
          <div class="title">
            <h2>Some Video Playing and It's title goes here.</h2>
          </div>
          <div class="courseDetail">
            <div class="text">
              <h4>
                <%=courseName%>
              </h4>
              <small>
                <%=courseEnrolledStudent%> Students Learning
              </small>
            </div>
            <div class="like">
              <div class="likes">
                <i class="ri-thumb-up-line"></i>
                <i class="ri-thumb-up-fill active "></i>
                <small class="count">
                  0
                </small>
              </div>
              <div class="vr"></div>
              <div class="dislikes">
                <i class="ri-thumb-down-line"></i>
                <i class="ri-thumb-down-fill active"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="description">
          <small>Description</small>
          <p>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Velit, laborum cumque explicabo amet
            voluptatem voluptate itaque quia tenetur nulla, inventore aut quasi consequatur nobis. Fugit
            accusantium iusto molestiae a fuga?
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Velit, laborum cumque explicabo amet
            voluptatem voluptate itaque quia tenetur nulla, inventore aut quasi consequatur nobis. Fugit
            accusantium iusto molestiae a fuga?
          </p>
          <div class="showMore">
            <p>See more</p>
          </div>
        </div>
        <div class="comments">
          <div class="addComment">
            <div class="top">
              <p> 105 comments </p>
              <div class="arrow open">
                <i class="ri-arrow-down-s-line"></i>
                <i class="ri-arrow-up-s-line"></i>
              </div>
            </div>
            <div class="bottom">
              <div class="user">
                <img src="<%=profilePicture%>" alt="">
              </div>
              <textarea id="newComment" name="newComment" rows="1" placeholder="Add Comment"></textarea>
            </div>
            <div class="actions">
              <div class="action cancle">cancel</div>
              <div onclick="addComment('<%=user.name%>','<%=profilePicture%>','<%=user._id%>')"
                class="action addcomment ">comment</div>
            </div>
          </div>
          <div class="allcomments">
          </div>
        </div>
      </div>
      </div>
      <div class="chapters">
        <div class="notes">
          <input type="text" maxlength="45" class="live_class_notes" placeholder="Class Notes" value="<%=courseNotes%>">
        </div>
        <div class="title">
          <button onclick="addModuleWrapper()">Add Module</button>
          <% if(courseStatus=='live' ){%>
            <button onclick="seeLiveClassCredentials()">Start Live Class</button>
            <%}%>
              <button onclick="saveConfig()">Save Order</button>
        </div>
        <div class="modules">

          <% for (const moduleWrapper of classroomContent) { %>
            <div class="moduleWrapper" data-id="<%=moduleWrapper._id%>">
              <div class="title">
                <div class="controlls">
                  <h1 class="name">
                    <input id="moduleTitle" spellcheck="false" type="text" value="<%= moduleWrapper.title %>">
                  </h1>
                  <button class="dropdownButton dropdownButtons">
                    <i title="Add Module" onclick="addModule(this)" class="ri-add-box-line"></i>
                    <i title="Add ComingSoon Module" onclick="addModuleWrapperUpdate(this)"
                      class="ri-calendar-event-fill"></i>
                    <i title="Delete" onclick="deleteModuleWrapper(this)" class="ri-delete-bin-7-fill"></i>
                  </button>
                  <i class="moduleWrapperArrow ri-arrow-right-s-line" onclick="closeModuleWrapper(this)"></i>
                </div>
                <%if(moduleWrapper.comingSoonDate){%>
                  <div class="seperator"></div>
                  <div class="comingSoonModuleDate">
                    <p>Select Date:</p>
                    <input value="<%=new Date(moduleWrapper.comingSoonDate).toISOString().split('T')[0];%>" type="date"
                      name="myDate" placeholder="Enter Date">
                  </div>
                  <%}%>
              </div>
              <% for (const module of moduleWrapper.modules) { %>
                <div class="module" data-wrapper-id="<%=moduleWrapper._id%>" data-id="<%=module._id%>">
                  <div class="title">
                    <div class="controlls">
                      <h1 class="name">
                        <input id="moduleTitle" spellcheck="false" type="text" value="<%= module.title %>">
                      </h1>
                      <button class="dropdownButton dropdownButtons">
                        <i title="Add MCQ Questions" onclick="addMCQ(this,'<%=moduleWrapper._id%>','<%=module._id%>')"
                          class="ri-checkbox-multiple-line"></i>
                        <i title="Add Coding Questions"
                          onclick="addCode(this,'<%=moduleWrapper._id%>','<%=module._id%>')"
                          class="ri-code-box-line"></i>
                        <i title="Add PDF" onclick="addPDF(this)" class="ri-file-pdf-2-line"></i>
                        <i title="Add Lecture" onclick="addLecture(this)" class="ri-movie-line"></i>
                        <i title="Add ComingSoon Lecture" onclick="addUpdate(this)" class="ri-time-fill"></i>
                        <i title="Add ComingSoon Module" onclick="addModuleUpdate(this)"
                          class="ri-calendar-event-fill"></i>
                        <i title="Delete" onclick="deleteModule(this)" class="ri-delete-bin-7-fill"></i>
                      </button>
                      <i class="moduleArrow ri-arrow-right-s-line" onclick="closeModule(this)"></i>
                    </div>
                    <%if(module.comingSoonDate){%>
                      <div class="seperator"></div>
                      <div class="comingSoonDate">
                        <p>Select Date:</p>
                        <input value="<%=new Date(module.comingSoonDate).toISOString().split('T')[0];%>" type="date"
                          name="myDate" placeholder="Enter Date">
                      </div>
                      <%}%>
                  </div>

                  <div close class="lectures">
                    <% for (const lecture of module.lectures) { %>
                      <%if((Date.now() < lecture.comingSoonDetails.date) || lecture.guid=="null" ){%>
                        <div data-lectureType="<%=lecture.type%>" class="lecture" data-type="comingSoon"
                          data-wrapper-id="<%=moduleWrapper._id%>" data-lecture-id="<%=lecture._id%>"
                          data-module-id="<%= module._id %>">
                          <div class="rgiht righ ">
                            <div class="lectureTitle comingSoon">
                              <div>
                                <small>Topic Name</small>
                                <input id="lectureGuid" type="text" placeholder="Guid *if"
                                  value="<%=lecture.guid == 'null' ? '' : lecture.guid%>">
                              </div>
                              <div>
                                <small>Topic Name</small>
                                <input id="lectureTitle" type="text" placeholder="Title"
                                  value="<%=lecture.comingSoonDetails.title%>">
                              </div>
                              <div>
                                <small>Date</small>

                                <input id="lectureTime" type="date" data-time=<%=lecture.comingSoonDetails.date%>
                                >
                              </div>
                            </div>
                            <button onclick="deleteLecture(this)"><i class="ri-delete-bin-7-fill"></i></button>
                          </div>
                        </div>
                        <%}else{%>
                          <%if(lecture.type=="video" ){%>
                            <div data-lectureType="<%=lecture.type%>" class="lecture" data-lecture-id="<%=lecture._id%>"
                              data-module-id="<%= module._id %>" data-wrapper-id="<%= moduleWrapper._id %>">
                              <div class="rgiht righ">
                                <div class="inputFields">
                                  <p class="lectureTitle">
                                    <small>GUID</small>
                                    <input type="text" id="lectureGuid" placeholder="Guid" value="<%= lecture.guid %>">
                                  </p>
                                  <p class="lectureTitle">
                                    <small>Marks</small>
                                    <input type="text" id="marks" placeholder="Marks" value="<%= lecture.points %>">
                                  </p>
                                </div>

                              </div>
                              <div id="newCheck">
                                <div>
                                  <input <%=lecture.newLecture ? "checked" : "" %>
                                  type="checkbox"
                                  id="newLecture"
                                  > <label for="newLecture">Make this lecture new</label>
                                </div>
                                <div class="buttons">
                                  <button class="lectureBtn" onclick="addLink(this)"><i
                                      class="ri-add-box-line"></i></button>
                                  <button onclick="deleteLecture(this)"><i class="ri-delete-bin-7-fill"></i></button>
                                </div>
                              </div>
                              <div id="allLinks">
                                <% for (const link of lecture.links) { %>
                                  <div id="links" data-id="<%=link._id%>">
                                    <div>
                                      <small>Link name</small>
                                      <div>
                                        <input placeholder="Link Name" value="<%=link.naam%>" class="linkName"
                                          type="text">
                                        <button class="lectureBtn" onclick="deleteLink(this)">
                                          <i class="ri-delete-bin-7-fill"></i></button>
                                      </div>
                                    </div>
                                    <small>Link</small>
                                    <input placeholder="Link" value="<%=link.link%>" class="linkLink" type="text">
                                  </div>
                                  <%}%>
                              </div>
                            </div>
                            <%}else if(lecture.type=="pdf" ){%>
                              <div data-lectureType="<%=lecture.type%>" class="lecture"
                                data-lecture-id="<%=lecture._id%>" data-module-id="<%= module._id %>"
                                data-wrapper-id="<%= moduleWrapper._id %>">
                                <div class="rgiht righ">
                                  <div class="inputFields">
                                    <p class="lectureTitle">
                                      <small>Title</small>
                                      <input id="lectureTitle" type="text" placeholder="Enter Title"
                                        value="<%=lecture.title%>">
                                    </p>
                                    <p class="lectureTitle">
                                      <small>PDF Link</small>
                                      <input id="PDFLink" type="text" placeholder="Link" value="<%=lecture.pdf%>">
                                    </p>
                                    <p class="lectureTitle">
                                      <small>Marks</small>
                                      <input id="marks" type="text" placeholder="Marks" value="<%=lecture.points%>">
                                    </p>
                                  </div>
                                </div>
                                <div id="newCheck">
                                  <div>

                                    <input <%=lecture.newLecture ? "checked" : "" %>
                                    type="checkbox"
                                    id="newLecture"
                                    > <label for="newLecture">Make this lecture new</label>
                                  </div>
                                  <button onclick="deleteLecture(this)"><i class="ri-delete-bin-7-fill"></i></button>
                                </div>
                              </div>
                              <%}else if(lecture.type=="mcq" ){%>
                                <div data-lectureType="<%=lecture.type%>" class="lecture"
                                  data-lecture-id="<%=lecture._id%>" data-module-id="<%= module._id %>"
                                  data-wrapper-id="<%= moduleWrapper._id %>">
                                  <div class="rgiht righ">
                                    <h4>
                                      <%=lecture.title%>
                                    </h4>
                                  </div>
                                  <div id="newCheck">
                                    <div>
                                      <input <%=lecture.newLecture ? "checked" : "" %>
                                      type="checkbox"
                                      id="newLecture"
                                      > <label for="newLecture">Make this lecture new</label>
                                    </div>
                                    <button onclick="deleteLecture(this)"><i class="ri-delete-bin-7-fill"></i></button>
                                  </div>
                                </div>
                                <%}else if(lecture.type=="code" ){%>
                                  <div data-lectureType="<%=lecture.type%>" class="lecture"
                                    data-lecture-id="<%=lecture._id%>" data-module-id="<%= module._id %>"
                                    data-wrapper-id="<%= moduleWrapper._id %>">
                                    <div class="rgiht righ">
                                      <h4>
                                        <%=lecture.problem_name%>
                                      </h4>
                                    </div>
                                    <div id="newCheck">
                                      <div>
                                        <input <%=lecture.newLecture ? "checked" : "" %>
                                        type="checkbox"
                                        id="newLecture"
                                        > <label for="newLecture">Make this lecture new</label>
                                      </div>
                                      <button onclick="deleteLecture(this)"><i
                                          class="ri-delete-bin-7-fill"></i></button>
                                    </div>
                                  </div>
                                  <%}%>

                                    <%}%>
                                      <%}%>
                  </div>
                </div>
                <%}%>
            </div>
            <%}%>
        </div>
      </div>
    </section>
    <%- include('Partials/newFooterpart1.ejs') %>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.33.1/ace.js"
        integrity="sha512-WN1CEDE9Js0mEqvtRrNS7GHS+arRJxWVO03zttkQQXEQjwGVcHQ2kMja415m0bNeB3AtYsyjUWJ1BS4wGLta/Q=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
      <script src="https://cdn.jsdelivr.net/npm/simple-notify@0.5.5/dist/simple-notify.min.js"></script>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.4.0/axios.min.js"
        integrity="sha512-uMtXmF28A2Ab/JJO2t/vYhlaa/3ahUOgj1Zf27M5rOo8/+fcTUVH0/E0ll68njmjrLqOBjXM3V9NiPFL5ywWPQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
      <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
      <script defer src="/js/gotoclassroomedit.js"></script>
      <script src="/js/ace-editor-render.js"></script>

      <script src="/ace-builds/src-noconflict/ext-emmet.js"></script>
      <script src="/ace-builds/src/ace.js" type="text/javascript" charset="utf-8"></script>
      <script src="/ace-builds/src-noconflict/ext-language_tools.js"></script>
      <script s></script>
      <script>

        let mainCode = initializeEditor("mainCode");
        let templateCode = initializeEditor("templateCode");
        let solutionCode = initializeEditor("solutionCode");
        mainCode.session.on('change', saveCurrentEditorState);
        templateCode.session.on('change', saveCurrentEditorState);
        solutionCode.session.on('change', saveCurrentEditorState);

        let editors = {
          mainCode,
          templateCode,
          solutionCode
        };

        let currentTab = "mainCode";
        let currentLang = "python";

        // Default structure supporting all languages
        const supportedLanguages = {
          java: { compilerId: "62", mode: "java" },
          python: { compilerId: "71", mode: "python" },
          c_cpp: { compilerId: "49", mode: "c_cpp" },
          javascript: { compilerId: "63", mode: "javascript" }
        };

        let codeData = {
          java: { mainCode: "", templateCode: "", solutionCode: "", compilerId: "62", default: false, enabled: false },
          python: { mainCode: "", templateCode: "", solutionCode: "", compilerId: "71", default: false, enabled: false },
          c_cpp: { mainCode: "", templateCode: "", solutionCode: "", compilerId: "49", default: false, enabled: false },
          javascript: { mainCode: "", templateCode: "", solutionCode: "", compilerId: "63", default: false, enabled: false }
        };

        const problemsId = JSON.parse(`<%-problemsId%>`)
        var snippetCode
        
        function changeDefaultValue(event) {
          const selectedCompilerId = event.value;

          // Loop through each language and update default status
          console.log(codeData)
          for (const lang in codeData) {
            if (codeData[lang].compilerId === selectedCompilerId) {
              codeData[lang].default = true;
            } else {
              codeData[lang].default = false;
            }
          }
          console.log(codeData)
          console.log("Updated codeData default language:", selectedCompilerId);
        };


        async function loadCodingLecture(moduleWrapperId, moduleId, lectureId) {
          try {
            // Clear the in-memory codeData before loading new data
            codeData = {
              java: { mainCode: "", templateCode: "", solutionCode: "", compilerId: "62", default: false, enabled: true },
              python: { mainCode: "", templateCode: "", solutionCode: "", compilerId: "71", default: false, enabled: true },
              c_cpp: { mainCode: "", templateCode: "", solutionCode: "", compilerId: "49", default: false, enabled: true },
              javascript: { mainCode: "", templateCode: "", solutionCode: "", compilerId: "63", default: false, enabled: true }
            };
            const response = await fetch(`/classroom/getVideoDetails/${moduleWrapperId}/${moduleId}/${lectureId}`);
            const data = await response.json();
            document.querySelector('#heroArea-code').setAttribute("data-module-id", moduleId);
            document.querySelector('#heroArea-code').setAttribute("data-wrapper-id", moduleWrapperId);
            document.querySelector('#heroArea-code button').textContent = "Save Lecture";
            document.querySelector('#heroArea-code .title').value = data.problem_name || "";
            document.querySelector('#heroArea-code .problemIdInput').value = data.problemId || "";
            document.querySelector('#heroArea-code #difficulty').value = data.difficulty || "";
            document.querySelector('#heroArea-code .marks').value = data.points || "";
            // Load schema - support both new and old
            if (data.code) {
              codeData = data.code;

            } else {
              const map = { "62": "java", "71": "python", "49": "c_cpp", "63": "javascript" };
              const lang = map[data.compilerId];
              const langData = {
                mainCode: data.main_code || "",
                templateCode: data.code_template || "",
                solutionCode: data.solution_code || "",
                compilerId: data.compilerId || "",
                default: true,
                enabled: true
              };
              codeData[lang] = langData;
            }
            langToggleInputs.forEach(input => {
              const match = Object.values(codeData).find(lang => lang.compilerId === input.value);
              if (match) input.checked = match.enabled;
            });
            const defaultSelect = document.querySelector('.defaultValue');
            const defaultLang = Object.values(codeData).find(lang => lang.default);
            if (defaultLang) {
              defaultSelect.value = defaultLang.compilerId;
            }
            for (const lang in codeData) {
                if (codeData[lang].default) {
                  currentLang = lang;
                  break;
                }
              }
              // If no default is set, use the first language or fallback to python
              if (!currentLang || !codeData[currentLang]) {
                currentLang = Object.keys(codeData)[0] || "python";
              }
            renderCurrentEditor();

            loader.style.display = 'none';
            codingHeroAreaWrap.classList.add("active");
            codingHeroAreaWrap.setAttribute("data-lectureId", lectureId);
          } catch (err) {
            console.error('Error fetching video details:', err);
          }
        }
        function changeLanguage(select) {
          console.log(codeData)
          currentLang = select.value;
          renderCurrentEditor();
        }
        const langToggleInputs = document.querySelectorAll('.language-toggle input[type="checkbox"]');

        langToggleInputs.forEach(input => {
          input.addEventListener('change', () => {
            const selectedId = input.value;

            for (const lang in codeData) {
              if (codeData[lang].compilerId === selectedId) {
                codeData[lang].enabled = input.checked;
              }
            }

            console.log("Updated codeData enabled flags:", codeData);
          });
        });
        function changeTab(tabEl, tabId) {
          document.querySelectorAll(".codeInputDiv").forEach(div => div.classList.remove("active"));
          document.querySelectorAll(".codeEditorHeader .headLinks p").forEach(p => p.classList.remove("active"));
          tabEl.classList.add("active");
          document.getElementById(tabId).classList.add("active");
          // // Save current tab code 
          currentTab = tabId;

          console.log(codeData)
          editors[currentTab].setValue(codeData[currentLang][currentTab] || "", 1);
        }

        function saveCurrentEditorState() {
          if (!codeData[currentLang]) return;
          console.log(codeData[currentLang][currentTab], editors[currentTab].getValue())
          codeData[currentLang][currentTab] = editors[currentTab].getValue() || codeData[currentLang][currentTab];
          // codeData[currentLang].templateCode = editors.templateCode.getValue() || codeData[currentLang].templateCode;
          // codeData[currentLang].solutionCode = editors.solutionCode.getValue() || codeData[currentLang].solutionCode;
        }

        function renderCurrentEditor() {
          const mode = supportedLanguages[currentLang]?.mode || "text";

          editors.mainCode.session.setMode(`ace/mode/${mode}`);
          editors.templateCode.session.setMode(`ace/mode/${mode}`);
          editors.solutionCode.session.setMode(`ace/mode/${mode}`);

          editors[currentTab].setValue(codeData[currentLang][currentTab] || "", 1);
          // document.querySelector(".compilerId").value = codeData[currentLang].compilerId || "";
          document.querySelector(".codeEditorHeader select").value = currentLang;
        }

        // Save Coding Lecture Function
        // async function saveCodeLecture(button) {
        //   try {

        //     const moduleId = document.querySelector('#heroArea-code').getAttribute('data-module-id');
        //     const moduleWrapperId = document.querySelector('#heroArea-code').getAttribute('data-wrapper-id');
        //     const lectureId = document.querySelector('#heroArea-code').getAttribute('data-lectureid');

        //     const payload = {
        //       code: codeData,
        //       problemId: document.querySelector('#heroArea-code .problemIdInput').value,
        //       problem_name: document.querySelector('#heroArea-code .title').value,
        //       difficulty: document.querySelector('#heroArea-code #difficulty').value,
        //       points: document.querySelector('#heroArea-code .marks').value,
        //     };

        //     button.textContent = "Saving...";
        //     button.disabled = true;

        //     const res = await axios.post(`/classroom/saveCodeLecture/${moduleWrapperId}/${moduleId}/${lectureId}`, payload);

        //     if (res.status === 200) {
        //       new Notify({
        //         status: 'success',
        //         title: 'Lecture Saved',
        //         text: 'Your coding lecture was saved successfully.',
        //         effect: 'slide',
        //         speed: 300,
        //         customClass: '',
        //         customIcon: null,
        //         showIcon: true,
        //         showCloseButton: true,
        //         autoclose: true,
        //         autotimeout: 2000,
        //         gap: 20,
        //         distance: 20,
        //         type: 1,
        //         position: 'right top'
        //       });
        //     } else {
        //       throw new Error("Unexpected response");
        //     }
        //   } catch (err) {
        //     console.error("Save failed", err);
        //     alert("Failed to save lecture. Please try again.");
        //   } finally {
        //     button.textContent = "Save Lecture";
        //     button.disabled = false;
        //   }
        // }
        
        function convertToDateTimeLocal(savedMilliseconds) {
          // Create a new Date object using the saved milliseconds
          const restoredDate = new Date(savedMilliseconds);

          // Extract the date components
          const restoredYear = restoredDate.getFullYear();
          const restoredMonth = String(restoredDate.getMonth() + 1).padStart(2, '0');
          const restoredDay = String(restoredDate.getDate()).padStart(2, '0');

          // Format the date components into the string format expected by date input
          const restoredDateValue = `${restoredYear}-${restoredMonth}-${restoredDay}`;

          // Set the value to the date input
          return restoredDateValue;
        }
        document.querySelectorAll(".linkLink").forEach((e) => {
          e.value = convertHtmlEntityToChar(e.value)
        })
        function decodeHTML(html) {
          const doc = new DOMParser().parseFromString(html, 'text/html');
          return doc.documentElement.textContent;
        }
        async function sendUpdate(event) {
          if (confirm("Are you sure you want to send updates")) {
            try {
              const data = await axios.post("/classroom/sendUpdateEmail/<%=courseId%>")
              console.log(data)
            } catch (err) {
              event.style.backgroundColor = "red"
              event.textContent = "Error Updating"
            }
          }
        }

        async function deleteComment(commentId, userId) {
          try {
            const response = await fetch(`/classroom/deleteComment/${commentId}/${userId}`, {
              method: 'DELETE'
            });
            const data = await response.json();
            if (response.ok) {
              const commentCount = document.querySelector('.page1 .heroArea .comments .addComment .top p');
              commentCount.textContent = Number(commentCount.textContent.split(" ")[0]) - 1 + " comments";
            } else {
              console.error('Failed to delete comment:', data.error);
            }
          } catch (err) {
            console.error('Error deleting comment:', err);
          }
        }
        function convertHtmlEntityToChar(htmlEntity) {
          const parser = new DOMParser();
          const decodedString = parser.parseFromString(`<!doctype html><body>${htmlEntity}`, 'text/html').body.textContent;
          return decodedString;
        }
        const loader = document.querySelector('.loader');
        const videoHeroAreaWrap = document.querySelector('#heroArea-video');
        const MCQHeroAreaWrap = document.querySelector('#heroArea-mcq');
        const codingHeroAreaWrap = document.querySelector('#heroArea-code');
        let lectureId, moduleId, moduleWrapperId, Likebutton, Dislikebutton;
        let courseId = '<%=courseId%>'
        let likeButtonEnabled = true;
        let dislikeButtonEnabled = true;
        let commentButtonEnabled = true;
        document.addEventListener('DOMContentLoaded', async () => {
          const lectures = document.querySelectorAll('.lecture');

          // Get the last watched lecture ID from the server
          // const response = await fetch('/classroom/getLastWatchedLecture/<%=courseId%>');
          const lastWatchedLectureData = <% - user.lastWatchedLecture ? JSON.stringify(user.lastWatchedLecture) : "null" %>
            // const findlastWatchedLecture = lastWatchedLectureData.find(item => item.courseId.toString() === courseId.toString());

            lectures.forEach((lecture) => {
              lecture.addEventListener('click', async () => {
                lectureId = lecture.getAttribute('data-lecture-id');
                moduleId = lecture.getAttribute('data-module-id');
                moduleWrapperId = lecture.getAttribute('data-wrapper-id');
                lectureType = lecture.getAttribute('data-lectureType');
                // Update the lastWatchedLecture in the database
                const response = fetch('/classroom/updateLastWatchedLecture', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    courseId,
                    moduleId,
                    lectureId
                  })
                });

                try {
                  // Show the loader while waiting for the server response
                  loader.style.display = 'flex';
                  console.log(videoHeroAreaWrap)
                  videoHeroAreaWrap.classList.remove("active")
                  MCQHeroAreaWrap.classList.remove("active")
                  codingHeroAreaWrap.classList.remove("active")
                  if (lectureType == "video") {

                    // Make an AJAX request to the server to get the video and metadata
                    const response = await fetch(`/classroom/getVideoDetails/${moduleWrapperId}/${moduleId}/${lectureId}`);
                    const data = await response.json();
                    // Update the heroArea section with the video and metadata
                    const video = document.querySelector('.video');
                    console.log(video)
                    video.innerHTML = `<iframe src="${data.iframe}" 
                                              loading="lazy" style="border: none; border-radius:7px; position: relative; top: 0; height: 100%; width: 100%;" 
                                              allow="accelerometer; gyroscope; encrypted-media; picture-in-picture;" allowfullscreen="true">
                                          </iframe>`;

                    const title = document.querySelector('.metaData .title h2');
                    console.log(title)
                    title.textContent = data.title;

                    const description = document.querySelector('.description p');
                    description.textContent = data.description;

                    const likes = document.querySelector('.page1 .heroArea .metaData .courseDetail .like .likes .count');
                    likes.textContent = data.likes;

                    const comments = document.querySelector('.page1 .heroArea .comments .addComment .top p');
                    comments.textContent = data.comments.length + " comments";

                    const commentsContainer = document.querySelector('.page1 .heroArea .comments .allcomments');
                    commentsContainer.innerHTML = ""; // Clear the previous comments

                    if (data.comments.length === 0) {
                      // Show a message if there are no comments
                      commentsContainer.innerHTML = "<p class='noComments'>No comments yet.</p>";
                    } else {
                      // Iterate through the comments and display them
                      data.comments.forEach((comment) => {
                        const commentElement = createCommentElement(comment.username, comment.content, comment.createdAt, comment.image, comment.isCurrentUserComment, comment._id, comment.user);
                        commentsContainer.appendChild(commentElement);
                      });
                    }

                    Likebutton = document.querySelector('.page1 .heroArea .metaData .courseDetail .like .likes');
                    Dislikebutton = document.querySelector('.page1 .heroArea .metaData .courseDetail .like .dislikes');
                    const likeParent = document.querySelector('.like')
                    if (data.isLiked) {
                      likeParent.classList.add('liked');
                      likeParent.classList.remove("disLiked")
                    } else if (data.isDisliked) {
                      likeParent.classList.remove('liked');
                      likeParent.classList.add("disLiked")
                    } else {
                      likeParent.classList.remove('liked');
                      likeParent.classList.remove("disLiked")
                    }

                    // Remove the previous event listeners before adding new ones
                    Likebutton.removeEventListener('click', likeButtonClickHandler);
                    Dislikebutton.removeEventListener('click', dislikeButtonClickHandler);

                    // Add new event listeners
                    Likebutton.addEventListener('click', likeButtonClickHandler);
                    Dislikebutton.addEventListener('click', dislikeButtonClickHandler);
                    // Hide the loader after the server response is received and data is processed
                    loader.style.display = 'none';
                    videoHeroAreaWrap.classList.add("active")
                  } else if (lectureType == "mcq") {
                    const response = await fetch(`/classroom/getVideoDetails/${moduleWrapperId}/${moduleId}/${lectureId}`);
                    const data = await response.json();

                    snippetCode = initializeEditor("snippetCode")
                    snippetCode.setValue(data.codeSnippet)

                    document.querySelector('#heroArea-mcq').setAttribute("data-module-id", moduleId)
                    document.querySelector('#heroArea-mcq').setAttribute("data-wrapper-id", moduleWrapperId)
                    document.querySelector('#heroArea-mcq button').textContent = "Save Lecture"

                    const title = document.querySelector('#heroArea-mcq .title');
                    title.value = data.title

                    const snippetCodeLanguage = document.querySelector('#heroArea-mcq .snippetLanguage');
                    snippetCodeLanguage.value = data.codeSnippetLanguage

                    const question = document.querySelector('#heroArea-mcq .question');
                    question.value = data.question

                    if (data.questionImage) {
                      const questionImage = document.querySelector('#heroArea-mcq .questionImage').closest(".input");
                      questionImage.insertAdjacentHTML("beforeend", `<a href="${data.questionImage}" target="_blank">View Image</a>`)
                    }

                    const explanation = document.querySelector('#heroArea-mcq .explaination');
                    explanation.value = data.explanation

                    if (data.explanationVideo) {
                      const explanationVideo = document.querySelector('#heroArea-mcq .explainationVideo').closest(".input");
                      explanationVideo.insertAdjacentHTML("beforeend", `<a href="${data.explanationVideo}" target="_blank">View Explaination</a>`)
                    }

                    const points = document.querySelector('#heroArea-mcq .marks');
                    points.value = data.points

                    document.querySelectorAll("#heroArea-mcq .choice").forEach((choice, idx) => {
                      choice.querySelector("textarea").value = data.options[idx].option
                      console.log((idx + 1), data.correctOption)
                      if ((idx + 1) == data.correctOption) {
                        choice.querySelector("input").checked = true
                      }
                    })

                    loader.style.display = 'none';
                    MCQHeroAreaWrap.classList.add("active")
                    MCQHeroAreaWrap.setAttribute("data-lectureId", lecture.getAttribute('data-lecture-id'))

                  } else if (lectureType == "code") {
                    loadCodingLecture(moduleWrapperId, moduleId, lectureId);
                    // mainCode = initializeEditor("mainCode")
                    // solutionCode = initializeEditor("solutionCode")
                    // templateCode = initializeEditor("templateCode")

                    // const response = await fetch(`/classroom/getVideoDetails/${moduleWrapperId}/${moduleId}/${lectureId}`);
                    // const data = await response.json();
                    
                    // console.log(data.code)
                    // document.querySelector('#heroArea-code').setAttribute("data-module-id", moduleId)
                    // document.querySelector('#heroArea-code').setAttribute("data-wrapper-id", moduleWrapperId)
                    // document.querySelector('#heroArea-code button').textContent = "Save Lecture"

                    // const title = document.querySelector('#heroArea-code .title');
                    // title.value = data.problem_name

                    // solutionCode.setValue(data.solutionCode);
                    // mainCode.setValue(data.mainCode);
                    // templateCode.setValue(data.code_template)

                    // const compilerId = document.querySelector('#heroArea-code .compilerId');
                    // const codeEditorHeader = document.querySelector('#heroArea-code .codeEditorHeader select');
                    // compilerId.value = data.compilerId

                    // if (data.compilerId == 62) {
                    //   mainCode.session.setMode(`ace/mode/java`);
                    //   solutionCode.session.setMode(`ace/mode/java`);
                    //   templateCode.session.setMode(`ace/mode/java`);
                    //   codeEditorHeader.value = "java"

                    // } else if (data.compilerId == 71) {
                    //   mainCode.session.setMode(`ace/mode/python`);
                    //   solutionCode.session.setMode(`ace/mode/python`);
                    //   templateCode.session.setMode(`ace/mode/python`);
                    //   codeEditorHeader.value = "python"

                    // } else if (data.compilerId == 49) {
                    //   mainCode.session.setMode(`ace/mode/c_cpp`);
                    //   solutionCode.session.setMode(`ace/mode/c_cpp`);
                    //   templateCode.session.setMode(`ace/mode/c_cpp`);
                    //   codeEditorHeader.value = "c_cpp"

                    // } else if (data.compilerId == 63) {
                    //   mainCode.session.setMode(`ace/mode/javascript`);
                    //   solutionCode.session.setMode(`ace/mode/javascript`);
                    //   templateCode.session.setMode(`ace/mode/javascript`);
                    //   codeEditorHeader.value = "javascript"
                    // }

                    // const problemId = document.querySelector('#heroArea-code .problemIdInput');
                    // problemId.value = data.problemId

                    // const difficulty = document.querySelector('#heroArea-code #difficulty');
                    // difficulty.value = data.difficulty

                    // const points = document.querySelector('#heroArea-code .marks');
                    // points.value = data.points

                    // loader.style.display = 'none';
                    // codingHeroAreaWrap.classList.add("active")
                    // codingHeroAreaWrap.setAttribute("data-lectureId", lecture.getAttribute('data-lecture-id'))
                  }
                } catch (err) {
                  console.error('Error fetching video details:', err);
                }
              });
            });


          async function likeButtonClickHandler() {
            // Check if the function is currently enabled
            if (!likeButtonEnabled) {
              return; // If disabled, do nothing and return
            }
            // Disable the function to prevent multiple clicks while waiting for the response
            likeButtonEnabled = false;
            try {
              const response = await fetch(`/classroom/lectures/${lectureId}/${moduleId}/like`, {
                method: 'POST'
              });
              const data_res = await response.json();
              if (response.ok) {
                // Update the like count in the front-end
                // const countElement = Likebutton.querySelector('.count');
                // countElement.innerText = data_res.likes;
              }
            } catch (err) {
              console.error('Error liking video:', err);
            } finally {
              // Re-enable the function after the response is received
              likeButtonEnabled = true;
            }
          }

          async function dislikeButtonClickHandler() {
            // Check if the function is currently enabled
            if (!dislikeButtonEnabled) {
              return; // If disabled, do nothing and return
            }
            // Disable the function to prevent multiple clicks while waiting for the response
            dislikeButtonEnabled = false;
            try {
              const response = await fetch(`/classroom/lectures/${lectureId}/${moduleId}/dislike`, {
                method: 'POST'
              });
              const data_res = await response.json();
              if (response.ok) {
                // Update the dislike count in the front-end
                // const countElement = Likebutton.querySelector('.count');
                // countElement.innerText = data_res.likes;
              }
            } catch (err) {
              console.error('Error disliking video:', err);
            } finally {
              // Re-enable the function after the response is received
              dislikeButtonEnabled = true;
            }
          }

          function timeDifference(current, previous) {
            const milliSecondsPerMinute = 60 * 1000;
            const milliSecondsPerHour = milliSecondsPerMinute * 60;
            const milliSecondsPerDay = milliSecondsPerHour * 24;
            const milliSecondsPerMonth = milliSecondsPerDay * 30;
            const milliSecondsPerYear = milliSecondsPerDay * 365;

            const elapsed = current - previous;

            if (elapsed < milliSecondsPerMinute) {
              return Math.round(elapsed / 1000) + ' seconds ago';
            } else if (elapsed < milliSecondsPerHour) {
              return Math.round(elapsed / milliSecondsPerMinute) + ' minutes ago';
            } else if (elapsed < milliSecondsPerDay) {
              return Math.round(elapsed / milliSecondsPerHour) + ' hours ago';
            } else if (elapsed < milliSecondsPerMonth) {
              return Math.round(elapsed / milliSecondsPerDay) + ' days ago';
            } else if (elapsed < milliSecondsPerYear) {
              return Math.round(elapsed / milliSecondsPerMonth) + ' months ago';
            } else {
              return Math.round(elapsed / milliSecondsPerYear) + ' years ago';
            }
          }

          function createCommentElement(username, content, createdAt, image, isCurrentUserComment, commentId, userId) {
            const commentDiv = document.createElement('div');
            commentDiv.classList.add('comment');

            const leftDiv = document.createElement('div');
            leftDiv.classList.add('left');
            const img = document.createElement('img');
            img.src = decodeHTML(image); // Replace with the correct image URL
            img.alt = 'User Image';
            leftDiv.appendChild(img);

            const rightDiv = document.createElement('div');
            rightDiv.classList.add('right');

            const metaDataDiv = document.createElement('div');
            metaDataDiv.classList.add('metaData');
            const userPara = document.createElement('p');
            userPara.classList.add('user');
            userPara.textContent = username;
            metaDataDiv.appendChild(userPara);
            const timePara = document.createElement('p');
            timePara.classList.add('time');
            const currentDate = new Date();
            timePara.textContent = timeDifference(currentDate, new Date(createdAt)); // Calculate time difference, you need to implement this function
            metaDataDiv.appendChild(timePara);
            rightDiv.appendChild(metaDataDiv);

            const textPara = document.createElement('p');
            textPara.classList.add('text');
            textPara.textContent = decodeHTML(content);
            rightDiv.appendChild(textPara);

            // <div class="delete">
            //     <i class="ri-delete-bin-7-line"></i>
            // </div>
            commentDiv.appendChild(leftDiv);
            commentDiv.appendChild(rightDiv);

            if (isCurrentUserComment) {
              const deleteDiv = document.createElement('div');
              deleteDiv.classList.add('delete');
              const deleteIcon = document.createElement('i');
              deleteIcon.classList.add('ri-delete-bin-7-line');
              deleteDiv.appendChild(deleteIcon);

              deleteDiv.addEventListener('click', () => {
                // Call the backend API to delete the comment and remove the comment from the UI
                deleteComment(commentId, userId);
                commentDiv.remove();
              });

              commentDiv.appendChild(deleteDiv);
            }

            return commentDiv;
          }
        })
      </script>
      </body>

      </html>