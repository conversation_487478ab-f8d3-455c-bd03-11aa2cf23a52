<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-RT36RYDNZL"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-RT36RYDNZL');
  </script>
  <!-- Google Tag Manager -->
  <script>(function (w, d, s, l, i) {
      w[l] = w[l] || []; w[l].push({
        'gtm.start':
          new Date().getTime(), event: 'gtm.js'
      }); var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-N9CBSHXN');</script>
  <!-- End Google Tag Manager -->
  <meta charset='UTF-8' />
  <meta name="description" content="Learn how to build modern websites with animations, explore awwwards-winning or award-winning websites, and courses on website creation. Master web technologies like Node.js, MongoDB, Socket.io, and databases such as DBMS. Dive into programming with Java, C, Python, and explore data analysis, blockchain, CPT, aptitude, HTML, CSS, and JavaScript for a comprehensive learning experience in modern web technologies.">
  <meta name="keywords" content="modern web technologies, animations, award-winning websites, Node.js, MongoDB, Socket.io, DBMS, Java, C, Python, data analysis, blockchain, CPT, aptitude, HTML, CSS, JavaScript">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="https://www.sheryians.com">

  <meta http-equiv='X-UA-Compatible' content='IE=edge' />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

  <title>
    <%= title %>
  </title>
  <link rel="apple-touch-icon" sizes="57x57" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="apple-touch-icon" sizes="60x60" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="apple-touch-icon" sizes="72x72" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="apple-touch-icon" sizes="76x76" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="apple-touch-icon" sizes="114x114" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="apple-touch-icon" sizes="120x120" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="apple-touch-icon" sizes="144x144" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="apple-touch-icon" sizes="152x152" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="apple-touch-icon" sizes="180x180" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="icon" type="image/png" sizes="192x192" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="icon" type="image/png" sizes="32x32" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="icon" type="image/png" sizes="96x96" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link rel="icon" type="image/png" sizes="16x16" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
  <meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
  <meta name="theme-color" content="#ffffff">
  <link rel="stylesheet" href="/css/common.css">

  <script defer>
    function hidePreLoader() {
      document.querySelector('#preLoder').style.opacity = '0'
      setTimeout(() => {
        document.querySelector('#preLoder').style.display = 'none'
      }, 500);
    }

    function observeAndCallback(element, callback, options) {
      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            callback(entry.target); // Run the callback when the element is intersecting
            observer.disconnect(); // Disconnect the observer after the callback is executed
          }
        });
      }, options);

      observer.observe(element);
    }

    function applyLazyLoading() {
      document.querySelectorAll('[load-lazy]').forEach(lazyLoadingElement => {

        function callbackFunction() {
          const src = lazyLoadingElement.getAttribute('load-lazy')
          lazyLoadingElement.removeAttribute('load-lazy')
          lazyLoadingElement.setAttribute('src', src)
        }

        observeAndCallback(lazyLoadingElement, callbackFunction, {
          threshold: 0.5
        });
      })
    }

    window.addEventListener('DOMContentLoaded', (event) => {

      applyLazyLoading()
    })
  </script>
  