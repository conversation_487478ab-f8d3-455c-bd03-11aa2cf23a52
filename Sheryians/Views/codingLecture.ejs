<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link
    href="https://cdn.jsdelivr.net/npm/remixicon@4.3.0/fonts/remixicon.css"
    rel="stylesheet"
/>
    <link rel="stylesheet" href="/css/common.css">
    <link rel="stylesheet" href="/css/coding.css">
</head>
<body>
    <div class="code">
        <div class="overlaySubmission">
            <div class="codeDiv">
                <div id="submissionCode"></div>
                <i onclick="this.closest('.overlaySubmission').style.display = 'none'" class="ri-close-line"></i>
            </div>
        </div>
        <div onclick="highlight(this)" class="left">
            <div class="heading">
               <div onclick="changeTab(this,1)" class="active">
                <i class="ri-article-fill"></i>
                <p>Assessment</p>
               </div>
               <div id="submissionHeading" onclick="changeTab(this,2)" class="">
                <i class="ri-send-plane-2-fill"></i>
                <p>Submission</p>
               </div>
               <div class="solutionHeading" onclick="changeTab(this,3)">
                <i class="ri-flask-fill"></i>
                <p>Solution</p>
               </div>
               <div class="reviewHeading" onclick="changeTab(this,4)">
                <i class="ri-file-list-3-fill"></i>
                <p>Review</p>
               </div>
            </div>
            <div class="confirmPopup">
                <div class="overlay">
                    <p>If you continue, your current score will be locked, and you will not earn additional points for any correct submissions made afterward.</p>                
                    <div>
                        <button onclick="renderSolutionTab(event)">Continue ⚠️</button>
                        <button onclick="closePopup(this,event)">Cancel</button>
                    </div>
                </div>
            </div>
            <div class="confirmPopupReview">
                <div class="overlay">
                    <p>If you review your code, your current score will be locked, and you will not earn additional points for any correct submissions made afterward.</p>                
                    <div>
                        <button onclick="renderReviewTab(event)">Continue ⚠️</button>
                        <button onclick="closeReviewPopup(this,event)">Cancel</button>
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="tab question active">
                    <div class="title">
                        <p><%=problem.title%></p>
                    </div>
                    <pre class="description"><%=problem.description%> </pre>
                    <%if(problem.image){%>
                        <div class="image">
                            <img src="<%=problem.image%>" alt="">
                        </div>
                    <%}%>
                    <div class="sampleTestCases">
                        <%problem.sampleTestcases.forEach((test,idx)=>{%>
                            <h3>Example <%=idx+1%></h3>
                            <div class="testcases">
                                <%if(test.stdin){%>
                                    <p>Sample Input</p>
                                    <div class="stdin">
                                        <pre><%=test.stdin%></pre>
                                    </div>
                                <%}%>
                                <%if(test.stdout){%>
                                    <p>Sample Output</p>
                                    <div class="stdout">
                                        <pre><%=test.stdout%></pre>
                                    </div>
                                <%}%>
                                <%if(test.explaination){%>
                                    <p>Explaination</p>
                                    <div class="explaination">
                                        <pre><%=test.explaination%></pre>
                                    </div>
                                <%}%>
                                
                            </div>
                        <%})%>
                    </div>
                    <%if(problem.inputFormat){%>
                        <div class="">
                            <h4>Input Format</h4>
                            <div class="inputFormat">
                                <pre><%=problem.inputFormat%></pre>
                            </div>
                        </div>
                    <%}%>
                    <%if(problem.outputFormat){%>
                        <div class="">
                            <h4>Output Format</h4>
                            <div  class="outputFormat">
                                <pre><%=problem.outputFormat%></pre>
                            </div>
                        </div>
                    <%}%>
                    <%if(problem.hint || problem.explaination){%>
                        <div class="explaination">
                            <h3><%=problem.hint ? "Hint" : "Explaination"%></h3>
                            <pre><%=problem.hint || problem.explaination%></pre>
                        </div>
                    <%}%>
                    <%if(problem.constrains.length > 0 && problem.constrains[0]){%>
                        <div class="constrains">
                            <h3>Constraints</h3>
                            <ul>
                                
                            </ul>
                        </div>
                    <%}%>
                </div>
                <div class="tab submission">
                    <div class="head">
                        <p>Result</p>
                        <p>Cases</p>
                        <p>Language</p>
                        <p>Runtime</p>
                        <p>Memory</p>
                    </div>
                    <div class="allSubmissions">
                        <div class="dotLoader loader"></div> 
                        
                    </div>
                </div>
                <div class="tab solution">
                    <div class="solution-tabs" id="solutionTabs"></div> <!-- Tab buttons -->
                    <div id="solutionEditor"></div>
                </div>
                <div class="tab review">
                    <div class="reviewEditor">
                        <div class="reviewLoader"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="verticalResizer">
            <div class="line"></div>
        </div>
        <div class="right">
            <div onclick="highlight(this)" class="right-top">
                <div class="heading">
                    <div class="editor-left">
                        <div onclick="showTabs(this)" class="active">
                            <i class="codeHeading ri-code-s-slash-line"></i>
                            <p>Code</p>
                        </div>
                    </div>
                    <div class="editor-right">
                        <div class="mergeBtn"><div class="dotLoader"></div> <span>Running</span>
                    </div>
                        <button class="resetBtn" onclick="resetCode()"><i class="ri-refresh-line"></i> Reset</button>
                        <button class="runBtn" onclick="runCode()"><i class="ri-play-fill"></i> Run</button>
                        <button class="submitBtn" onclick="submitCode()"><i class="ri-logout-box-r-line"></i> Submit</button>
                    </div>
                </div>
                <div class="language_topic">
                    <%let defaultCompilerId = null%>
                    <select id="languageSelect" onchange="changeLanguage(this.value)">
                    <%if(!lecture.codeData || Object.keys(lecture.codeData).length === 0){%>
                        <% const compilerToLang = {
                            "62": "Java",
                            "71": "Python",
                            "49": "CPP",
                            "63": "JavaScript"
                        };%>
                          <option value="<%= lecture.compilerId %>">
                            <%= compilerToLang[lecture.compilerId]%>
                          </option>
                    <%}else{%>
                        <% Object.entries(lecture.codeData).forEach(([langKey, langData]) => { %>
                            <% if (langData.enabled) { %>
                                <%console.log(langData.default,langData.compilerId)%>
                            <% if (langData.default) { defaultCompilerId = langData.compilerId; } %>
                            <option value="<%= langData.compilerId %>">
                                <%= langKey === 'c_cpp' ? 'C' : langKey.charAt(0).toUpperCase() + langKey.slice(1) %>
                            </option>
                            <% } %>
                        <% }) %>
                    <%}%>
                    </select>
                    <div class="line"></div>
                    <p><%=problem.topic%></p>
                </div>
                <div onchange="saveCode()" id="editor"></div>

            </div>
        <div class="horizontalResizer">
            <div class="line"></div>
        </div>
        <div onclick="highlight(this)" class="right-bottom highlight" style="height: 50%;">
            <div class="heading">
                <div onclick="showTabs(this)"  class="active">
                    <i class="testcaseHeading ri-checkbox-fill" style="display: block;"></i>
                    <div class="testCaseLoader" style="display: none;"></div>
                    <p>Testcases</p>
                </div>
                <div class="button">
                    <a class="button discord" onclick="joinDiscord('<%= discord_channel_id %>','<%= discord_role_id %>','<%= userId %>')">
                      <span>Ask Doubt</span></i>
                    </a>
                    <button class="nextButton" onclick="nextLec(this)">Next</button>
                  </div>
            </div>
            <div class="output">
                <div class="result">
                    <h2>Accepted</h2>
                    <small class="runtime">Runtime: 27ms</small>
                </div>
                <div class="empty">
                    <p>Run code to see the results</p>
                </div>
                <div class="cases"></div>
                <div class="selectedCase"></div>
            </div>
        </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="/ace-builds/src-noconflict/ace.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/ace-editor-render.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="/ace-builds/src-noconflict/ext-emmet.js"></script>
    <script src="/ace-builds/src/ace.js" type="text/javascript" charset="utf-8"></script>
    <script src="/ace-builds/src-noconflict/ext-language_tools.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.7.2/axios.min.js" integrity="sha512-JSCFHhKDilTRRXe9ak/FJ28dcpOJxzQaCd3Xg8MyF6XFjODhy/YMCM8HW0TFDckNHWUewW+kfvhin43hKtJxAw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <script>
        let token 
        let interval = 100
        let seenSolution = <%=seenSolution%>;
        let completed = <%=completed%>;
        let languageId = "<%= lecture.compilerId%>";
        var editor = ace.edit("editor");
        var solutionEditor = ace.edit("solutionEditor");
        var submissionEditor = ace.edit("submissionCode");
        const codeData = <%-JSON.stringify(lecture.codeData)%>
        const modeMap = {
            "62": "java",
            "71": "python",
            "49": "c_cpp",
            "63": "javascript"
        };
        editor.setTheme("ace/theme/monokai");
        solutionEditor.setTheme("ace/theme/monokai");
        submissionEditor.setTheme("ace/theme/monokai");
        const codeMap = <%-JSON.stringify(code)%>; // a map like { python: "some code", java: "some code" }
        const isCodeDataEmpty = !codeData || Object.keys(codeData).length === 0;
        console.log(codeMap,codeData);

        function changeLanguage(compilerId) {
            const langEntry = Object.values(codeData).find(lang => lang.compilerId == compilerId);
            
            if (!langEntry) return;
            const langKey = Object.keys(codeData).find(key => codeData[key].compilerId == compilerId);
            const aceMode = `ace/mode/${modeMap[compilerId] || "text"}`;

            languageId = compilerId; // Update global if needed
            localStorage.setItem("preferredLanguage", compilerId); // Save preference
            console.log(langEntry,langKey);
            const mainCode = (codeMap && codeMap[langKey]) || langEntry.templateCode || langEntry.mainCode || '';
            // Set ACE mode + content
            editor.session.setMode(aceMode);
            editor.setValue(mainCode, 1);
            
        }
        function initializeEditorFromDefault() {
            const templateFallback = `<%= lecture.code_template || lecture.main_code %>`;
            const defaultCompilerId = `<%= defaultCompilerId%>`;
            const savedCompilerId = localStorage.getItem('preferredLanguage');
            const lectureCompilerId = `<%= lecture.compilerId %>`;
            // Check if saved compilerId is enabled in codeData (when not empty)
            if (!isCodeDataEmpty && savedCompilerId) {
                const savedLangKey = Object.keys(codeData).find(
                    key => codeData[key].compilerId == savedCompilerId
                );
                console.log(savedLangKey,codeData[savedLangKey]);
                if (!savedLangKey || !codeData[savedLangKey].enabled) {
                    savedCompilerId = null;
                }
            }

            const isCodeMapEmpty = !codeMap || Object.keys(codeMap).length === 0;

            let finalCompilerId = isCodeDataEmpty ? lectureCompilerId : savedCompilerId || defaultCompilerId || lectureCompilerId
            console.log(finalCompilerId,defaultCompilerId,savedCompilerId,lectureCompilerId,"loll");
            
            let langKey = Object.keys(codeData).find(
                key => codeData[key].compilerId == finalCompilerId
            ) || modeMap[finalCompilerId] || null;

            // Step 2: If invalid or not enabled, fallback
            if(!isCodeDataEmpty){
                if (!langKey || !codeData[langKey].enabled) {
                    const fallbackEntry = Object.entries(codeData).find(([_, val]) => val.enabled);
                    if (fallbackEntry) {
                        langKey = fallbackEntry[0];
                        finalCompilerId = fallbackEntry[1].compilerId;
                    } else {
                        langKey = null;
                        finalCompilerId = null;
                    }
                }
            }
            console.log(finalCompilerId);
            // Step 3: Set value in <select id="languageSelect">
            const select = document.getElementById("languageSelect");
            if (select && finalCompilerId) {
                select.value = finalCompilerId;
            }

            languageId = finalCompilerId; // Update global if needed
            // Step 4: Resolve final code
            let finalCode = '';
            if (!isCodeMapEmpty && langKey && codeMap[langKey]) {
                console.log("1")
                finalCode = codeMap[langKey];
            } else if (!isCodeDataEmpty && langKey && codeData[langKey].templateCode) {
                console.log("2")
                finalCode = codeData[langKey].templateCode;
            } else {
                console.log("3")
                finalCode = templateFallback || '';
            }
            // Step 5: Set editor mode + content
            if (langKey) {
                const mode = langKey === "c_cpp" ? "c_cpp" : langKey.toLowerCase();
                const aceMode = `ace/mode/${mode}`;

                editor.session.setMode(aceMode);
                solutionEditor.session.setMode(aceMode);
                submissionEditor.session.setMode(aceMode);

                editor.setValue(finalCode, 1);
            } else {
                // Fallback: only load code in editor if nothing is available
                console.log(finalCompilerId)
                console.log(modeMap[finalCompilerId])
                const aceMode = `ace/mode/${modeMap[finalCompilerId]}`;
                editor.session.setMode(aceMode);
                editor.setValue(finalCode, 1);

            }

            // Optional: persist the selected compilerId
            if (finalCompilerId) {
                localStorage.setItem("preferredLanguage", finalCompilerId);
            }
        }
        
        initializeEditorFromDefault()

        editor.setShowPrintMargin(false);
        editor.session.setUseWrapMode(true);
        editor.setOptions({
            enableBasicAutocompletion: true,
            enableSnippets: true,
            enableLiveAutocompletion: true
        });
        editor.setOption("enableEmmet", true);
        editor.setOption("fontSize", "14px");
        editor.setOption("tabSize", 4);
        editor.setOption("wrap", true);
        editor.setOption("showPrintMargin", false);
        editor.setOption("showGutter", true);
        editor.setOption("highlightActiveLine", false);
        editor.setOption("highlightSelectedWord", true);
        editor.setOption("showLineNumbers", true);
        editor.setOption("readOnly", false);
        editor.setOption("cursorStyle", "ace");
        editor.setOption("mergeUndoDeltas", true);
        editor.setOption("behavioursEnabled", true);
        editor.setOption("autoScrollEditorIntoView", true);
        editor.setOption("copyWithEmptySelection", true);
        editor.setOption("enableMultiselect", true);
        editor.setOption("fadeFoldWidgets", true);
        editor.setOption("showFoldWidgets", true);
        editor.setOption("showLineNumbers", true);
        editor.setOption("highlightActiveLine", true);
        editor.setOption("highlightSelectedWord", true);
        editor.setOption("animatedScroll", true);
        ace.config.loadModule("ace/keybinding/vscode", function () {
            editor.setKeyboardHandler("ace/keyboard/vscode");
        })
        editor.session.on('change', saveCode());
        
        function nextLec(event) {
            event.style.pointerEvents = "none"
            window.parent.dispatchEvent(new CustomEvent('nextLecture'));
        }

        const horizontalResize = document.querySelector(".horizontalResizer")
        const topDiv = document.querySelector('.right-top');
        const bottomDiv = document.querySelector('.right-bottom');
        
        const verticalResize = document.querySelector(".verticalResizer")
        const leftDiv = document.querySelector('.code .left');
        const rightDiv = document.querySelector('.code .right');

        let isVResizing = false;
        let isHResizing = false;

        verticalResize.addEventListener('touchstart', function(e) {
            isVResizing = true;
            e.preventDefault(); // Prevents the page from scrolling
            e.stopPropagation(); // Stops the event from propagating further
            document.addEventListener('touchmove', onVTouchMove);
            document.addEventListener('touchend', onTouchEnd);
        });

        horizontalResize.addEventListener('touchstart', function(e) {
            isHResizing = true;
            e.preventDefault(); // Prevents the page from scrolling
            e.stopPropagation(); // Stops the event from propagating further
            document.addEventListener('touchmove', onHTouchMove);
            document.addEventListener('touchend', onTouchEnd);
        });

        verticalResize.addEventListener('mousedown', function(e) {
            isVResizing = true;
            document.addEventListener('mousemove', onVMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });

        horizontalResize.addEventListener('mousedown', function(e) {
            isHResizing = true;
            document.addEventListener('mousemove', onHMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });

        function onHTouchMove(e) {
            if (!isHResizing) return;

            e.preventDefault(); // Prevents the page from scrolling
            e.stopPropagation(); // Stops the event from propagating further

            // Access the first touch point (assuming a single touch)
            const touch = e.touches[0];

            const containerOffsetTop = horizontalResize.parentNode.offsetTop;
            const containerHeight = horizontalResize.parentNode.offsetHeight;
            const mouseY = touch.clientY - containerOffsetTop;

            // Calculate new heights
            const topDivHeight = (mouseY / containerHeight) * 100;
            const bottomDivHeight = 100 - topDivHeight;

            // Set the heights of the top and bottom divs
            editor.resize();
            topDiv.style.height = `${topDivHeight}%`;
            bottomDiv.style.height = `${bottomDivHeight}%`;
        }

        function onVTouchMove(e) {
            if (!isVResizing) return; // Check if resizing is active

            e.preventDefault(); // Prevents the page from scrolling
            e.stopPropagation(); // Stops the event from propagating further

            // Access the first touch point (assuming a single touch)
            const touch = e.touches[0];

            const containerOffsetLeft = verticalResize.parentNode.offsetLeft;
            const containerWidth = verticalResize.parentNode.offsetWidth;
            const touchX = touch.clientX - containerOffsetLeft;

            // Calculate new widths
            const leftDivWidth = (touchX / containerWidth) * 100;
            const rightDivWidth = 100 - leftDivWidth;
            
            if(leftDivWidth <= 4){
                leftDiv.classList.add("onlyNav")
            }else{
                leftDiv.classList.remove("onlyNav")
            }
            if(rightDivWidth <= 4){
                topDiv.classList.add("onlyNav")
                bottomDiv.classList.add("onlyNav")
            }else{
                topDiv.classList.remove("onlyNav")
                bottomDiv.classList.remove("onlyNav")
            }
            // Set the widths of the left and right divs
            leftDiv.style.width = `${leftDivWidth}%`;
            rightDiv.style.width = `${rightDivWidth}%`;
        }

        function onVMouseMove(e) {
            if (!isVResizing) return;

            const containerOffsetLeft = verticalResize.parentNode.offsetLeft;
            const containerWidth = verticalResize.parentNode.offsetWidth;
            const mouseX = e.clientX - containerOffsetLeft;

            // Calculate new widths
            const leftDivWidth = (mouseX / containerWidth) * 100;
            const rightDivWidth = 100 - leftDivWidth;
            if(leftDivWidth <= 4){
                leftDiv.classList.add("onlyNav")
            }else{
                leftDiv.classList.remove("onlyNav")
            }
            if(rightDivWidth <= 4){
                topDiv.classList.add("onlyNav")
                bottomDiv.classList.add("onlyNav")
            }else{
                topDiv.classList.remove("onlyNav")
                bottomDiv.classList.remove("onlyNav")
            }
            leftDiv.style.width = `${leftDivWidth}%`;
            rightDiv.style.width = `${rightDivWidth}%`;
        }

        function onHMouseMove(e) {
            if (!isHResizing) return;

            const containerOffsetTop = horizontalResize.parentNode.offsetTop;
            const containerHeight = horizontalResize.parentNode.offsetHeight;
            const mouseY = e.clientY - containerOffsetTop;

            // Calculate new heights
            const topDivHeight = (mouseY / containerHeight) * 100;
            const bottomDivHeight = 100 - topDivHeight;

            topDiv.style.height = `${topDivHeight}%`;
            bottomDiv.style.height = `${bottomDivHeight}%`;
            editor.resize();

        }

        function onMouseUp() {
            isVResizing = false;
            isHResizing = false;
            document.removeEventListener('mousemove', onVMouseMove);
            document.removeEventListener('mousemove', onHMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }

        function onTouchEnd(e) {
            isHResizing = false;
            isVResizing = false;
            document.removeEventListener('touchmove', onVTouchMove);
            document.removeEventListener('touchmove', onHTouchMove);
            document.removeEventListener('touchend', onTouchEnd);
        }

        function closePopup(btn,event){
            event.stopPropagation()
            btn.closest(".confirmPopup").classList.remove("active")
        }
        function closeReviewPopup(btn,event){
            event.stopPropagation()
            btn.closest(".confirmPopupReview").classList.remove("active")
        }

        function renderSolutionPopup(event){
            if(seenSolution){
                renderSolutionTab(event)
            }else{
                document.querySelector(".confirmPopup").classList.add("active")
            }
        }

        function renderReviewPopup(event){
            if(seenSolution){
                renderReviewTab(event)
            }else{
                document.querySelector(".confirmPopupReview").classList.add("active")
            }
        }
        async function joinDiscord(channel_id, role_id, user_id) {
            window.parent.dispatchEvent(new CustomEvent('joinDiscord', { detail: { user_id,role_id,channel_id} }));
        }
        async function renderSolutionTab(event) {
                if (event) event.stopPropagation();

                document.querySelector(".tab.active")?.classList.remove("active");
                document.querySelector(".confirmPopup")?.classList.remove("active");

                const { data } = await axios.post("/coding/getSolution", {
                    moduleId: "<%= moduleId %>",
                    lectureId: "<%= lectureId %>",
                    courseId: "<%= courseId %>"
                });

                const codeData = data.code || {};
                
                // ❌ Skip rendering if no code exists or all values are empty
                const hasValidSolution = Object.values(codeData).some(code => code && code.trim() !== "");
                if (!hasValidSolution) return;

                seenSolution = true;
                document.querySelector(".tab.solution").classList.add("active");

                const solutionEditor = ace.edit("solutionEditor");

                // Make editor readonly and clean
                solutionEditor.setReadOnly(true);
                solutionEditor.setHighlightActiveLine(false);
                solutionEditor.setHighlightGutterLine(false);
                solutionEditor.renderer.$cursorLayer.element.style.display = "none";
                solutionEditor.setShowFoldWidgets(false);

                // Render language tabs
                const tabsContainer = document.getElementById("solutionTabs");
                tabsContainer.innerHTML = "";

                const modeMap = {
                    java: "java",
                    python: "python",
                    c_cpp: "c_cpp",
                    javascript: "javascript"
                };

                Object.entries(codeData).forEach(([lang, code]) => {
                    if (!code || code.trim() === "") return;

                    const btn = document.createElement("button");
                    btn.className = "solution-tab-btn";
                    btn.innerText = lang === "c_cpp" ? "C" : lang.charAt(0).toUpperCase() + lang.slice(1);
                    btn.onclick = () => {
                        document.querySelectorAll(".solution-tab-btn").forEach(b => b.classList.remove("active"));
                        btn.classList.add("active");

                        solutionEditor.session.setMode(`ace/mode/${modeMap[lang] || "text"}`);
                        solutionEditor.setValue(code);
                        solutionEditor.clearSelection();
                    };
                    tabsContainer.appendChild(btn);
                });

                const firstBtn = tabsContainer.querySelector("button");
                if (firstBtn) firstBtn.click();
            }
            
            async function renderReviewTab(event) {
            if (event) {
                event.stopPropagation();
            }

            document.querySelector(".tab.active").classList.remove("active");
            document.querySelector(".confirmPopupReview").classList.remove("active");
            document.querySelector(".tab.review").classList.add("active");
            const { data } = await axios.post("/coding/review-code", {
                lectureId: "<%= lectureId %>",
                courseId: "<%= courseId %>",
                code: editor.getValue(),
                problemId: "<%= problem._id %>"
            });
            document.querySelector('.reviewEditor').innerHTML = marked.parse(data.review);
                        
        }

        function highlight(event){
            document.querySelector(".left").classList.remove("highlight")
            document.querySelector(".right-top").classList.remove("highlight")
            document.querySelector(".right-bottom").classList.remove("highlight")
            event.classList.add("highlight")
        }

        function changeTab(event,tab){
            document.querySelector(".left .heading .active").classList.remove("active")
            
            event.classList.add("active")
            if(tab == 1){
                document.querySelector(".confirmPopup").classList.remove("active")
                document.querySelector(".tab.active").classList.remove("active")
                document.querySelector(".tab.question").classList.add("active")
                if(document.querySelector(".code .left").classList.contains("onlyNav")){
                    document.querySelector(".code .left").style.width = "40%"
                    document.querySelector(".code .right").style.width = "60%"
                    document.querySelector(".code .left").classList.remove("onlyNav")
                }
            }else if(tab == 2){
                document.querySelector(".confirmPopup").classList.remove("active")
                document.querySelector(".tab.active").classList.remove("active")
                document.querySelector(".tab.submission").classList.add("active")
                if(document.querySelector(".code .left").classList.contains("onlyNav")){
                    document.querySelector(".code .left").style.width = "40%"
                    document.querySelector(".code .right").style.width = "60%"
                    document.querySelector(".code .left").classList.remove("onlyNav")
                }
                renderSubmissionTab()
            }else if(tab == 3){
                if(document.querySelector(".code .left").classList.contains("onlyNav")){
                    document.querySelector(".code .left").style.width = "40%"
                    document.querySelector(".code .right").style.width = "60%"
                    document.querySelector(".code .left").classList.remove("onlyNav")
                }
                renderSolutionPopup()

            }else if(tab == 4){
                if(document.querySelector(".code .left").classList.contains("onlyNav")){
                    document.querySelector(".code .left").style.width = "40%"
                    document.querySelector(".code .right").style.width = "60%"
                    document.querySelector(".code .left").classList.remove("onlyNav")
                }
                renderReviewPopup()
            }
        }
        function showTabs(event){
            if(document.querySelector(".right-top.onlyNav")){
                document.querySelector(".code .left").style.width = "40%"
                document.querySelector(".code .right").style.width = "60%"
                document.querySelector(".right-top.onlyNav").classList.remove("onlyNav")
            }
            if(document.querySelector(".right-bottom.onlyNav")){
                document.querySelector(".code .left").style.width = "40%"
                document.querySelector(".code .right").style.width = "60%"
                document.querySelector(".right-bottom.onlyNav").classList.remove("onlyNav")
            }
        }
        function changeButton(state){
            if(state == "show"){
                document.querySelector(".runBtn").style.transform = "translateX(0px)"
                document.querySelector(".submitBtn").style.transform = "translateX(0px)"
                document.querySelector(".resetBtn").style.transform = "translateX(0px)"
                document.querySelector(".runBtn").style.opacity = "1"
                document.querySelector(".submitBtn").style.opacity = "1"
                document.querySelector(".resetBtn").style.opacity = "1"
                document.querySelector(".mergeBtn").style.opacity = "0"
                document.querySelector(".mergeBtn").style.pointerEvents = "none"
                document.querySelector(".mergeBtn").style.transform = "translate(-200px)"
            }else{
                document.querySelector(".runBtn").style.transform = "translateX(100px)"
                document.querySelector(".submitBtn").style.transform = "translateX(100px)"
                document.querySelector(".resetBtn").style.transform = "translateX(100px)"
                document.querySelector(".runBtn").style.opacity = "0"
                document.querySelector(".submitBtn").style.opacity = "0"
                document.querySelector(".resetBtn").style.opacity = "0"
                document.querySelector(".mergeBtn").style.opacity = "1"
                document.querySelector(".mergeBtn").style.pointerEvents = "all"
                document.querySelector(".mergeBtn").style.transform = "translate(0px)"
            }
        }
        
        function addLoaderAndSkeleton(){
            document.querySelector(".right-bottom .result").style.display = "none"
            document.querySelector(".right-bottom .cases").style.display = "flex"
            document.querySelector(".right-bottom .selectedCase").style.display = "block"
            document.querySelector(".right-bottom .empty").style.display = "none"
            document.querySelector(".right-bottom .heading i").style.display = "none"
            document.querySelector(".right-bottom .heading .testCaseLoader").style.display = "block"
            document.querySelector(".selectedCase").innerHTML = `
                <div class="skeleton">
                    <div class="skeleton">
                        <p></p>
                        <pre></pre>                        
                    </div>
                    <div class="skeleton">
                        <p></p>
                        <pre></pre>                        
                    </div>
                    <div class="skeleton">
                        <p></p>
                        <pre></pre>                        
                    </div>
                </div>`
            
            document.querySelector(".cases").innerHTML = `
                <p class="skeleton"></p>
                <p class="skeleton"></p>
                <p class="skeleton"></p>`
        }

        function showOutput(submissions){
            document.querySelector(".right-bottom .heading i").style.display = "block"
            document.querySelector(".right-bottom .heading .testCaseLoader").style.display = "none"
            document.querySelector(".right-top").style.height = '50%'
            document.querySelector(".right-bottom").style.height = '50%'
            document.querySelector(".right-bottom .result").style.display = "flex"
            editor.resize();
            test = submissions.every((state)=>state.status.id == 3)

            if(test){
                document.querySelector(".right-bottom .result h2").textContent = "Accepted"
                document.querySelector(".right-bottom .result h2").classList.add("accepted")
                document.querySelector(".right-bottom .result h2").classList.remove("rejected")
            }else{
                document.querySelector(".right-bottom .result h2").textContent = "Rejected"
                document.querySelector(".right-bottom .result h2").classList.remove("accepted")
                document.querySelector(".right-bottom .result h2").classList.add("rejected")
            }
            const averageRuntime = submissions.reduce((acc, submission, index, array) => {
                acc += Number(submission.time);
                if (index === array.length - 1) {
                    return acc / array.length;
                }
                return acc;
                }, 0); 
            document.querySelector(".runtime").textContent = `Runtime: ${Math.floor(averageRuntime * 1000)}ms`
            const cases = submissions.map((data,idx)=>`<p onclick='caseSelection(${idx+1})' class="${!idx && "active"} ${submissions[idx].status.id == 3 ? "correct" : "wrong"}" onclick='showCase(${idx+1})'>Case ${idx+1}</p>`)
            const casesDetail = submissions.map((data,idx)=>{
                if(data.status.id == 3){
                    return (`
                    <div class="${!idx && "active"} accepted">
                            <div>
                                <p>Input</p>
                                <pre>${data.stdin}</pre>                        
                            </div>
                            <div>
                                <p>Output</p>
                                <pre>${data.stdout}</pre>                        
                            </div>
                            <div>
                                <p>Expected Output</p>
                                <pre>${data.expected_output}</pre>                        
                            </div>
                            </div>`
                        )
                }else if(data.status.id == 4){
                    return (`<div class="${!idx && "active"} rejected">
                            <div>
                                <p>Input</p>
                                <pre>${data.stdin}</pre>                        
                            </div>
                            <div>
                                <p>Output</p>
                                <pre>${data.stdout}</pre>                        
                            </div>
                            <div>
                                <p>Expected Output</p>
                                <pre>${data.expected_output}</pre>                        
                            </div>
                            </div>`
                        )
                }else{
                    return (`<div class=" ${!idx && "active"}">
                                <div class="error">
                                    <p>${data.status.description}</p>
                                    <small>${data.message ? data.message : ""}</small>
                                    <pre class='accepted'>${data.stderr || data.compile_error || data.message}</pre>                        
                                </div>
                            </div>` 
                        )
                }

            })
            document.querySelector(".cases").innerHTML = cases.join(" ")
            document.querySelector(".selectedCase").innerHTML = casesDetail.join(" ")

        }
        function showCasesOutput(submissions){
            document.querySelector(".right-bottom .heading i").style.display = "block"
            document.querySelector(".right-bottom .heading .testCaseLoader").style.display = "none"
            document.querySelector(".right-top").style.height = '50%'
            document.querySelector(".right-bottom").style.height = '50%'
            editor.resize();
            document.querySelector(".right-bottom .result").style.display = "flex"
            test = submissions.every((state)=>state.status.id == 3)

            if(test){
                document.querySelector(".right-bottom .result h2").textContent = "Accepted"
                document.querySelector(".right-bottom .result h2").classList.add("accepted")
                document.querySelector(".right-bottom .result h2").classList.remove("rejected")
            }else{
                document.querySelector(".right-bottom .result h2").textContent = "Rejected"
                document.querySelector(".right-bottom .result h2").classList.remove("accepted")
                document.querySelector(".right-bottom .result h2").classList.add("rejected")
            }
            const averageRuntime = submissions.reduce((acc, submission, index, array) => {
                acc += Number(submission.time);
                if (index === array.length - 1) {
                    return acc / array.length;
                }
                return acc;
                }, 0); 
            document.querySelector(".runtime").textContent = `Runtime: ${Math.floor(averageRuntime * 1000)}ms`
            const cases = submissions.map((data,idx)=>`<p onclick='caseSelection(${idx+1})' class="${!idx && "active"} ${submissions[idx].status.id == 3 ? "correct" : "wrong"}" onclick='showCase(${idx+1})'>Case ${idx+1}</p>`)
            
            const casesDetail = submissions.map((data,idx)=>{
                if(data.status.id == 3){
                    return (`
                    <div class="${!idx && "active"} accepted">
                            <div>
                                <p>Result</p>
                                <pre>Accepted</pre>                        
                            </div>
                            </div>`
                        )
                }else if(data.status.id == 4){
                    return (`<div class="${!idx && "active"} rejected">
                           <div>
                                <p>Result</p>
                                <pre>Rejected</pre>                        
                            </div>
                            </div>`
                        )
                }else{
                    return (`<div class=" ${!idx && "active"}">
                                <div class="error">
                                    <p>${data.status.description}</p>
                                    <small>${data.message ? data.message : ""}</small>
                                    <pre class='accepted'>${data.stderr || data.message || data.compile_error}</pre>                        
                                </div>
                            </div>` 
                        )
                }

            })
            document.querySelector(".cases").innerHTML = cases.join(" ")
            document.querySelector(".selectedCase").innerHTML = casesDetail.join(" ")
        }
        
        function showError(text){
            changeButton("show")
            document.querySelector(".right-bottom .heading i").style.display = "block"
            document.querySelector(".right-bottom .heading .testCaseLoader").style.display = "none"
            document.querySelector(".right-top").style.height = '50%'
            document.querySelector(".right-bottom").style.height = '50%'
            editor.resize()
            document.querySelector(".right-bottom .cases").style.display = "none"
            document.querySelector(".right-bottom .selectedCase").style.display = "none"
            document.querySelector(".right-bottom .empty").style.display = "flex"
            document.querySelector(".right-bottom .empty p").classList.add("error")
            document.querySelector(".right-bottom .empty p").textContent = text

        }
        function resetCode(){
            if(isCodeDataEmpty){
                editor.setValue(`<%-lecture.code_template ? lecture.code_template : lecture.main_code%>`)
            }else{
                const langKey = Object.keys(codeData).find(key => codeData[key].compilerId == languageId);
                const mainCode = codeData[langKey].templateCode || codeData[langKey].mainCode || '';
                editor.setValue(mainCode, 1);
            }
        }
        async function runCode(event){
            try{
                if(!editor.getValue()) return
                changeButton("hide")
                addLoaderAndSkeleton()
                let data = await axios.post("/coding/run-code",{compilerId:languageId,lectureId:"<%=lectureId%>",moduleId:"<%=moduleId%>",code:editor.getValue()})
                token = data.data.tokens
                checkRunCodeStatus()
            }catch(err){
                if(err.response.status == 401){
                    window.parent.dispatchEvent(new CustomEvent('openSignup'));
                    return
                }
                showError("You have attempted to run code too soon. Please try again in a few seconds")
                console.log(err.status)
            }
        }
        
        async function submitCode(event){
            try{
                if(!editor.getValue()) return
                addLoaderAndSkeleton()  
                let data = await axios.post("/coding/submit-code",{compilerId:languageId,lectureId:"<%=lectureId%>",moduleId:"<%=moduleId%>",code:editor.getValue()})
                token = data.data.tokens.map((tkn)=>tkn.token) 
                checkSubmitCodeStatus()
            }catch(err){
                if(err.response.status == 401){
                    window.parent.dispatchEvent(new CustomEvent('openSignup'));
                    return
                }
                showError("You have attempted to run code too soon. Please try again in a few seconds")
                console.log(err)
            }
        }

        async function checkRunCodeStatus() {
            let data
            let count = 0
            const intervalId = setInterval(async() => {
                if(count > 100){
                    clearInterval(intervalId);
                    showError("Server Timed Out")
                }
                count++
                if (data && data.data.data.every((state)=>state.status.id != 1 && state.status.id != 2)) {
                    clearInterval(intervalId);
                    changeButton("show")
                    showOutput(data.data.data)
                    count = 0
                } else {
                    data = await axios.post(`/coding/checkRunStatus`,{token,lectureId:'<%=lectureId%>',courseId:'<%=courseId%>'})
                }
                if(count > 3){
                    document.querySelector(".mergeBtn span").textContent = "Analysing"
                }
                

            }, 300);
        }

    let checkingStatus = false; // Flag to prevent multiple intervals

    async function checkSubmitCodeStatus() {
        let data
        let count = 0

        const intervalId = setInterval(async() => {
            if(count > 100){
                clearInterval(intervalId);
                showError("Server Timed Out")
            }
            count++
            if (checkingStatus) return; // Exit if already checking status
            checkingStatus = true; // Set the flag
            try{
                if (interval > 10000 || data && data.data.data.every((state)=>state.status.id != 1 && state.status.id != 2)) {
                    clearInterval(intervalId);
                    changeButton("show")
                    showCasesOutput(data.data.data)
                    document.querySelector(".confirmPopup").classList.remove("active")
                    document.querySelector(".left .heading .active").classList.remove("active")
                    document.querySelector(".tab.active").classList.remove("active")
                    document.querySelector(".tab.submission").classList.add("active")
                    document.querySelector("#submissionHeading").classList.add("active")
                    if(document.querySelector(".code .left").classList.contains("onlyNav")){
                        document.querySelector(".code .left").style.width = "40%"
                        document.querySelector(".code .right").style.width = "60%"
                        document.querySelector(".code .left").classList.remove("onlyNav")
                    }
                    renderSubmissionTab()
                    // if(document.querySelector(".content .submission").classList.contains("active")){
                    //     renderSubmissionTab()
                    // }
                    const percentage = (Math.floor(data.data.data.filter(data=> data.status.id == 3).length / data.data.data.length * 100))

                    if(!seenSolution && !completed){
                        window.parent.dispatchEvent(new CustomEvent('updateCodePoints', { detail: { points: percentage, id: '<%=lectureId%>' } }));
                    }
                    if(percentage == 100){
                        completed = true
                    }

                } else {
                    const language =
                        (codeData && Object.keys(codeData).find(key => codeData[key].compilerId == languageId)) ||
                        modeMap[languageId] ||
                    null; 
                    console.log(language)
                    data = await axios.post(`/coding/checkStatus`,{language,token,lectureId:'<%=lectureId%>',courseId:'<%=courseId%>'})
                }
                if(count > 5){
                    document.querySelector(".mergeBtn span").textContent = "Analysing"
                }
                
            }catch(err){
                console.log(err)
            }finally{
                checkingStatus = false; // Reset the flag

            }
        }, 300);
    }
    async function renderSubmissionTab(event){ 
            const {data} = await axios.post("/coding/getSubmission",{lectureId:"<%=lectureId%>"})
            let clutter = ''
            console.log(data)
            for (const cases of data.submissions){
                const total = cases.testcases.length
                const passed = cases.testcases.filter((c)=>c.passed).length
                if(passed == total){
                    clutter += `
                        <div class="result" onclick="showSubmissionCode(this)" data-code="${btoa(unescape(encodeURIComponent(cases.code)))}">
                            <div>
                                <p class="correct">Accepted</p>
                                <small>${new Date(cases.date).toLocaleString("en-GB",{hour12: true, hour: 'numeric', minute: 'numeric'})}</small>
                                 <small>${new Date(cases.date).toLocaleString("en-GB",{day: 'numeric', month: 'long',year: 'numeric'})}</small>    
                            </div>
                            <p>${passed}/${total}</p>
                            <p>${cases.language.toUpperCase()}</p>
                            <p>${Math.max(...cases.testcases.map(data => Number(data.executionTime)))* 1000}ms</p>
                            <p>${(Math.max(...cases.testcases.map(data => Number(data.memoryUsage))) / 1000).toFixed(1)} MB</p>
                        </div>`
                }
                else{
                    clutter += `
                        <div class="result" onclick="showSubmissionCode(this)" data-code="${btoa(unescape(encodeURIComponent(cases.code)))}">
                            <div>
                                <p class="incorrect">Rejected</p>
                                <small>${new Date(cases.date).toLocaleString("en-GB",{hour12: true, hour: 'numeric', minute: 'numeric'})}</small>
                                 <small>${new Date(cases.date).toLocaleString("en-GB",{day: 'numeric', month: 'long',year: 'numeric'})}</small>    
                            </div>
                            <p>${passed}/${total}</p>
                            <p>${cases.language.toUpperCase()}</p>
                            <p>${Math.max(...cases.testcases.map(data => Number(data.executionTime))) * 1000}ms</p>
                            <p>${(Math.max(...cases.testcases.map(data => Number(data.memoryUsage))) / 1000).toFixed(1)} MB</p>
                        </div>`
                }
            }
            document.querySelector(".allSubmissions").innerHTML = clutter
        }

    
    function caseSelection(id){
        document.querySelector(`.cases .active`).classList.remove("active") 
        document.querySelector(`.selectedCase .active`).classList.remove("active") 
        document.querySelector(`.cases p:nth-child(${id})`).classList.add("active") 
        document.querySelector(`.selectedCase>div:nth-child(${id})`).classList.add("active") 
    }

    function showSubmissionCode(event){
        submissionEditor.setValue(decodeURIComponent(escape(atob(event.getAttribute("data-code")))))
        submissionEditor.setReadOnly(true);
        submissionEditor.setHighlightActiveLine(false); // Disable line highlight
        submissionEditor.setHighlightGutterLine(false); // Disable gutter line highlight
        submissionEditor.setReadOnly(true); // Make submissionEditor read-only
        submissionEditor.renderer.$cursorLayer.element.style.display = "none"; // Hide the cursor
        submissionEditor.clearSelection();
        submissionEditor.renderer.setShowGutter(false);
        document.querySelector(".overlaySubmission").style.display = "flex"
    }
    function convertDateToLongFormat(dateStr) {
        const [day, month, year] = dateStr.split('/');
        const dateObj = new Date(year, month - 1, day);
        const options = { year: '2-digit', month: 'long', day: 'numeric' };
        return dateObj.toLocaleDateString('en-US', options);
    }

    function convertTo12HourFormat(timeStr) {
        const [hours, minutes, seconds] = timeStr.split(':').map(Number);
        const period = hours >= 12 ? 'PM' : 'AM';
        const hours12 = hours % 12 || 12;
        const formattedTime = `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
        return formattedTime;
    }
    function checkNextLec(event) {
      window.parent.dispatchEvent(new CustomEvent('checkNextLecture'));
    }
    checkNextLec()

    window.addEventListener('message', function (event) {
      if (!event.data) {
        document.querySelectorAll(".nextButton").forEach((btn) => {
          btn.style.opacity = 0.5
          btn.style.pointerEvents = 'none'
        })
      }
    });

    function saveCode(fn, delay) {
        let timeoutID;
        console.log("object");
        const currentCode = editor.getValue();
        const langKey = Object.keys(codeData).find(key => codeData[key].compilerId == languageId);        
        if (!codeMap) codeMap = {};
        if (langKey) {
            codeMap[langKey] = currentCode;
        }
        return function(...args) {
            clearTimeout(timeoutID);
            timeoutID = setTimeout(async() => {
                try{
                    console.log(editor.getValue(),languageId);
                    await axios.post(`/coding/saveCode`,{code:editor.getValue(),lectureId:'<%=lectureId%>',compilerId:languageId})

                }catch(err){
                    console.log(err);
                }
            }, 1000);
        };
    }
    <%if(problem.constrains.length > 0 && problem.constrains[0]){%>
        let constrainsClutter = ""
        <%problem.constrains.forEach((e)=>{%>
            constrainsClutter += `<li>${"<%=e%>".replace(/\^(\d+)/g, '<span class="superscript">$1</span>')}</li>`
        <%})%>
        document.querySelector(".constrains ul").innerHTML = constrainsClutter
    <%}%>
    </script>
</body>
</html>

<!-- hide submit data from backend api while submitting  -->