<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SRC Submissions - Sheryians Coding School</title>
    <link rel="stylesheet" href="/Assets/css/src_submissions_updated.css?v=<%= Date.now() %>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>

<body>
    <div class="src-submissions">
        <div class="src-submissions__header">
            <h1>SRC Submissions</h1>
            <p>Manage and review all submissions from prospective students</p>
        </div>

        <!-- Filters Section -->
        <div class="src-submissions__filters">
            <h3 class="src-submissions__filters-title">Filters & Search</h3>
            <div class="src-submissions__filters-grid">
                <!-- Full Name Filter -->
                <div class="src-submissions__filters-group">
                    <label for="fullname">Full Name</label>
                    <input type="text" id="fullname" placeholder="Search by name..."
                        title="Enter the full name of the candidate">
                </div>

                <!-- Email Filter -->
                <div class="src-submissions__filters-group">
                    <label for="email">Email</label>
                    <input type="text" id="email" placeholder="Search by email..."
                        title="Enter the email address of the candidate">
                </div>

                <!-- Expertise Filter -->
                <div class="src-submissions__filters-group">
                    <label for="expertise">Expertise</label>
                    <select id="expertise" title="Select the expertise area">
                        <option value="">All Expertise</option>
                        <!-- Expertise will be populated from API -->
                    </select>
                </div>

                <!-- City Filter -->
                <div class="src-submissions__filters-group">
                    <label for="city">City</label>
                    <select id="city" title="Select the city">
                        <option value="">All Cities</option>
                        <!-- Cities will be populated from API -->
                    </select>
                </div>

                <!-- Applied Role Filter -->
                <div class="src-submissions__filters-group">
                    <label for="applied_for_role">Applied Role</label>
                    <select id="applied_for_role" title="Select the applied role">
                        <option value="">All Roles</option>
                        <!-- Roles will be populated from API -->
                    </select>
                </div>

                <!-- Status Filter -->
                <div class="src-submissions__filters-group">
                    <label for="status">Status</label>
                    <select id="status" title="Select the submission status">
                        <option value="">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="good fit">Good Fit</option>
                        <option value="best fit">Best Fit</option>
                        <option value="not fit">Not Fit</option>
                    </select>
                </div>
            </div>
            <div class="src-submissions__filters-actions">
                <button class="clear" onclick="clearFilters()">Clear All</button>
            </div>
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="src-submissions__error" style="display: none;"></div>

        <!-- Table Section -->
        <div class="src-submissions__table-container">
            <!-- Loading State -->
            <div id="loadingState" class="src-submissions__loading">
                Loading submissions...
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="src-submissions__empty" style="display: none;">
                No submissions found matching your criteria
            </div>

            <!-- Data Table -->
            <table class="src-submissions__table" id="submissionsTable">
                <thead>
                    <tr>
                        <th class="sortable" data-sort="fullname">Full Name</th>
                        <th class="sortable" data-sort="email">Email</th>
                        <th class="sortable" data-sort="phone">Phone</th>
                        <th class="sortable" data-sort="expertise">Expertise</th>
                        <th class="sortable" data-sort="applied_for_role">Applied Role</th>
                        <th class="sortable" data-sort="city">City</th>
                        <th class="sortable" data-sort="worklink">Worklink</th>
                        <th class="sortable" data-sort="status">Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="submissionsTableBody">
                    <!-- Data will be populated here -->
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="src-submissions__pagination" id="paginationContainer" style="display: none;">
                <div class="src-submissions__pagination-info" id="paginationInfo">
                    <!-- Pagination info will be populated here -->
                </div>
                <div class="src-submissions__pagination-controls" id="paginationControls">
                    <!-- Pagination controls will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="src-submissions__modal-backdrop" id="editModal">
        <div class="src-submissions__modal">
            <div class="src-submissions__modal-header">
                <h2 class="src-submissions__modal-title">Edit Submission</h2>
                <button class="src-submissions__modal-close" onclick="closeEditModal()">&times;</button>
            </div>
            <div class="src-submissions__modal-body">
                <div class="src-submissions__modal-section">
                    <h3 class="src-submissions__modal-section-title">Candidate Information</h3>
                    <div class="src-submissions__info-item">
                        <div class="src-submissions__info-label">Full Name</div>
                        <div class="src-submissions__info-value" id="modalFullname"></div>
                    </div>
                    <div class="src-submissions__info-item">
                        <div class="src-submissions__info-label">Email</div>
                        <div class="src-submissions__info-value" id="modalEmail"></div>
                    </div>
                    <div class="src-submissions__info-item">
                        <div class="src-submissions__info-label">Phone</div>
                        <div class="src-submissions__info-value" id="modalPhone"></div>
                    </div>
                    <div class="src-submissions__info-item">
                        <div class="src-submissions__info-label">Expertise</div>
                        <div class="src-submissions__info-value" id="modalExpertise"></div>
                    </div>
                    <div class="src-submissions__info-item">
                        <div class="src-submissions__info-label">Applied Role</div>
                        <div class="src-submissions__info-value" id="modalAppliedRole"></div>
                    </div>
                    <div class="src-submissions__info-item">
                        <div class="src-submissions__info-label">City</div>
                        <div class="src-submissions__info-value" id="modalCity"></div>
                    </div>
                    <div class="src-submissions__info-item">
                        <div class="src-submissions__info-label">Thoughts</div>
                        <div class="src-submissions__info-value" id="modalThoughts"></div>
                    </div>
                    <div class="src-submissions__info-item">
                        <div class="src-submissions__info-label">Worklink</div>
                        <div class="src-submissions__info-value" id="modalWorklink"></div>
                    </div>
                </div>

                <!-- Phase Selection -->
                <div class="src-submissions__form-row">
                    <div class="src-submissions__form-group">
                        <label for="modalPhase1">Phase 1</label>
                        <select id="modalPhase1">
                            <option value="pending">Pending</option>
                            <option value="accepted">Accepted</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                    <div class="src-submissions__form-group">
                        <label for="modalPhase2">Phase 2</label>
                        <select id="modalPhase2">
                            <option value="pending">Pending</option>
                            <option value="accepted">Accepted</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="src-submissions__modal-footer">
                <button class="cancel" onclick="closeEditModal()">Cancel</button>
                <button class="save" onclick="saveChanges()">Save Changes</button>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentPage = 1;
        let currentSort = null;
        let currentSortDirection = 'asc';
        let currentFilters = {};
        let debounceTimer;
        let submissions = []; // Store loaded submissions
        let currentSubmission = null; // Store currently editing submission

        // Utility function to escape HTML, crucial for security
        function escapeHtml(unsafe) {
            if (unsafe === null || typeof unsafe === 'undefined') {
                return '';
            }
            return unsafe
                .toString()
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function () {
            loadFilterOptions();
            loadSubmissions();
            setupSortHandlers();
            setupFilterListeners();
        });

        // Load filter options (expertise, roles and cities)
        async function loadFilterOptions() {
            try {
                const response = await axios.get('/src/filter-options');

                if (response.data.success) {
                    // Populate expertise dropdown
                    const expertiseSelect = document.getElementById('expertise');
                    response.data.expertise.forEach(expertise => {
                        const option = document.createElement('option');
                        option.value = expertise;
                        option.textContent = expertise;
                        expertiseSelect.appendChild(option);
                    });

                    // Populate applied_for_role dropdown
                    const roleSelect = document.getElementById('applied_for_role');
                    response.data.applied_for_role.forEach(role => {
                        const option = document.createElement('option');
                        option.value = role;
                        option.textContent = role;
                        roleSelect.appendChild(option);
                    });

                    // Populate cities dropdown
                    const citySelect = document.getElementById('city');
                    response.data.cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city;
                        option.textContent = city;
                        citySelect.appendChild(option);
                    });
                } else {
                    console.error('Failed to load filter options');
                }
            } catch (error) {
                console.error('Error loading filter options:', error);
            }
        }

        // Debounce function to prevent excessive API calls
        function debounce(func, delay) {
            return function () {
                const context = this;
                const args = arguments;
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => func.apply(context, args), delay);
            };
        }

        // Setup filter input listeners
        function setupFilterListeners() {
            // Text inputs
            const textInputs = ['fullname', 'email'];
            textInputs.forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    el.addEventListener('input', debounce(applyFilters, 500));
                }
            });

            // Dropdowns
            const dropdowns = ['expertise', 'applied_for_role', 'city', 'status', 'phase1Status', 'phase2Status'];
            dropdowns.forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    el.addEventListener('change', applyFilters);
                }
            });
        }

        // Setup sort handlers
        function setupSortHandlers() {
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', function () {
                    const sortField = this.dataset.sort;

                    if (currentSort === sortField) {
                        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSort = sortField;
                        currentSortDirection = 'asc';
                    }

                    updateSortHeaders();
                    loadSubmissions();
                });
            });
        }

        // Update sort headers visual state
        function updateSortHeaders() {
            document.querySelectorAll('.sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
                if (header.dataset.sort === currentSort) {
                    header.classList.add(currentSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
                }
            });
        }

        // Apply filters
        function applyFilters() {
            currentFilters = {
                fullname: document.getElementById('fullname').value,
                email: document.getElementById('email').value,
                expertise: document.getElementById('expertise').value,
                applied_for_role: document.getElementById('applied_for_role').value,
                city: document.getElementById('city').value,
                status: document.getElementById('status').value,
                phase1Status: document.getElementById('phase1Status').value,
                phase2Status: document.getElementById('phase2Status').value
            };

            // Remove empty filters
            Object.keys(currentFilters).forEach(key => {
                if (!currentFilters[key]) {
                    delete currentFilters[key];
                }
            });

            currentPage = 1;
            loadSubmissions();
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('fullname').value = '';
            document.getElementById('email').value = '';
            document.getElementById('expertise').value = '';
            document.getElementById('applied_for_role').value = '';
            document.getElementById('city').value = '';
            document.getElementById('status').value = '';
            document.getElementById('phase1Status').value = '';
            document.getElementById('phase2Status').value = '';

            currentFilters = {};
            currentPage = 1;
            loadSubmissions();
        }

        // Load submissions
        async function loadSubmissions() {
            try {
                showLoading();
                hideError();

                const params = new URLSearchParams({
                    page: currentPage,
                    limit: 20,
                    ...currentFilters
                });

                if (currentSort) {
                    params.append('sort', currentSort);
                    params.append('sortDirection', currentSortDirection);
                }

                const response = await axios.get(`/src?${params}`);
                const data = response.data;

                if (data.success) {
                    submissions = data.submissions; // Store submissions globally
                    displaySubmissions(data.submissions);
                    displayPagination(data);
                } else {
                    showError('Failed to load submissions');
                }
            } catch (error) {
                console.error('Error loading submissions:', error);
                showError('Error loading submissions: ' + (error.response?.data?.message || error.message));
            }
        }

        // Display submissions in table
        function displaySubmissions(submissions) {
            const tbody = document.getElementById('submissionsTableBody');

            if (!submissions || submissions.length === 0) {
                showEmpty();
                return;
            }

            hideLoading();
            hideEmpty();
            document.getElementById('submissionsTable').style.display = 'table';
            document.getElementById('paginationContainer').style.display = 'flex';

            tbody.innerHTML = '';

            submissions.forEach((submission, index) => {
                const tr = document.createElement('tr');

                if (index < 5) {
                    tr.classList.add('table-row-top-submission'); // Renamed class
                }

                tr.innerHTML = `
                    <td data-label="Applicant">
                        ${index < 5 ? `<span class="table-row-badge top-badge">${index + 1}</span>` : ''}
                        <div class="applicant-name">${escapeHtml(submission.fullname || 'N/A')}</div>
                        <div class="applicant-email">${escapeHtml(submission.email || 'N/A')}</div>
                    </td>
                    <td data-label="Contact">${escapeHtml(submission.phone || 'N/A')}</td>
                    <td data-label="Expertise">${formatExpertise(submission.expertise)}</td>
                    <td data-label="Applied Role">${escapeHtml(submission.applied_for_role || 'N/A')}</td>
                    <td data-label="City">${escapeHtml(submission.city || 'N/A')}</td>
                    <td data-label="Worklink">
                        ${renderWorklink(submission.worklink, index < 5)}
                    </td>
                    <td data-label="Status">
                        <select class="status-select" data-id="${submission.uniqueId}" onchange="updateStatus('${submission.uniqueId}', this.value)">
                            <option value="pending" ${submission.status === 'pending' ? 'selected' : ''}>Pending</option>
                            <option value="good fit" ${submission.status === 'good fit' ? 'selected' : ''}>Good Fit</option>
                            <option value="best fit" ${submission.status === 'best fit' ? 'selected' : ''}>Best Fit</option>
                            <option value="not fit" ${submission.status === 'not fit' ? 'selected' : ''}>Not Fit</option>
                        </select>
                    </td>
                    <td data-label="Actions">
                        <div class="table-actions">
                            <button class="button-primary button-small" onclick="editSubmission('${submission.uniqueId}')">Edit Phases</button>
                        </div>
                    </td>
                `;

                tbody.appendChild(tr);
            });
        }

        // Render worklink (iframe for top 5, links for others)
        function renderWorklink(worklink, isTop5) {
            if (!worklink || (Array.isArray(worklink) && worklink.length === 0)) {
                return '<span class="worklink-none">No worklink provided</span>';
            }

            const links = Array.isArray(worklink) ? worklink : [worklink];
            const displayLinks = links.slice(0, 5);

            if (displayLinks.length === 0) {
                return '<span class="worklink-none">No worklink provided</span>';
            }

            let moreLinksIndicatorHtml = '';
            if (links.length > 5) {
                moreLinksIndicatorHtml = `<div class="worklink-more-indicator">+${links.length - 5} more</div>`;
            }

            if (isTop5 && displayLinks[0]) {
                const primaryLink = displayLinks[0];
                let otherLinksIconsHtml = '';

                if (displayLinks.length > 1) {
                    for (let i = 1; i < displayLinks.length; i++) {
                        otherLinksIconsHtml += getLinkIconHtml(displayLinks[i], i);
                    }
                }

                return `
                    <div class="worklink-cell worklink-preview-mode">
                        <div class="worklink-preview-container">
                            <iframe src="${sanitizeUrl(primaryLink)}" class="worklink-iframe" sandbox="allow-scripts allow-same-origin" loading="lazy"
                                onload="this.style.opacity='1'; this.parentElement.querySelector('.iframe-loader').style.display='none';"
                                onerror="this.parentElement.innerHTML = '<div class=\\'iframe-error\\'>Preview failed to load. <a href=\\'${sanitizeUrl(primaryLink)}\\' target=\\'_blank\\'>Open directly</a></div>';"
                                style="opacity:0; transition: opacity 0.3s;"></iframe>
                            <div class="iframe-loader">Loading preview...</div>
                        </div>
                        <div class="worklink-details">
                            <a href="${sanitizeUrl(primaryLink)}" target="_blank" class="worklink-primary-link" title="${escapeHtml(primaryLink)}">${getShortLink(primaryLink)}</a>
                            ${otherLinksIconsHtml ? `<div class="worklink-additional-icons">${otherLinksIconsHtml}</div>` : ''}
                            ${moreLinksIndicatorHtml}
                        </div>
                    </div>
                `;
            } else {
                let allLinksIconsHtml = '';
                for (let i = 0; i < displayLinks.length; i++) {
                    allLinksIconsHtml += getLinkIconHtml(displayLinks[i], i);
                }

                return `
                    <div class="worklink-cell worklink-icons-only-mode">
                        <div class="worklink-additional-icons">
                            ${allLinksIconsHtml}
                        </div>
                        ${moreLinksIndicatorHtml}
                    </div>
                `;
            }
        }

        // Get icon HTML for a link based on its type
        function getLinkIconHtml(link, index) {
            if (!link) return '';

            let iconClass = 'fa-link';
            let backgroundColor = '#4a5568';
            let title = 'Link';

            const sanitizedLink = sanitizeUrl(link); // Use sanitized link for checks and href

            if (sanitizedLink.includes('github.com')) {
                iconClass = 'fa-github';
                backgroundColor = '#24292e';
                title = 'GitHub';
            } else if (sanitizedLink.includes('linkedin.com')) {
                iconClass = 'fa-linkedin';
                backgroundColor = '#0077B5';
                title = 'LinkedIn';
            } else if (sanitizedLink.includes('behance.net')) {
                iconClass = 'fa-behance';
                backgroundColor = '#1769ff';
                title = 'Behance';
            } else if (sanitizedLink.includes('dribbble.com')) {
                iconClass = 'fa-dribbble';
                backgroundColor = '#ea4c89';
                title = 'Dribbble';
            } else if (sanitizedLink.includes('medium.com')) {
                iconClass = 'fa-medium';
                backgroundColor = '#00ab6c';
                title = 'Medium';
            } else if (sanitizedLink.includes('codepen.io')) {
                iconClass = 'fa-codepen';
                backgroundColor = '#000000';
                title = 'CodePen';
            } else if (sanitizedLink.includes('gitlab.com')) {
                iconClass = 'fa-gitlab';
                backgroundColor = '#fc6d26';
                title = 'GitLab';
            } else if (sanitizedLink.includes('youtube.com') || sanitizedLink.includes('youtu.be')) {
                iconClass = 'fa-youtube';
                backgroundColor = '#ff0000';
                title = 'YouTube';
            } else if (sanitizedLink.includes('portfolio') || sanitizedLink.endsWith('.io') || sanitizedLink.includes('vercel.app') || sanitizedLink.includes('netlify.app')) {
                iconClass = 'fa-briefcase';
                backgroundColor = '#805ad5';
                title = 'Portfolio/Project';
            }

            return `
                <a href="${sanitizedLink}"
                   target="_blank"
                   class="worklink-icon-item"
                   style="background-color: ${backgroundColor};"
                   title="${escapeHtml(title)}: ${getShortLink(link)}">
                    <i class="fas ${iconClass}"></i>
                </a>
            `;
        }

        // Get shortened version of the link for display
        function getShortLink(link) {
            try {
                const url = new URL(link);
                let domain = url.hostname;

                // Remove www. prefix if present
                domain = domain.replace(/^www\./, '');

                // Truncate if too long
                if (domain.length > 25) {
                    domain = domain.substring(0, 22) + '...';
                }

                return domain;
            } catch (e) {
                // If invalid URL, return truncated original
                if (link.length > 30) {
                    return link.substring(0, 27) + '...';
                }
                return link;
            }
        }

        // Format expertise array
        function formatExpertise(expertise) {
            if (!expertise) return '';

            // If it's already a string, return it
            if (typeof expertise === 'string') return escapeHtml(expertise);

            // If it's an array, take only the top 3 and join with commas
            if (Array.isArray(expertise)) {
                const top3 = expertise.slice(0, 3);
                const formatted = top3.map(item => escapeHtml(item)).join(', ');

                // Add "+X more" if there are more than 3 expertise items
                if (expertise.length > 3) {
                    return formatted + ` <span class="more-items">+${expertise.length - 3} more</span>`;
                }

                return formatted;
            }

            return '';
        }

        // Format expertise for the modal display (limit to top 3)
        function formatModalExpertise(expertise) {
            if (!expertise) return '';
            if (typeof expertise === 'string') return escapeHtml(expertise);
            if (Array.isArray(expertise)) {
                let html = '<div class="src-submissions__modal-expertise">';
                expertise.forEach(item => {
                    html += `<span class="src-submissions__modal-expertise-badge">${escapeHtml(item)}</span>`;
                });
                html += '</div>';
                return html;
            }
            return '';
        }

        // Sanitize URL
        function sanitizeUrl(url) {
            try {
                // Try to create a URL object
                const urlObj = new URL(url);
                return urlObj.toString();
            } catch (e) {
                // If it's not a valid URL, try to add https://
                try {
                    if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        const httpsUrl = new URL('https://' + url);
                        return httpsUrl.toString();
                    }
                } catch (e2) {
                    // If still invalid, return # to prevent XSS
                    return '#';
                }
                return '#';
            }
        }

        // Display pagination
        function displayPagination(data) {
            const { currentPage: page, totalPages, totalSubmissions } = data;
            const paginationContainer = document.getElementById('paginationContainer');
            const paginationInfoEl = document.getElementById('paginationInfo');
            const paginationControlsEl = document.getElementById('paginationControls');

            if (!paginationContainer || !paginationInfoEl || !paginationControlsEl) {
                console.error('Pagination elements not found');
                return;
            }

            if (totalPages <= 1 && totalSubmissions > 0) { // Hide only if 1 page, but show if 0 submissions (handled by showEmpty)
                paginationContainer.style.display = 'none';
                return;
            }
            if (totalSubmissions === 0) { // if no submissions, ensure it's hidden.
                paginationContainer.style.display = 'none';
                return;
            }
            paginationContainer.style.display = 'flex';

            const itemsPerPage = 20; // Assuming 20 items per page from loadSubmissions
            const startItem = totalSubmissions > 0 ? ((page - 1) * itemsPerPage) + 1 : 0;
            const endItem = Math.min(page * itemsPerPage, totalSubmissions);
            paginationInfoEl.textContent =
                `Showing ${startItem}-${endItem} of ${totalSubmissions} submissions`;

            let paginationHTML = '';

            // Previous button
            paginationHTML += `
                <button class="pagination-button" onclick="goToPage(${page - 1})" ${page <= 1 ? 'disabled' : ''}>
                    &laquo; Previous
                </button>
            `;

            // Page numbers
            const maxPagesToShow = 5; // Max number of direct page links to show
            let startPage, endPage;

            if (totalPages <= maxPagesToShow) {
                startPage = 1;
                endPage = totalPages;
            } else {
                const maxPagesBeforeCurrent = Math.floor((maxPagesToShow - 1) / 2);
                const maxPagesAfterCurrent = Math.ceil((maxPagesToShow - 1) / 2);
                if (page <= maxPagesBeforeCurrent) {
                    startPage = 1;
                    endPage = maxPagesToShow;
                } else if (page + maxPagesAfterCurrent >= totalPages) {
                    startPage = totalPages - maxPagesToShow + 1;
                    endPage = totalPages;
                } else {
                    startPage = page - maxPagesBeforeCurrent;
                    endPage = page + maxPagesAfterCurrent;
                }
            }

            if (startPage > 1) {
                paginationHTML += `<button class="pagination-button" onclick="goToPage(1)">1</button>`;
                if (startPage > 2) {
                    paginationHTML += `<span class="pagination-ellipsis">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <button class="pagination-button ${i === page ? 'active' : ''}" onclick="goToPage(${i})">
                        ${i}
                    </button>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHTML += `<span class="pagination-ellipsis">...</span>`;
                }
                paginationHTML += `<button class="pagination-button" onclick="goToPage(${totalPages})">${totalPages}</button>`;
            }

            // Next button
            paginationHTML += `
                <button class="pagination-button" onclick="goToPage(${page + 1})" ${page >= totalPages ? 'disabled' : ''}>
                    Next &raquo;
                </button>
            `;

            paginationControlsEl.innerHTML = paginationHTML;
        }

        // Go to specific page
        function goToPage(page) {
            currentPage = page;
            loadSubmissions();
        }

        // Show/hide states
        function showLoading() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('submissionsTable').style.display = 'none';
            document.getElementById('paginationContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
        }

        function showEmpty() {
            hideLoading();
            document.getElementById('emptyState').style.display = 'block';
            document.getElementById('submissionsTable').style.display = 'none';
            document.getElementById('paginationContainer').style.display = 'none';
        }

        function hideEmpty() {
            document.getElementById('emptyState').style.display = 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            hideLoading();
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        // Edit submission - open modal
        function editSubmission(uniqueId) {
            // Find submission by uniqueId
            const submission = submissions.find(sub => sub.uniqueId === uniqueId);
            if (!submission) {
                showError(`Submission with ID ${uniqueId} not found`);
                return;
            }

            currentSubmission = submission;

            // Populate modal with submission data
            document.getElementById('modalFullname').textContent = submission.fullname || '';
            document.getElementById('modalEmail').textContent = submission.email || '';
            document.getElementById('modalPhone').textContent = submission.phone || '';
            document.getElementById('modalExpertise').innerHTML = formatModalExpertise(submission.expertise);
            document.getElementById('modalAppliedRole').textContent = submission.applied_for_role || '';
            document.getElementById('modalCity').textContent = submission.city || '';
            document.getElementById('modalThoughts').textContent = submission.thoughts || '';

            // Populate worklinks (show all)
            const worklinkElement = document.getElementById('modalWorklink');
            worklinkElement.innerHTML = '';

            if (submission.worklink && submission.worklink.length > 0) {
                const worklinks = Array.isArray(submission.worklink) ? submission.worklink : [submission.worklink];
                const linksContainer = document.createElement('div');
                linksContainer.className = 'src-submissions__modal-worklinks';
                worklinks.forEach(link => {
                    if (link) {
                        const linkItem = document.createElement('div');
                        linkItem.className = 'src-submissions__modal-worklink-item';

                        // Create icon based on link type
                        const iconSpan = document.createElement('span');
                        iconSpan.className = 'src-submissions__modal-worklink-icon';

                        if (link.includes('github.com')) {
                            iconSpan.innerHTML = '<i class="fab fa-github"></i>';
                            iconSpan.style.backgroundColor = '#24292e';
                        } else if (link.includes('linkedin.com')) {
                            iconSpan.innerHTML = '<i class="fab fa-linkedin"></i>';
                            iconSpan.style.backgroundColor = '#0077B5';
                        } else if (link.includes('behance.net')) {
                            iconSpan.innerHTML = '<i class="fab fa-behance"></i>';
                            iconSpan.style.backgroundColor = '#1769ff';
                        } else if (link.includes('dribbble.com')) {
                            iconSpan.innerHTML = '<i class="fab fa-dribbble"></i>';
                            iconSpan.style.backgroundColor = '#ea4c89';
                        } else if (link.includes('medium.com')) {
                            iconSpan.innerHTML = '<i class="fab fa-medium"></i>';
                            iconSpan.style.backgroundColor = '#00ab6c';
                        } else if (link.includes('codepen.io')) {
                            iconSpan.innerHTML = '<i class="fab fa-codepen"></i>';
                            iconSpan.style.backgroundColor = '#000000';
                        } else if (link.includes('gitlab.com')) {
                            iconSpan.innerHTML = '<i class="fab fa-gitlab"></i>';
                            iconSpan.style.backgroundColor = '#fc6d26';
                        } else if (link.includes('youtube.com') || link.includes('youtu.be')) {
                            iconSpan.innerHTML = '<i class="fab fa-youtube"></i>';
                            iconSpan.style.backgroundColor = '#ff0000';
                        } else {
                            iconSpan.innerHTML = '<i class="fas fa-link"></i>';
                            iconSpan.style.backgroundColor = '#4a5568';
                        }

                        // Create link element
                        const linkElement = document.createElement('a');
                        linkElement.href = sanitizeUrl(link);
                        linkElement.textContent = link;
                        linkElement.target = '_blank';

                        // Assemble
                        linkItem.appendChild(iconSpan);
                        linkItem.appendChild(linkElement);
                        linksContainer.appendChild(linkItem);
                    }
                });
                worklinkElement.appendChild(linksContainer);
            } else {
                worklinkElement.textContent = 'No worklink provided';
            }

            // Set phase dropdown values
            document.getElementById('modalPhase1').value = submission.phases?.phase1?.status || 'pending';
            document.getElementById('modalPhase2').value = submission.phases?.phase2?.status || 'pending';

            // Show modal
            document.getElementById('editModal').classList.add('active');
        }

        // Close edit modal
        function closeEditModal() {
            document.getElementById('editModal').classList.remove('active');
            currentSubmission = null;
        }

        // Save changes from modal
        async function saveChanges() {
            if (!currentSubmission) {
                showError('No submission selected for editing');
                return;
            }

            try {
                // Get values from form
                const phase1Status = document.getElementById('modalPhase1').value;
                const phase2Status = document.getElementById('modalPhase2').value;

                // Construct update data
                const updateData = {
                    phases: {
                        phase1: { status: phase1Status },
                        phase2: { status: phase2Status }
                    }
                };

                // Send update request
                const response = await axios.patch(`/src/${currentSubmission.uniqueId}/status-phases`, updateData);

                if (response.data.success) {
                    // Update in UI
                    // First find the submission in the global array
                    const submissionIndex = submissions.findIndex(sub => sub.uniqueId === currentSubmission.uniqueId);
                    if (submissionIndex >= 0) {
                        // Update the submission in the array
                        submissions[submissionIndex].phases = {
                            phase1: { status: phase1Status },
                            phase2: { status: phase2Status }
                        };
                    }

                    // Refresh the table to show updated data
                    displaySubmissions(submissions);

                    // Close modal
                    closeEditModal();
                } else {
                    showError('Failed to update: ' + response.data.message);
                }
            } catch (error) {
                console.error('Error saving changes:', error);
                showError('Error saving changes: ' + (error.response?.data?.message || error.message));
            }
        }

        // Update status (outside modal)
        async function updateStatus(uniqueId, status) {
            try {
                // Construct update data
                const updateData = { status };

                // Send update request
                const response = await axios.patch(`/src/${uniqueId}/status-phases`, updateData);

                if (response.data.success) {
                    // Update in UI
                    const submissionIndex = submissions.findIndex(sub => sub.uniqueId === uniqueId);
                    if (submissionIndex >= 0) {
                        submissions[submissionIndex].status = status;
                    }
                } else {
                    showError('Failed to update status: ' + response.data.message);
                    // Reset the select box to previous value
                    const select = document.querySelector(`select[data-id="${uniqueId}"]`);
                    if (select) {
                        const submission = submissions.find(sub => sub.uniqueId === uniqueId);
                        if (submission) {
                            select.value = submission.status;
                        }
                    }
                }
            } catch (error) {
                console.error('Error updating status:', error);
                showError('Error updating status: ' + (error.response?.data?.message || error.message));

                // Reset the select box to previous value on error
                const select = document.querySelector(`select[data-id="${uniqueId}"]`);
                if (select) {
                    const submission = submissions.find(sub => sub.uniqueId === uniqueId);
                    if (submission) {
                        select.value = submission.status;
                    }
                }
            }
        }
    </script>
</body>

</html>