<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Success Payment</title>
    <link rel="apple-touch-icon" sizes="57x57" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="apple-touch-icon" sizes="60x60" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="apple-touch-icon" sizes="72x72" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="apple-touch-icon" sizes="76x76" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="apple-touch-icon" sizes="114x114" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="apple-touch-icon" sizes="120x120" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="apple-touch-icon" sizes="144x144" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="apple-touch-icon" sizes="152x152" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="apple-touch-icon" sizes="180x180" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="icon" type="image/png" sizes="192x192"
        href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="icon" type="image/png" sizes="32x32"
        href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="icon" type="image/png" sizes="96x96"
        href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="icon" type="image/png" sizes="16x16"
        href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
    <link rel="stylesheet" href="/css/success.css">
    <script>
        !function (f, b, e, v, n, t, s) {
            if (f.fbq) return; n = f.fbq = function () {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
            n.queue = []; t = b.createElement(e); t.async = !0;
            t.src = v; s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '632198189232294');
        fbq('track', 'Purchase');
    </script>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-RT36RYDNZL"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-RT36RYDNZL');
    </script>
    <!-- Google Tag Manager -->
    <script>(function (w, d, s, l, i) {
            w[l] = w[l] || []; w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            }); var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-N9CBSHXN');</script>
    <!-- End Google Tag Manager -->
    <!-- Event snippet for DSA Purchase conversion page -->
    <script>
        gtag('event', 'conversion', {
            'send_to': 'AW-11552349473/NhJpCLmskdkaEKG6y4Qr',
            'currency': 'INR',
        });
    </script>
</head>
<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N9CBSHXN" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div class="success-message">
        <svg viewBox="0 0 76 76" class="success-message__icon icon-checkmark">
            <circle cx="38" cy="38" r="36" />
            <path fill="none" stroke="#FFFFFF" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"
                stroke-miterlimit="10" d="M17.7,40.9l10.9,10.9l28.7-28.7" />
        </svg>
        <h1 class="success-message__title">Payment Received</h1>
        <div class="success-message__content">
            <p>Your transaction has been completed. You will be redirected to the classroom
                in <span id="secondsCounter">5</span> seconds.</p>
        </div>
    </div>
    <script>
        var seconds = 5;
        var el = document.getElementById('secondsCounter');
        var timer = setInterval(function () {
            el.innerHTML = seconds;
            seconds--;
            if (seconds < 0) {
                clearInterval(timer);
                window.location.href = '/classroom/<%=request.user._id%>';
            }
        }, 1000);
    </script>
    <script>

        function PathLoader(el) {
                this.el = el;
                this.strokeLength = el.getTotalLength();

                // set dash offset to 0
                this.el.style.strokeDasharray =
                    this.el.style.strokeDashoffset = this.strokeLength;
            }

            PathLoader.prototype._draw = function (val) {
                this.el.style.strokeDashoffset = this.strokeLength * (1 - val);
            }

            PathLoader.prototype.setProgress = function (val, cb) {
                this._draw(val);
                if (cb && typeof cb === 'function') cb();
            }

            PathLoader.prototype.setProgressFn = function (fn) {
                if (typeof fn === 'function') fn(this);
            }

            var body = document.body,
                svg = document.querySelector('svg path');

            if (svg !== null) {
                svg = new PathLoader(svg);

                setTimeout(function () {
                    document.body.classList.add('active');
                    svg.setProgress(1);
                }, 200);
            }

            document.addEventListener('click', function () {

                if (document.body.classList.contains('active')) {
                    document.body.classList.remove('active');
                    svg.setProgress(0);
                    return;
                }
                document.body.classList.add('active');
                svg.setProgress(1);
            });
    </script>
</body>
</html>
