<%- include('./new-partials/header.ejs') %>
  <style>
    :root {
      --primary: #10a37f;
      --secondary: #075c48;
      --white: #fff;
      --primaryLight: #F8F8F8;
      --primaryLightNavStroke: #F8F8F8;
      --primaryDark: #222222;
      --primaryDarkNavStroke: #222222;
      --secondaryLight: #dbdbdb;
      --secondaryLightActive: #cbcbcb;
      --secondaryDark: #ececec;
      --secondaryDarkShadow: rgba(0, 0, 0, 0.16);
      --textColor: #7BBA81;
      --special: #d2af7a;
      --ternary: #c4c9d3;
      --error: #e74c3c;
      --maxPageWidth: 110rem;
      --discord: #7b40ff;
      --link: rgb(0, 71, 125)
    }

    /* Pincode Popup Styles */
    .popup.pincode-popup {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 99;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .popup.pincode-popup.active {
      opacity: 1;
    }

    .popup-content {
      background-color: white;
      border-radius: 8px;
      width: 90%;
      max-width: 400px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      overflow: hidden;
    }

    .popup-header {
      padding: 15px 20px;
      background-color: var(--primary);
      color: white;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .popup-header h3 {
      margin: 0;
      font-size: 18px;
    }

    .close-popup {
      font-size: 24px;
      cursor: pointer;
      color: white;
    }

    .popup-body {
      padding: 20px;
    }

    .popup-body p {
      margin-bottom: 15px;
      font-size: 14px;
      color: #555;
    }

    .input-field {
      margin-bottom: 20px;
    }

    .input-field input {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
      color: #333;
    }

    .input-field input:focus {
      border-color: var(--primary);
      outline: none;
    }

    .input-field small {
      display: block;
      margin-top: 5px;
      color: var(--error);
      font-size: 12px;
    }

    .submit-pincode-btn {
      width: 100%;
      padding: 12px;
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s;
    }

    .submit-pincode-btn:hover {
      background-color: var(--secondary);
    }

    * {
      margin: 0%;
      padding: 0%;
      box-sizing: border-box;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      text-rendering: optimizeSpeed;
    }

    *:focus {
      outline: none;
    }

    *::selection {
      color: var(--primaryLight);
      background: var(--primaryDark);
    }

    html,
    body {
      height: 100%;
      width: 100%;
      background-color: var(--secondaryDark);
    }

    body {
      overflow: overlay;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: var(--background-color);
    }

    html ::-webkit-scrollbar,
    body ::-webkit-scrollbar {
      width: 5px;
    }

    html ::-webkit-scrollbar-thumb,
    body ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.4392156863);
      border-radius: 3px;
    }


    #main {
      width: 100%;
      /* overflow: hidden; */
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    button {
      padding: 7px 13px;
      background-color: var(--primaryDark);
      border: none;
      outline: none;
      border-radius: 4px;
      cursor: pointer;
      color: var(--primaryLight);
      width: -moz-fit-content;
      width: fit-content;
      white-space: nowrap;
    }

    nav a {
      text-decoration: none;
      color: var(--primaryDark);
    }

    #main .page {
      width: 100%;
      padding: 7vh 6vw;
      max-width: 110rem;
    }

    .navCursor {
      transform-origin: center;
      height: 3.5rem;
      width: 3.5rem;
      background-color: var(--white);
      background-blend-mode: overlay;
      border-radius: 50%;
      position: fixed;
      transform: translate(-50%, -50%) scale(0);
      top: 0;
      left: 0;
      mix-blend-mode: exclusion;
      transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
      z-index: 10895;
      pointer-events: none;
    }




    #main #quote .right {
      max-width: 35rem;
      width: 45vw;
    }



    #preLoder {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #d8d8d8;
      position: fixed;
      top: 0;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
      z-index: 565656;
    }

    #preLoder .center {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 1.5rem;
    }

    #preLoder .center img {
      max-height: 7rem;
    }
  </style>
  <link rel="stylesheet" href="/css/PaymentPortal.css">
  <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      style-src 'self' http://localhost:7000 https://sheryians.com https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline';
      script-src 'self' https://ajax.googleapis.com https://checkout.razorpay.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://connect.facebook.net 'unsafe-inline';
      img-src 'self' data: https://ik.imagekit.io https://rvs-order-summary-component.netlify.app/ https://www.facebook.com/;
      font-src 'self' https://cdn.jsdelivr.net https://sheryians.com http://localhost:7000 https://fonts.gstatic.com https://ik.imagekit.io;
      frame-src 'self' https://api.razorpay.com;
      connect-src 'self' https://lumberjack.razorpay.com https://lumberjack-metrics.razorpay.com https://lumberjack-cx.razorpay.com https://capig.madgicx.ai;
    ">

  <script>
    !function (f, b, e, v, n, t, s) {
      if (f.fbq) return; n = f.fbq = function () {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
      n.queue = []; t = b.createElement(e); t.async = !0;
      t.src = v; s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '632198189232294');
    fbq('track', 'AddToCart');
  </script>
  </head>

  <body>
    <main id="main">

      <div class="loadingspinner loading-indicator"></div>

      <!-- Pincode Popup for Tax Calculation -->
      <div class="popup pincode-popup">
        <div class="bg" onclick="closePincodePopup()"></div>
        <div class="popup-content">
          <div class="popup-header">
            <h3>Enter Your Postal Code</h3>
            <span class="close-popup" onclick="closePincodePopup()">&times;</span>
          </div>
          <div class="popup-body">
            <p>Please enter your postal code(pincode or zipcode) to proceed with the payment.</p>
            <div class="input-field">
              <input type="text" id="pincode" name="pincode" placeholder="Enter 6-digit Postal Code" maxlength="6">
              <small id="pincodeValidation"></small>
            </div>
            <button id="submit-pincode" class="submit-pincode-btn" onclick="validateAndSubmitPincode()">Continue to Payment</button>
          </div>
        </div>
      </div>

      <div id="page1" class="course-container">
        <div class="course-details">
          <h2 class="heading">Your course</h2>
          <div class="course-info">
            <a href="/courses/courses-details/<%= course.course_name %>">
              <div class="course-heading">
                <div class="course-poster">
                  <img src="<%= course.image %>" alt="">
                </div>
                <div>
                  <h3><%= course.course_name %></h3>
                  <p class="current-price">₹ <%=bounty%></p>
                </div>
              </div>
            </a>
            <div class="course-text">
              <a style="text-decoration: none;" href="/courses/courses-details/<%= course.course_name %>">
                <h3><%= course.course_name %></h3>
              </a>
              <p>
                <span class="current-price">₹ <%=bounty%></span>
              </p>
              <div class="coupon-section">
                <form action="/paymentPortal/<%=course._id%>/applyCoupon" method="post" autocomplete="off" class="applyCoupon">
                  <div>
                    <% if(typeof discount_coupon != 'undefined' && applyCouponDiscount > 0 && request.session.message === 'Coupon Applied' ){ %>
                      <div class="coupon-input">
                        <input style="user-select: none; color: var(--primary);" id="applyTxt" type="text" name="discount_coupon" placeholder="Enter coupon code" readonly value="<%=discount_coupon%>">
                        <i class="ri-check-line" style="color:var(--primary);"></i>
                      </div>
                    <% }else{ %>
                      <% if(request.session.message === 'Invalid Coupon Code'){ %>
                      <div style="border: 2px solid var(--error);" class="coupon-input">
                          <input  id="applyTxt"  type="text" value="<%=discount_coupon%>" name="discount_coupon" placeholder="Enter coupon code" required>
                          <i class="ri-close-line" style="color:var(--error);"></i>
                        </div>
                        <%}else{%>
                          <div class="coupon-input">
                            <input id="applyTxt"  type="text" name="discount_coupon" placeholder="Enter coupon code" required>
                          </div>
                        <%}%>

                    <% } %>
                    <button class="btn-dark" type="submit">
                      Apply Coupon
                    </button>
                  </div>

                </form>
              </div>
            </div>
          </div>
          <div class="payment-support">
            <div class="heading">
              <p>Payment & Support Info -</p>
            </div>
            <div class="content">
              <div class="support-item">
                <div class="check">
                  <div>
                    <i class="ri-check-line"></i>
                  </div>
                </div>
                <span>3-Days <br> Refund Policy</span>
              </div>
              <div class="support-item">
                <div class="check">
                  <div>
                    <i class="ri-check-line"></i>
                  </div>
                </div>
                <span>Contact Us <br> <a style="text-decoration: none;" href="mailto:<EMAIL>"><EMAIL></a></span>
              </div>
              <div class="support-item">
                <div class="check">
                  <div>
                    <i class="ri-check-line"></i>
                  </div>
                </div>
                <span>Get Course <br> Completion Certificate</span>
              </div>
            </div>
          </div>
        </div>
        <div class="payment-details">
          <h2 class="heading">Payment Details</h2>
          <div class="details">

              <%
                var discount = 0;
                if (typeof applyCouponDiscount != 'undefined' && applyCouponDiscount > 0) {
                  discount = applyCouponDiscount;
                }

                bounty = parseFloat(bounty); // Ensure bounty is a number

                // Calculate subtotal
                var subtotal = (bounty - discount) / 2.006; // 1 + 0.7 + (1 + 0.7) * 0.18 = 2.006

                // Calculate platform fee (70% of subtotal)
                var platformFee = subtotal * 0.7;

                // Calculate GST (18% of amount after discount)
                var gst = (subtotal + platformFee) * 0.18;

                // Calculate total amount
                var totalAmount = subtotal + platformFee + gst;
                %>
                <% if (discount > 0) { %>
                  <div class="detail-item">
                    <span>Original Price</span>
                    <span>₹<%= Math.round(bounty) %></span>
                  </div>
                  <div class="detail-item">
                    <span>Coupon Discount</span>
                    <span class="discount">- ₹<%= Math.round(discount) %></span>
                  </div>
                <% } %>
                <div class="detail-item">
                  <span>(Base Amount)</span>
                  <span>₹<%= Math.round(subtotal) %></span>
                </div>
                <div class="detail-item">
                  <span>(Platform Fees)<small>(Server, Streaming, Database, Bandwidth, etc.)</small></span>
                  <span>₹<%= Math.round(platformFee) %></span>
                </div>
                <div class="detail-item">
                  <span>(GST @18%)<small>To Govt. of India</small></span>
                  <span>₹<%= Math.round(gst) %></span>
                </div>
                <div class="checkout">
                  <div class="total-amount">
                    <span>Total Amount</span>
                    <span>₹<%= Math.round(totalAmount) %></span>
                  </div>
                  <button id="rzp-button1" class="checkout-btn">
                    Proceed to checkout
                  </button>
                </div>
        </div>

        </div>
      </div>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>      <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
      <script>

// Function to open pincode popup
function openPincodePopup() {
  document.querySelector('.pincode-popup').style.display = 'flex';
  document.querySelector('.pincode-popup').classList.add('active');
  document.querySelector('.pincode-popup').style.opacity = '1';
  document.getElementById('pincode').focus();
}

// Function to close pincode popup
function closePincodePopup() {
  document.querySelector('.pincode-popup').classList.remove('active');
  document.querySelector('.pincode-popup').style.display = 'none';
  document.querySelector('.pincode-popup').style.opacity = '0';
}

// Function to validate pincode
function validateAndSubmitPincode() {
  const pincode = document.getElementById('pincode').value;
  const pincodeValidation = document.getElementById('pincodeValidation');

  // Validate pincode (6 digits)
  if(!(pincode.length >= 5 && pincode.length <= 6)) {
    pincodeValidation.textContent = 'Please enter a valid 6-digit pincode';
    return;
  }

  // Clear validation message
  pincodeValidation.textContent = '';

  // loader
  document.querySelector('#page1').style.filter = "blur(0.1rem)";
  document.querySelector('.loading-indicator').style.display = "block";


  // Update pincode via API
  fetch('/signup/update-pincode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ pincode }),
  })
  .then(response => response.json())
  .then(data => {
    // remove loader
    document.querySelector('#page1').style.filter = "blur(0)";
    document.querySelector('.loading-indicator').style.display = "none";

    if (data.message === 'success') {
      // Close popup
      closePincodePopup();
      initiatePayment();
    } else {
      pincodeValidation.textContent = data.reason || 'Error updating pincode';
    }
  })
  .catch(error => {
    console.error('Error updating pincode:', error);
    pincodeValidation.textContent = 'Error updating pincode. Please try again.';
  });
}

// Modified code with condition
document.querySelector('.checkout-btn').addEventListener('click', async () => {
  fetch("/tracker/lead/proceedToPayment", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      user_id: `<%= user._id %>`,
      course_id: `<%= course.course_id %>`,
    }),
  });

  fbq('track', 'InitiateCheckout');

  // Check if user has a pincode
  fetch('/signup/update-pincode', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.message !== 'success' || data.pincode === null) {
      // User doesn't have a pincode, show popup
      openPincodePopup();
    }
  })
  .catch(error => {
    console.error('Error checking pincode:', error);
    // If there's an error, show the popup to be safe
    openPincodePopup();
  });
});

      </script>
      <% if(typeof discount_coupon !='undefined' ){ %>
        <script>
          var discountc = "<%=discount_coupon%>";
        </script>
        <% }else{ %>
          <script>
            var discountc = "";
          </script>
          <% } %>
            <script>
              const userId = '<%= user._id %>';
              const user_name = '<%= user.name %>';
              const user_email = '<%= user.email %>';
              const user_phone = '<%= user.phoneNumber %>';
              const courseId = '<%= course._id %>';
              const course_name = '<%= course.course_name %>';
            </script>
            <script defer>
              let mobile = false;
              let notMobile = false
              let tablet = false;

              function isTablet() {
                if (tablet) {
                  return true;
                }
                if (/iPad|Android/.test(navigator.userAgent) && !window.MSStream) {
                  tablet = true;
                }
                return tablet;
              }
              // Check if the current device is a mobile phone

              function isMobile() {
                if (mobile) {
                  return true;
                }
                if (notMobile) {
                  return false
                }
                if (
                  /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
                    navigator.userAgent
                  ) && window.innerWidth < 600
                ) {
                  mobile = true;
                  return true;
                } else {
                  notMobile = false
                  return false
                }
              }



              // Check if the current device is a desktop
              function isDesktop() {
                return !isTablet() && !isMobile();
              }


              function mapRange(value, inMin, inMax, outMin, outMax) {
                return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
              }

              function clamp(value, min, max) {
                return Math.min(Math.max(value, min), max);
              }
            </script>


            <script>
              function closePopup() {
                document.querySelectorAll('.popup').forEach(pop => {
                  pop.classList.remove('active')
                  pop.style.display = 'none'
                  pop.style.opacity = '0'
                })

                try {
                  if (lenis)
                    lenis.start()
                } catch (er) {

                }
              }

              function closeRotatePopup() {
                document.querySelector('.rotatePopup').classList.remove('active')
                document.querySelector('.rotatePopup').style.display = 'none'
                document.querySelector('.rotatePopup').style.opacity = '0'
              }

              function openRotatePopup() {
                document.querySelector('.rotatePopup').style.display = 'flex'
                document.querySelector('.rotatePopup').classList.add('active')
                document.querySelector('.rotatePopup').style.opacity = '1'
              }

              function openDiscordPopup() {
                document.querySelector('.discordJoinPopup').style.display = 'flex'
                document.querySelector('.discordJoinPopup').classList.add('active')
                document.querySelector('.discordJoinPopup').style.opacity = '1'
                try {
                  if (lenis)
                    lenis.stop()
                } catch (err) {

                }
              }

              function closeDiscordPopup() {
                document.querySelector('.discordJoinPopup').classList.remove('active')
                document.querySelector('.discordJoinPopup').style.display = 'none'
                document.querySelector('.discordJoinPopup').style.opacity = '0'

                try {
                  if (lenis)
                    lenis.start()
                } catch (err) {

                }
              }

              function onOrientationChange(callbacks) {
                if (!isMobileDevice()) {
                  return
                }
                if (typeof callbacks.landscape !== 'function' || typeof callbacks.portrait !== 'function') {
                  console.log('Invalid input. Please provide an object with landscape and portrait callback functions.');
                  return;
                }

                function handleOrientationChange() {
                  const orientation = window.orientation;
                  if (orientation === 0 || orientation === 180) {
                    // Portrait orientation
                    callbacks.portrait();
                  } else {
                    // Landscape orientation
                    callbacks.landscape();
                  }
                }

                // window.addEventListener('resize', handleOrientationChange);

                // Initial callback with current orientation
                handleOrientationChange();


              }

              function isMobileDevice() {
                return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
              }


              // Example usage:
              const orientationCallbacks = {
                landscape: openRotatePopup,
                portrait: closeRotatePopup,
              };

              const cleanupCallback = onOrientationChange(orientationCallbacks);


              var isNotificationVisible = false
              const popup = document.querySelector('.notifications');
              const navMainRight = document.querySelector('.nav-right')


              function insertAfter(referenceNode, newNode) {
                referenceNode.parentElement.insertBefore(newNode, referenceNode.nextSibling);
              }

              function defaultDp(event) {
                event.setAttribute("src", "/images/Sheryians Logo.png")
              }

              function setTimeDifference(current, previous, elem) {
                elem.textContent = timeDifference(current, previous)
              }

              function timeDifference(current, previous) {
                const milliSecondsPerMinute = 60 * 1000;
                const milliSecondsPerHour = milliSecondsPerMinute * 60;
                const milliSecondsPerDay = milliSecondsPerHour * 24;
                const milliSecondsPerMonth = milliSecondsPerDay * 30;
                const milliSecondsPerYear = milliSecondsPerDay * 365;

                const elapsed = current - previous;

                if (elapsed < milliSecondsPerMinute) {
                  return Math.round(elapsed / 1000) + ' seconds ago';
                } else if (elapsed < milliSecondsPerHour) {
                  return Math.round(elapsed / milliSecondsPerMinute) + ' minutes ago';
                } else if (elapsed < milliSecondsPerDay) {
                  return Math.round(elapsed / milliSecondsPerHour) + ' hours ago';
                } else if (elapsed < milliSecondsPerMonth) {
                  return Math.round(elapsed / milliSecondsPerDay) + ' days ago';
                } else if (elapsed < milliSecondsPerYear) {
                  return Math.round(elapsed / milliSecondsPerMonth) + ' months ago';
                } else {
                  return Math.round(elapsed / milliSecondsPerYear) + ' years ago';
                }
              }

              async function openNotification(link, id) {

                try {
                  axios.post("/classroom/notifications/update", {
                    notificationId: id
                  })
                  window.location.href = link
                } catch (err) {
                  console.log(err)
                }

              }


              function createNotification(seen, id, type, href, someone, text, userProfile, lectureThumbnail, createdAt) {
                const aElement = document.createElement('div');
                // aElement.setAttribute('href', href);
                aElement.setAttribute("onclick", `openNotification("${href}","${id}")`)
                aElement.classList.add('notification', type);
                if (seen) aElement.classList.add("seen");
                if (!seen) aElement.classList.add("unseen");


                const userDiv = document.createElement('div');
                userDiv.classList.add('user');
                const userImg = document.createElement('img');
                userImg.setAttribute('src', userProfile);
                userImg.setAttribute('alt', '');
                userDiv.appendChild(userImg);

                const dataDiv = document.createElement('div');
                dataDiv.classList.add('data');
                if (type == 'like') {
                  const likeP = document.createElement('p');
                  likeP.classList.add('like');
                  likeP.innerHTML = `👍 <span class="fromUser">${someone}</span> liked : `;
                  dataDiv.appendChild(likeP);
                } else if (type == 'reply') {
                  const replyP = document.createElement('p');
                  replyP.classList.add('reply');
                  replyP.innerHTML = `<span class="fromUser"> ${someone}</span> replied: `;
                  dataDiv.appendChild(replyP);
                }
                const textP = document.createElement('p');
                textP.classList.add('text');
                textP.innerHTML = `<span>"</span><span>${text}</span><span>"</span>`;
                const small = document.createElement('small');
                small.innerText = timeDifference(Date.now(), new Date(createdAt));
                dataDiv.appendChild(textP);
                dataDiv.appendChild(small);

                const lectureDiv = document.createElement('div');
                lectureDiv.classList.add('lecture');
                const lectureImg = document.createElement('img');
                lectureImg.setAttribute('src', lectureThumbnail);
                lectureImg.setAttribute('alt', '');
                lectureDiv.appendChild(lectureImg);

                aElement.appendChild(userDiv);
                aElement.appendChild(dataDiv);
                aElement.appendChild(lectureDiv);

                return aElement;
              }

              function createNotificationUpdate(seen, id, type, href, text, lectureThumbnail, createdAt) {
                const aElement = document.createElement('div');
                // aElement.setAttribute('href', href);
                aElement.setAttribute("onclick", `openNotification("${href}","${id}")`)
                aElement.classList.add('notification', type);
                if (seen) aElement.classList.add("seen");
                if (!seen) aElement.classList.add("unseen");



                const dataDiv = document.createElement('div');
                dataDiv.classList.add('data');

                const textP = document.createElement('p');
                textP.classList.add('text');
                textP.innerHTML = `<span>"</span><span>${text}</span><span>"</span>`;
                const small = document.createElement('small');
                small.innerText = timeDifference(Date.now(), new Date(createdAt));
                dataDiv.appendChild(textP);
                dataDiv.appendChild(small);

                const lectureDiv = document.createElement('div');
                lectureDiv.classList.add('lecture');
                const lectureImg = document.createElement('img');
                lectureImg.setAttribute('src', lectureThumbnail);
                lectureImg.setAttribute('alt', '');
                lectureDiv.appendChild(lectureImg);

                aElement.appendChild(dataDiv);
                aElement.appendChild(lectureDiv);

                return aElement;
              }


              /*

              use replyNotification use as first argument to use as reply comment notification
              and likeNotification for like comment notification

              */

              let page = 1; // Initialize the page number for pagination

              const SCROLL_THRESHOLD = 5;
              const notificationBtn = document.querySelector('.notificationButton');
              const notificationsContainer = document.querySelector('.notifications');

              async function loadMoreNotification(event) {
                const {
                  scrollTop,
                  clientHeight,
                  scrollHeight
                } = event.target;
                const isNearBottom = scrollTop + clientHeight >= scrollHeight - SCROLL_THRESHOLD;

                if (isNearBottom) {
                  page++;

                  try {
                    const notifications = await fetchNotifications(page);


                    displayNotifications(notifications.notifications);
                  } catch (err) {
                    console.error("Error loading more notifications:", err);
                  }
                }
              }

              // Function to fetch notifications from the API
              async function fetchNotifications(page) {
                console.log(page)
                const response = await fetch(`/classroom/notifications/${page}`);
                const data = await response.json();
                return data;
              }

              function displayNotifications(notifications) {
                notifications.forEach((notification) => {
                  if (notification.type == "update") {
                    var notificationElement = createNotificationUpdate(
                      notification.seen,
                      notification._id,
                      notification.type,
                      notification.link,
                      notification.message,
                      notification.thumbnail_url,
                      notification.createdAt
                    );
                  } else {
                    var notificationElement = createNotification(
                      notification.seen,
                      notification._id,
                      notification.type,
                      notification.link,
                      notification.sender.name,
                      notification.message,
                      notification.sender_dp,
                      notification.thumbnail_url,
                      notification.createdAt
                    );
                  }
                  notificationsContainer.appendChild(notificationElement);
                });
              }

              // Function to implement "load more" on scroll

              // document.querySelector('.notifications').appendChild(createNotification(
              //   'replyNotification',
              //   '/courses',
              //   'Ankur',
              //   'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quas et officia nobis, impedit accusantium deleniti eveniet ab eum aliquam commodi mollitia quo adipisci perspiciatis ipsa, sequi, voluptatibus sapiente quaerat fugiat?',
              //   'https://media.istockphoto.com/id/1345121223/photo/young-beautiful-woman-stock-photo.webp?b=1&s=170667a&w=0&k=20&c=H7SuMjLiOzm78vNDb_cGGke5Qsp_94pFugV2Adzs9Eo=',
              //   'https://ik.imagekit.io/sheryians/courses_gif/Mastering_the_Art_of_Resume-RESUMECOURSEPOSTER_NK6hP8IP8.jpg',
              // ))

              // To stop listening to orientation changes, call the cleanup function:
              // cleanupCallback();

              let userPincodeStatus = <%= pincodePopup %>;
              if (userPincodeStatus) {
                openPincodePopup();
              }
            </script>
            <script src="/js/paymentPortal.js"></script>
            <%- include('./new-partials/footer.ejs') %>