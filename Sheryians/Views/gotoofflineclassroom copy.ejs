<%- include('./new-partials/header.ejs') %>
<style>
  :root {
    --primary: #10a37f;
    --secondary: #075c48;
    --white: #fff;
    --primaryLight: #F8F8F8;
    --primaryLightNavStroke: #F8F8F8;
    --primaryDark: #222222;
    --primaryDarkNavStroke: #222222;
    --secondaryLight: #dbdbdb;
    --secondaryLightActive: #cbcbcb;
    --secondaryDark: #ececec;
    --secondaryDarkShadow: rgba(0, 0, 0, 0.16);
    --textColor: #7BBA81;
    --special: #d2af7a;
    --ternary: #c4c9d3;
    --error: #e74c3c;
    --maxPageWidth: 110rem;
    --discord: #7b40ff;
    --link: rgb(0, 71, 125)
  }

  * {
    margin: 0%;
    padding: 0%;
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-rendering: optimizeSpeed;
  }

  *:focus {
    outline: none;
  }

  *::selection {
    color: var(--primaryLight);
    background: var(--primaryDark);
  }

  html,
  body {
    height: 100%;
    width: 100%;
    background-color: var(--secondaryDark);
  }

  body {
    overflow: overlay;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--background-color);
  }

  html ::-webkit-scrollbar,
  body ::-webkit-scrollbar {
    width: 5px;
  }

  html ::-webkit-scrollbar-thumb,
  body ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4392156863);
    border-radius: 3px;
  }


  #main {
    background-color: var(--secondaryDark);
    width: 100%;
    /* overflow: hidden; */
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  button {
    padding: 7px 13px;
    background-color: var(--primaryDark);
    border: none;
    outline: none;
    border-radius: 4px;
    cursor: pointer;
    color: var(--primaryLight);
    width: -moz-fit-content;
    width: fit-content;
    white-space: nowrap;
  }

  nav a {
    text-decoration: none;
    color: var(--primaryDark);
  }

  #main .page {
    width: 100%;
    padding: 7vh 6vw;
    /* max-width: 110rem; */
  }

  .navCursor {
    transform-origin: center;
    height: 3.5rem;
    width: 3.5rem;
    background-color: var(--white);
    background-blend-mode: overlay;
    border-radius: 50%;
    position: fixed;
    transform: translate(-50%, -50%) scale(0);
    top: 0;
    left: 0;
    mix-blend-mode: exclusion;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
    z-index: 10895;
    pointer-events: none;
  }




  #main #quote .right {
    max-width: 35rem;
    width: 45vw;
  }



  #preLoder {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #d8d8d8;
    position: fixed;
    top: 0;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
    z-index: 565656;
  }

  #preLoder .center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1.5rem;
  }

  #preLoder .center img {
    max-height: 7rem;
  }
</style>
<link rel="stylesheet" href="/css/headerpart1.css">
<link rel="stylesheet" href="/css/gotoclassroom.css">
<title><%= title %></title>

</head>

<body>

  <main id="main">


    <section class="page1 page">
      <div class="heroArea">
        <div class="notForMobilePopup">
          <div class="overlay">
            <div class="image">
              <img src="https://ik.imagekit.io/sheryians/IMG_6494_mJgm_Zq63.PNG?updatedAt=1723622042298" alt="">
            </div>
            <div class="content">
              <h2>Use a computer to complete this section</h2>
              <p>It is not designed to work on this device</p>
            </div>
            <div class="button">
              <button onclick="hideNotForMobilePopup()">Okay</button>
              <button class="popupNextLectureButton" onclick="nextLectureClickHandler()">Next Lecture</button>
            </div>
          </div>
        </div>
        <div class="loader" style="display: none; justify-content: center; align-items: center; height: 100%; width: 100%;">
          <img load-lazy="https://ik.imagekit.io/sheryians/Logo/shery-katana_w9qKsnkZk.gif" alt="">
          <style>
            .navCursor {
              display: none;
              mix-blend-mode: unset;
            }
          </style>
        </div>
        <div id="heroArea-wrap" style="flex-direction: column;">
          <div class="video">
          </div>
          <div class="metaData">
            <div class="title">
              <div class="text">
                <h2>Some Video Playing and It's title goes here.</h2>
              </div>
            </div>
            <div class="courseDetail">
              <div class="left" onclick="window.location.href='/courses/courses-details/<%=courseName%>'">
                <img load-lazy="<%= courseImage %>" alt="">
                <div class="text">
                  <h4><%=courseName%></h4>
                  <small>
                    <% for (let i = 0; i < course.techstacktag.length; i++) { %>
                      <% if (i === course.techstacktag.length - 1) { %>
                        <span>and <%= course.techstacktag[i] %>.</span>
                      <% } else if (i === course.techstacktag.length - 2) { %>
                        <span><%= course.techstacktag[i] %></span>
                      <% } else { %>
                        <span><%= course.techstacktag[i] %>,</span>
                      <% } %>
                    <% } %>
                  </small>
                </div>
              </div>

              <div class="right buttons">
                <div class="like">
                  <div role="button" class="likes">
                    <i class="ri-thumb-up-line"></i>
                    <i class="ri-thumb-up-fill active "></i>
                    <small class="count">
                      0
                    </small>
                  </div>
                  <div class="vr"></div>
                  <div class="dislikes">
                    <i class="ri-thumb-down-line"></i>
                    <i class="ri-thumb-down-fill active"></i>
                  </div>
                </div>



                <div role="button" onclick="openSharePopUp()" class="share">
                  <i class="ri-share-forward-line"></i>
                  Share
                </div>

                <div role="button" onclick="(()=>{
              showHIdeComment()
              // document.querySelector('#newComment').focus()
            })()" class="comment">
                  <i class="ri-question-answer-line"></i>
                  Comment
                </div>
                <% if(course){ %>
                <p class="button discord" onclick="openDoubtPopup()">
                  <span>Doubts</span> <i class="ri-discord-line"></i>
                </p>
                <% } %>

                <button id="next-btn" class="button btn btn-primary"> <span>Next</span> <i class="ri-skip-right-fill"></i> </button>

                <div title="Write Feedback" role="button" onclick="openFeedbackPopUp()" class="share poll"><i class="ri-chat-poll-line"></i></div>

              </div>
            </div>
          </div>
          <!-- <div class="links">
                        <p>Links mentioned in class</p>
                        <div class="link">
                            <small class="text">Some mentioned link : </small>
                            <small>
                                <a href="#">###########</a>
                            </small>
                        </div>
                    </div> -->
          <div class="description">
            <small>Description</small>
            <div id="descriptionTextWrapper">
              <div id="descriptionText">
                <div style="display: none;" id="links">
                </div>
                <p id="desc">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit. Velit, laborum cumque explicabo amet
                  voluptatem voluptate itaque quia tenetur nulla, inventore aut quasi consequatur nobis. Fugit
                  accusantium iusto molestiae a fuga?
                  Lorem ipsum dolor sit amet consectetur adipisicing elit. Velit, laborum cumque explicabo amet
                  voluptatem voluptate itaque quia tenetur nulla, inventore aut quasi consequatur nobis. Fugit
                  accusantium iusto molestiae a fuga?
                </p>

              </div>
            </div>
            <div role="button" class="showMore">
              <p>...more</p>
            </div>
          </div>
          <div onclick="showHIdeComment()" id="mobileViewCommentMask" class="comments" style="display: none;">
            <div>
              <p>comments <span style="font-size: 0.9em;">27</span></p>
            </div>
            <div class="comment">
              <div class="left">
                <img load-lazy="/images/Sheryians Logo.png" alt="">
              </div>
              <div class="right">
                <div class="metaData">
                  <p class="user">ankur</p>
                  <p class="time">three week ago</p>
                </div>
                <p class="text">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit. Debitis voluptates voluptatum amet et, atque
                  consectetur! Dolores eaque quae molestiae vel. Quaerat at a exercitationem, ducimus ullam optio dolorem
                  culpa assumenda!
                </p>

              </div>
            </div>
          </div>
          <div id="realComments" class="comments">
            <div class="stagger"></div>
            <div class="addComment">
              <div role="button" class="top">
                <p> 105 comments </p>
                <div onclick="closeComment()" class="arrow">
                  <i class="ri-close-line"></i>
                </div>
              </div>
              <div class="bottom">
                <div class="user">
                  <img load-lazy="<%=profilePicture%>" alt="">
                </div>
                <textarea id="newComment" name="newComment" rows="1" placeholder="Add Comment"></textarea>
              </div>
              <div class="actions">
                <div onclick="clearComment()" class="action cancle">cancel</div>
                <% if(typeof user != 'undefined'){ %>
                <div onclick="addComment('<%=user.name%>','<%=profilePicture%>','<%=user._id%>')" class="action addcomment ">comment</div>
                <% }else{ %>
                <div onclick="openPhonePopUp();" class="action addcomment ">comment</div>
                <% } %>
              </div>
            </div>
            <div class="allcomments">
              <div class="comment">
                <div class="left">
                  <img load-lazy="/images/Sheryians Logo.png" alt="">
                </div>
                <div class="right">
                  <div class="top">
                    <div class="metaData">
                      <p class="user">ankur</p>
                      <p class="time">three week ago</p>
                    </div>
                    <p class="text">
                      Lorem ipsum dolor sit amet consectetur, adipisicing elit. Quisquam eveniet soluta obcaecati
                      necessitatibus beatae a quaerat aliquid quam reiciendis assumenda. Assumenda enim mollitia perspiciatis
                      sunt maxime iusto? Labore, rerum iure?
                    </p>
                  </div>
                </div>
                <div class="delete">
                  <i class="ri-more-2-fill"></i>
                  <div class="options">
                    delete
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div id="heroAreaMCQ-wrap" style="display: none;">
          <iframe src="/classroom/getMCQDetails/" frameborder="0"></iframe>
        </div>
        <div id="heroAreaPDF-wrap" style="display: none;">
          <p >PDF Title</p>
          <iframe src="/classroom/getMCQDetails/" frameborder="0"></iframe>
          <div>
            <p>Download</p>
        </div>
        </div>
        <div id="heroAreaCode-wrap" style="display: none;">
          <p >PDF Title</p>
          <iframe src="/classroom/getCodeDetails/" frameborder="0"></iframe>
          <div>
            <p>Download</p>
        </div>
      </div>

      </div>
      <div class="verticalResizer">
        <div class="line"></div>
      </div>
      <div class="chapters">


        
        <div role="button" onclick="seeAllVideosInMobileView()" class="mobileViewTitle title" style="display: none;">
          <div class="stagger"></div>
          <i class="ri-menu-unfold-fill"></i>
          <div class="text">
            <h4>Next:&nbsp;<span class="lectureName"></span> </h4>
            <small> <span class="titleCourseName"> <%=courseName%> </span> &nbsp; <span class="currentNumber">1</span>/<span class="totalNumber"></span> </small>
          </div>
          <i class="ri-arrow-up-s-line"></i>
        </div>
        <div onclick="resetSize(this)" class="title">
          <i class="ri-box-3-line"></i>
          <span>All modules</span>
        </div>
       
        <div class="top-all-module">
          <div class="progressBar">
            <div class="progress">
              <p class="pointsPercentage"><%= totalPoints ? ((parseInt(currentPoints) / totalPoints) * 100).toFixed(2) : 0 %>% Completed</p>
              <h1><span class="current-points"><%=parseInt(currentPoints)%></span> / <span class="total-points"><%=totalPoints%></span></h1>
            </div>
            <div class="bar">
              <div class="meter" style="width: <%=progressPercentage%>%;"></div>
            </div>
          </div>
  <div class="status-assessments">
    <div class="status">
      <p><span>Assessments:</span> <h6 class="assessmentLecture"><%=AssesmentLecturesCompleted%></h6> / <h6><%=AssesmentLectures%></h6></p>
    </div>
    <div class="status">
      <p><span>Videos:</span> <h6 class="videoLectures"><%=videoLecturesCompleted%></h6> / <h6><%=videoLectures%></h6></p>
    </div>
  </div>
  
          </div>
        
        <div class="modules">
          
          
          <% for (const moduleWrapper of classroomContent) { %>
          <div class="moduleWrapper <%=moduleWrapper.comingSoonDate ? 'comingSoon' : '' %> accordian">
            <label for="<%= moduleWrapper._id %>" class="title">
              <h1 class="name">
                <%= moduleWrapper.title %>
              </h1>
              <%if(moduleWrapper.comingSoonDate){%>
              <p class="comingsoon"> <span>Unlocks on</span> <%=new Date(moduleWrapper.comingSoonDate).toLocaleDateString('en-US', { day: 'numeric', month: 'long' });%></p>
              <%}else{%>
              <input type="checkbox" id="<%= moduleWrapper._id %>" hidden>
              <button class="dropdownButton">
                <i class="ri-arrow-up-s-line"></i>
              </button>
              <%}%>
        </label>
        <div class="panel">
          <div class="wrapperContent">
            <% for (const module of moduleWrapper.modules) { %>
              <div class="module <%=module.comingSoonDate || moduleWrapper.comingSoonDate ? 'comingSoon' : '' %> accordian">
                <label for="<%= module._id %>" class="title">
                  <h1 class="name">
                    <%= module.title %>
                  </h1>
                  <%if(module.comingSoonDate){%>
                  <p class="comingsoon"><span>Unlocks on</span> <%=new Date(module.comingSoonDate).toLocaleDateString('en-US', { day: 'numeric', month: 'long' });%></p>
                  <%}else if(moduleWrapper.comingSoonDate){%>
                  <p class="comingsoon"><span>Unlocks on</span> <%=new Date(moduleWrapper.comingSoonDate).toLocaleDateString('en-US', { day: 'numeric', month: 'long'  });%></p>
                  <%}else{%>
                  <input hidden type="checkbox" id="<%= module._id %>">
                  <button class="dropdownButton">
                    <i class="ri-arrow-up-s-line"></i>
                  </button>
                  <%}%>
              </label>
              <div close class="lectures panel">
                <div class="lectureContent">
                  <% for (const lecture of module.lectures) { %> 
                    <%if(lecture.type == "video"){%>
                      <% const videoDetails = videoDetailsByLecture.find(item => item.lectureId.toString() === lecture._id.toString()) %>
                      <% if(videoDetails.videoDetails === undefined) continue %>
                      <% if(videoDetails.access === true){ %>
                      <%if((Date.now() < lecture.comingSoonDetails.date) || lecture.guid == "null"){%>
                      <div
                        style="pointer-events: none;"
                        data-type="video"
                        class="lecture comingSoon"
                      >
                        <label class="newLabel"></label>
                        <div class="left">
                          <div class="thumbnail">
                            <h2>Upcoming</h2><img
                              height="20px"
                              src="/images/tools.png"
                              alt=""
                            >
                          </div>
                        </div>
                      </div>
                      <%}else{%>
                      <div
                        data-type="video"
                        class="lecture <%=videoDetails.completionPercentage > 90 ? "watched" : ""%>"
                        data-lecture-id="<%=lecture._id%>"
                        data-module-wrapper-id="<%=moduleWrapper._id%>"
                        data-module-id="<%= module._id %>"
                      >
                        <%if(request && lecture.newLecture){%>
                        <%-request.user.seenLecture.includes(lecture._id) ? "" : '<label class="newLabel"></label>' %>
                        <%}else if(lecture.newLecture){%>
                        <label class="newLabel"></label>
                        <%}%>
                          <div class="left">
                            <div class="thumbnail">
                              <img load-lazy="<%=videoDetails.videoDetails.poster%>">
                        <div class="thumbnailProgress">
                          <div
                            class="meter"
                            style="width: <%=videoDetails.completionPercentage%>%;"
                          >
                          </div>
                        </div>
                        <div class="duration">
                          <small><%= videoDetails.videoDetails.duration %></small>
                        </div>
                      </div>
                  </div>
                  <div class="rgiht">
                    <p class="lectureTitle">
                      <%= videoDetails.videoDetails.title %>
                    </p>
                    <p class="marks">
                      <span><%=Math.ceil((Number(videoDetails.completionPercentage)/100) * Number(lecture.points))%></span> /
                      <%=lecture.points%> marks
                    </p>
                  </div>
              </div>
              <%} %>
              <% }else{ %>
              <%if((Date.now() < lecture.comingSoonDetails.date) || lecture.guid == "null"){%>
              <div
                data-type="video"
                class="lecture comingSoon lock"
              >
                <label class="newLabel"></label>
                <div class="left">
                  <div class="thumbnail">
                    <h2>Upcoming</h2><img
                      height="20px"
                      src="/images/tools.png"
                      alt=""
                    >
                  </div>
                </div>
              </div>
              <%}else{%>
              <div
                data-type="video"
                class="lecture lock"
              >
                <%if(lecture.newLecture){%>
                <label class="newLabel"></label>
                <%}%>
                          <div class="left">
                            <div class="thumbnail">
                              <img load-lazy="<%=videoDetails.videoDetails.poster%>">
                <div class="duration">
                  <small><%= videoDetails.videoDetails.duration %></small>
                </div>
              </div>
            </div>
            <div class="rgiht">
              <p class="lectureTitle">
                <%= videoDetails.videoDetails.title %>
              </p>
              <p class="marks">
                <span><%=Math.ceil((Number(videoDetails.completionPercentage)/100) * Number(lecture.points))%></span> /
                <%=lecture.points%> marks
              </p>
      
            </div>
            </div>
      
            <% } %>
            <% } %>
            <% }else if(lecture.type == "mcq"){ %>
            <div class="lecture <%=lectureCompletionData[lecture._id.toString()] == 100 ? "watched" : ""%> <%=lectureCompletionData[lecture._id.toString()] == -1 ? "left" : ""%> <%=lectureCompletionData[lecture._id.toString()] != undefined && lectureCompletionData[lecture._id.toString()] == 0 ? "wrong" : ""%> " data-type="mcq" data-lecture-id="<%=lecture._id%>" data-module-wrapper-id="<%=moduleWrapper._id%>" data-module-id="<%= module._id %>">
              <%if(request && lecture.newLecture){%>
              <%-request.user.seenLecture.includes(lecture._id) ? "" : '<label class="newLabel"></label>' %>
              <%}else if(lecture.newLecture){%>
              <label class="newLabel"></label>
              <%}%>
                    <div class="left">
                    <div class="thumbnail">
                      <img load-lazy="https://ik.imagekit.io/sheryians/backend%20diff_6TDoUnM4NM.png?updatedAt=1724916745028">
                    </div>
                    </div>
                    <div class="rgiht">
                      <p class="lectureTitle">
                        <%= lecture.title %>
                      </p>
                      <p class="marks"><span><%=Math.ceil((Number(lectureCompletionData[lecture._id.toString()] ? lectureCompletionData[lecture._id.toString()] : 0 )/100) * Number(lecture.points))%></span> / <%=lecture.points%> marks</p>
                    </div>
            </div>
            <% }else if(lecture.type == "code"){%>
              <div data-points='<%=lecture.points%>' <%=lectureCompletionData[lecture._id.toString()]%> data-prevPoints="<%=(Number(lectureCompletionData[lecture._id.toString()] == -1 || !lectureCompletionData[lecture._id.toString()] ? 0 : lectureCompletionData[lecture._id.toString()]) / 100) * lecture.points%>" class="lecture <%=lectureCompletionData[lecture._id.toString()] == 100 ? "watched" : "" %> <%=lectureCompletionData[lecture._id.toString()] == -1 ? "wrong" : ""%> <%=lectureCompletionData[lecture._id.toString()] >= 0 && lectureCompletionData[lecture._id.toString()] < 100  ? "left" : ""%> " data-type="code"  data-link='<%=pdflinks[lecture._id]%>' data-lecture-id="<%=lecture._id%>" data-module-wrapper-id="<%=moduleWrapper._id%>" data-module-id="<%= module._id %>">
                <%if(request && lecture.newLecture){%>
                <%-request.user.seenLecture.includes(lecture._id) ? "" : '<label class="newLabel"></label>' %>
                <%}else if(lecture.newLecture){%>
                <label class="newLabel"></label>
                <%}%>
                  <div class="left">
                  <div class="thumbnail">
                    <img load-lazy="https://ik.imagekit.io/sheryians/Backend%20diff-1_5y_QJIGY6.png?updatedAt=1724916744906">
                  </div>
                  </div>
                  <div class="rgiht">
                  <p class="lectureTitle"><%= lecture.problem_name %></p>
                  <p class="marks"><span><%=Math.ceil((Number(lectureCompletionData[lecture._id.toString()] ? lectureCompletionData[lecture._id.toString()] : 0)/100) * Number(lecture.points))%> </span> / <%=lecture.points%> marks</p>
              </div>
              </div>
            <% }else if(lecture.type == "pdf"){%>
            <div class="lecture <%=lectureCompletionData[lecture._id.toString()] == 100 ? "watched" : lectureCompletionData[lecture._id.toString()] == -1 && "left"%>" data-type="pdf" data-link='<%=pdflinks[lecture._id]%>' data-lecture-id="<%=lecture._id%>" data-module-wrapper-id="<%=moduleWrapper._id%>" data-module-id="<%= module._id %>">
              <%if(request && lecture.newLecture){%>
              <%-request.user.seenLecture.includes(lecture._id) ? "" : '<label class="newLabel"></label>' %>
              <%}else if(lecture.newLecture){%>
              <label class="newLabel"></label>
              <%}%>
                    <div class="left">
                    <div class="thumbnail">
                      <img load-lazy="https://ik.imagekit.io/sheryians/Backend%20dif-2f__0iIlTfD2e.png?updatedAt=1724916745006">
                    </div>
                    </div>
                    <div class="rgiht">
                      <p class="lectureTitle">
                        <%= lecture.title %>
              </p>
              <p class="marks"><span><%=Math.ceil((Number(lectureCompletionData[lecture._id.toString()] ? lectureCompletionData[lecture._id.toString()] : 0)/100) * Number(lecture.points))%></span> / <%=lecture.points%> marks</p>

            </div>
            </div>
            <%}%>
                <% } %>
            </div>
            </div>
            </div>
      <% } %>
      </div>
      </div>
      </div>
      <% } %>
    </section>
    <div class="iframe-overlay"></div>
    <div id="toast" class="hidden"></div>
    <section onclick="closePopup()" class="popup sharePopup">
      <div class="center">
        <img load-lazy="/Sheryians_Logo_wFKd9VClG.png" alt="">
        <h2>
          <h4>Share this video with your batchmates.</h4>

          <div class="actions">
            <a href="whatsapp://send?text=http://sheryians.com/classroom/gotoclassroom/<%= courseId %>/tryforfree">
              <img load-lazy="/images/WhatsApp.svg" alt="">
            </a>
            <div class="copy" onclick="(()=>{
          copyToClipboard('http://sheryians.com/classroom/gotoclassroom/<%= courseId %>/tryforfree');
          showToast('Link copied to clipboard')
        })()">
              <i class="ri-file-copy-line"></i>
            </div>

          </div>

          <div onclick="closePopup()" class="closePopup">
            <i class="ri-close-circle-line"></i>
          </div>
      </div>
    </section>
    <section onclick="closePopup()" class="popup phoneNumberPopUp">
      <div class="center">
        <img load-lazy="/Sheryians_Logo_wFKd9VClG.png" alt="">
        <h2>
          uh oh!
        </h2>
        <p>Unleash the Possibilities! Obtain the Course to Gain Access to Every Feature!</p>
        <button onclick="(()=>{
      window.location.href = '/Courses/enroll/<%=courseId%>/payfee'
    })()"> Get now </button>
        <div onclick="closePopup()" class="closePopup">
          <i class="ri-close-circle-line"></i>
        </div>
      </div>
    </section>
    <section class="popup feedbackPopup">
      <div style="pointer-events: all;" class="center">
        <img load-lazy="/Sheryians_Logo_wFKd9VClG.png" alt="">
        <h2>
          Feedback
        </h2>
        <textarea maxlength="200" placeholder="Enter Your Feedback" name="" id="" cols="30" rows="10"></textarea>
        <div id="buttons">
          <button onclick="closePopup()">Cancel</button>
          <button onclick="submitFeedback(this)">Send Feedback</button>
        </div>
        <div onclick="closePopup()" class="closePopup">
          <i class="ri-close-circle-line"></i>
        </div>
      </div>
    </section>
    <section onclick="closePopup()" class="popup signInPopup">
      <div class="center">
        <img load-lazy="/Sheryians_Logo_wFKd9VClG.png" alt="">
        <h2>
          Sign-in required !
        </h2>
        <p>You need to login first. No login, no fun. 😜</p>
        <button onclick="(()=>{
      window.location.href = '/signIn'
    })()"> Sign-in <i class="ri-arrow-right-line"></i> </button>
        <div onclick="closePopup()" class="closePopup">
          <i class="ri-close-circle-line"></i>
        </div>
      </div>
    </section>

    <section class="popup offlinePopup">
      <div class="center">
        <img src="/Sheryians_Logo_wFKd9VClG.png" alt="">
        <h2>
          You are offline
        </h2>
        <p>Please connect to the internet to continue learning</p>
      </div>
    </section>

    <section class="popup hardwareAcceleraionPopup disabled">
      <div class="center">
        <img load-lazy="/Sheryians_Logo_wFKd9VClG.png" alt="">
        <h2>
          Hardware Acceleration Disabled
        </h2>
        <p>Hardware acceleration is disabled in your browser. Please enable it for optimal experience.</p>
        <button onclick="(()=>{
      window.location.href = 'https://www.lifewire.com/hardware-acceleration-in-chrome-4125122'
    })()"> Enable <i class="ri-arrow-right-line"></i> </button>
      </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js" integrity="sha512-WrdC3CE9vf1nBf58JHepuWT4x24uTacky9fuzw2g/3L9JkihgwZ6Cfv+JGTtNyosOhEmttMtEZ6H3qJWfI7gIQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://player.vdocipher.com/v2/api.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script defer src="/js/gotoofflineclassroomejs.min.js"></script>
    <script>

      function askDoubt(){
        try{
          const question = document.querySelector('.doubtQuestion').value
          if(question.length < 30 || question.length > 200){
            document.querySelector('.doubt-container p').style.opacity = "1"
            return
          }
          joinDiscord('<%= course.discord_channel_id %>','<%= course.discord_role_id %>','<%= request.user._id %>',question)
        }catch(err){
          console.log(err)
          document.querySelector('.doubt-container p').style.opacity = "1"
          document.querySelector('.doubt-container p').textContent = "Something unexpected happend"
        }

      }

      function openDoubtPopup(){
        document.querySelector('.doubtPopup').style.display = "flex"
      }

      const batchId = '<%=batchId%>'
      const verticalResize = document.querySelector(".verticalResizer");
      const leftDiv = document.querySelector('.page1 .heroArea');
      const rightDiv = document.querySelector('.page1 .chapters');
      const iframeOverlay = document.querySelector('.iframe-overlay');
      let isVResizing = false;

      verticalResize.addEventListener('click', function(e) {
        rightDiv.classList.add("onlyNav")
      });

      verticalResize.addEventListener('mousedown', function(e) {
        isVResizing = true;
        iframeOverlay.style.display = "block"
        document.addEventListener('mousemove', onVMouseMove);
        document.addEventListener('mouseup', onMouseUp, { once: true }); // Capture mouseup only once
      });
      
      function onVMouseMove(e) {
        if (!isVResizing) return;

        const containerOffsetLeft = verticalResize.parentNode.offsetLeft;
        const containerWidth = verticalResize.parentNode.offsetWidth;
        const mouseX = e.clientX - containerOffsetLeft;

        // Calculate new widths
        let leftDivWidth = (mouseX / containerWidth) * 100;
        let rightDivWidth = 100 - leftDivWidth;

        // Prevent rightDiv shrinking below 30%
        if (rightDivWidth > 30) {
          rightDivWidth = 30;
          leftDivWidth = 100 - rightDivWidth;
        }

        rightDiv.classList.toggle("onlyNav", rightDivWidth <= 4);
        leftDiv.style.width = `${leftDivWidth}%`;
        rightDiv.style.width = `${rightDivWidth}%`;
      }

      function onMouseUp() {
        isVResizing = false;
        iframeOverlay.style.display = "none"

        document.removeEventListener('mousemove', onVMouseMove);
        // No need to remove mouseup listener as it's captured only once
      }

    </script>
    <script>
      window.addEventListener('online', function() {
        window.location.reload()
      });

      window.addEventListener('offline', function() {
        document.querySelector(".offlinePopup").style.display = "flex"
        document.querySelector(".offlinePopup").style.opacity = "1"
        window.location.href = "/offline"
      })
      const CurrentUsername = '<%= (typeof user != 'undefined')?user.name:'' %>'; // Replace with the actual username
      const CurrentProfilePicture = '<%= profilePicture %>'; // Replace with the actual profile picture
      const CurrentUserId = '<%= (typeof user != 'undefined')?user._id:"" %>';
      const userProfilePicture = '<%= profilePicture %>';
      let courseId = '<%=courseId%>'
      let isUserLoggedIn = true;
      let fullAccess = false;
      <% if (typeof user == 'undefined') { %>
      isUserLoggedIn = false;
      <% } %>
      <% if (fullAccess) { %>
      fullAccess = true;
      <% } %>

      <% if (typeof user != 'undefined') { %>
      <% if (typeof lecture_open != 'undefined') { %>
      const findlastWatchedLecture = <%- JSON.stringify(lecture_open) %>;
      <% } else{ %>
      const lastWatchedLectureData = <%- JSON.stringify(lastWatchedLecture) %>;
      const findlastWatchedLecture = lastWatchedLectureData.find(item => item.courseId.toString() === courseId.toString());
      <% } %>
      <% } else { %>
      const findlastWatchedLecture = null;
      <% } %>
    </script>


    <noscript>
      <section class="popup noScriptPopup disabled">
        <div class="center">
          <img src="/Sheryians_Logo_wFKd9VClG.png" alt="">
          <h2>
            Scripts couldn't be loaded
          </h2>
          <p>Scripts are disabled or check your internet connection for optimal experience.</p>
        </div>
      </section>
    </noscript>
  </main>




  <script defer>
    let mobile = false;
    let notMobile = false
    let tablet = false;

    function isTablet() {
      if (tablet) {
        return true;
      }
      if (/iPad|Android/.test(navigator.userAgent) && !window.MSStream) {
        tablet = true;
      }
      return tablet;
    }
    // Check if the current device is a mobile phone

    function isMobile() {
      if (mobile) {
        return true;
      }
      if (notMobile) {
        return false
      }
      if (
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        ) && window.innerWidth < 600
      ) {
        mobile = true;
        return true;
      } else {
        notMobile = false
        return false
      }
    }



    // Check if the current device is a desktop
    function isDesktop() {
      return !isTablet() && !isMobile();
    }


    function mapRange(value, inMin, inMax, outMin, outMax) {
      return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
    }

    function clamp(value, min, max) {
      return Math.min(Math.max(value, min), max);
    }
  </script>


  <script defer>
    function closePopup() {
      document.querySelectorAll('.popup').forEach(pop => {
        pop.classList.remove('active')
        pop.style.display = 'none'
        pop.style.opacity = '0'
      })

      try {
        if (lenis)
          lenis.start()
      } catch (er) {

      }
    } 

    function closeRotatePopup() {
      if(document.querySelector('.rotatePopup')){
        document.querySelector('.rotatePopup').classList.remove('active')
        document.querySelector('.rotatePopup').style.display = 'none'
        document.querySelector('.rotatePopup').style.opacity = '0'
      }
    }

    function openRotatePopup() {
      if(document.querySelector('.rotatePopup')){

        document.querySelector('.rotatePopup').style.display = 'flex'
        document.querySelector('.rotatePopup').classList.add('active')
        document.querySelector('.rotatePopup').style.opacity = '1'
      }
    }

    function openDiscordPopup() {
      if(document.querySelector('.discordJoinPopup')){
        document.querySelector('.discordJoinPopup').style.display = 'flex'
        document.querySelector('.discordJoinPopup').classList.add('active')
        document.querySelector('.discordJoinPopup').style.opacity = '1'
        try {
          if (lenis)
            lenis.stop()
        } catch (err) {

        }
      }
    }

    function closeDiscordPopup() {
      document.querySelector('.discordJoinPopup').classList.remove('active')
      document.querySelector('.discordJoinPopup').style.display = 'none'
      document.querySelector('.discordJoinPopup').style.opacity = '0'

      try {
        if (lenis)
          lenis.start()
      } catch (err) {

      }
    }

    function onOrientationChange(callbacks) {
      if (!isMobileDevice()) {
        return
      }
      if (typeof callbacks.landscape !== 'function' || typeof callbacks.portrait !== 'function') {
        console.log('Invalid input. Please provide an object with landscape and portrait callback functions.');
        return;
      }

      function handleOrientationChange() {
        const orientation = window.orientation;
        if (orientation === 0 || orientation === 180) {
          // Portrait orientation
          callbacks.portrait();
        } else {
          // Landscape orientation
          callbacks.landscape();
        }
      }

      // window.addEventListener('resize', handleOrientationChange);

      // Initial callback with current orientation
      handleOrientationChange();


    }

    function isMobileDevice() {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }


    // Example usage:
    const orientationCallbacks = {
      landscape: openRotatePopup,
      portrait: closeRotatePopup,
    };

    const cleanupCallback = onOrientationChange(orientationCallbacks);


    var isNotificationVisible = false
    const popup = document.querySelector('.notifications');
    const navMainRight = document.querySelector('.nav-right')


    function insertAfter(referenceNode, newNode) {
      referenceNode.parentElement.insertBefore(newNode, referenceNode.nextSibling);
    }

    function defaultDp(event) {
      event.setAttribute("src", "/images/Sheryians Logo.png")
    }

    function setTimeDifference(current, previous, elem) {
      elem.textContent = timeDifference(current, previous)
    }

    function timeDifference(current, previous) {
      const milliSecondsPerMinute = 60 * 1000;
      const milliSecondsPerHour = milliSecondsPerMinute * 60;
      const milliSecondsPerDay = milliSecondsPerHour * 24;
      const milliSecondsPerMonth = milliSecondsPerDay * 30;
      const milliSecondsPerYear = milliSecondsPerDay * 365;

      const elapsed = current - previous;

      if (elapsed < milliSecondsPerMinute) {
        return Math.round(elapsed / 1000) + ' seconds ago';
      } else if (elapsed < milliSecondsPerHour) {
        return Math.round(elapsed / milliSecondsPerMinute) + ' minutes ago';
      } else if (elapsed < milliSecondsPerDay) {
        return Math.round(elapsed / milliSecondsPerHour) + ' hours ago';
      } else if (elapsed < milliSecondsPerMonth) {
        return Math.round(elapsed / milliSecondsPerDay) + ' days ago';
      } else if (elapsed < milliSecondsPerYear) {
        return Math.round(elapsed / milliSecondsPerMonth) + ' months ago';
      } else {
        return Math.round(elapsed / milliSecondsPerYear) + ' years ago';
      }
    }

    async function openNotification(link, id) {

      try {
        axios.post("/classroom/notifications/update", {
          notificationId: id
        })
        window.location.href = link
      } catch (err) {
        console.log(err)
      }

    }


    function createNotification(seen, id, type, href, someone, text, userProfile, lectureThumbnail, createdAt) {
      const aElement = document.createElement('div');
      // aElement.setAttribute('href', href);
      aElement.setAttribute("onclick", `openNotification("${href}","${id}")`)
      aElement.classList.add('notification', type);
      if (seen) aElement.classList.add("seen");
      if (!seen) aElement.classList.add("unseen");


      const userDiv = document.createElement('div');
      userDiv.classList.add('user');
      const userImg = document.createElement('img');
      userImg.setAttribute('src', userProfile);
      userImg.setAttribute('alt', '');
      userDiv.appendChild(userImg);

      const dataDiv = document.createElement('div');
      dataDiv.classList.add('data');
      if (type == 'like') {
        const likeP = document.createElement('p');
        likeP.classList.add('like');
        likeP.innerHTML = `👍 <span class="fromUser">${someone}</span> liked : `;
        dataDiv.appendChild(likeP);
      } else if (type == 'reply') {
        const replyP = document.createElement('p');
        replyP.classList.add('reply');
        replyP.innerHTML = `<span class="fromUser"> ${someone}</span> replied: `;
        dataDiv.appendChild(replyP);
      }
      const textP = document.createElement('p');
      textP.classList.add('text');
      textP.innerHTML = `<span>"</span><span>${text}</span><span>"</span>`;
      const small = document.createElement('small');
      small.innerText = timeDifference(Date.now(), new Date(createdAt));
      dataDiv.appendChild(textP);
      dataDiv.appendChild(small);

      const lectureDiv = document.createElement('div');
      lectureDiv.classList.add('lecture');
      const lectureImg = document.createElement('img');
      lectureImg.setAttribute('src', lectureThumbnail);
      lectureImg.setAttribute('alt', '');
      lectureDiv.appendChild(lectureImg);

      aElement.appendChild(userDiv);
      aElement.appendChild(dataDiv);
      aElement.appendChild(lectureDiv);

      return aElement;
    }

    function createNotificationUpdate(seen, id, type, href, text, lectureThumbnail, createdAt) {
      const aElement = document.createElement('div');
      // aElement.setAttribute('href', href);
      aElement.setAttribute("onclick", `openNotification("${href}","${id}")`)
      aElement.classList.add('notification', type);
      if (seen) aElement.classList.add("seen");
      if (!seen) aElement.classList.add("unseen");



      const dataDiv = document.createElement('div');
      dataDiv.classList.add('data');

      const textP = document.createElement('p');
      textP.classList.add('text');
      textP.innerHTML = `<span>"</span><span>${text}</span><span>"</span>`;
      const small = document.createElement('small');
      small.innerText = timeDifference(Date.now(), new Date(createdAt));
      dataDiv.appendChild(textP);
      dataDiv.appendChild(small);

      const lectureDiv = document.createElement('div');
      lectureDiv.classList.add('lecture');
      const lectureImg = document.createElement('img');
      lectureImg.setAttribute('src', lectureThumbnail);
      lectureImg.setAttribute('alt', '');
      lectureDiv.appendChild(lectureImg);

      aElement.appendChild(dataDiv);
      aElement.appendChild(lectureDiv);

      return aElement;
    }


    /* 
    
    use replyNotification use as first argument to use as reply comment notification
    and likeNotification for like comment notification
    
    */

    let page = 1; // Initialize the page number for pagination

    const SCROLL_THRESHOLD = 5;
    const notificationBtn = document.querySelector('.notificationButton');
    const notificationsContainer = document.querySelector('.notifications');

    async function loadMoreNotification(event) {
      const {
        scrollTop,
        clientHeight,
        scrollHeight
      } = event.target;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - SCROLL_THRESHOLD;

      if (isNearBottom) {
        page++;

        try {
          const notifications = await fetchNotifications(page);


          displayNotifications(notifications.notifications);
        } catch (err) {
          console.error("Error loading more notifications:", err);
        }
      }
    }

    // Function to fetch notifications from the API
    async function fetchNotifications(page) {
      const response = await fetch(`/classroom/notifications/${page}`);
      const data = await response.json();
      return data;
    }

    function displayNotifications(notifications) {
      notifications.forEach((notification) => {
        if (notification.type == "update") {
          var notificationElement = createNotificationUpdate(
            notification.seen,
            notification._id,
            notification.type,
            notification.link,
            notification.message,
            notification.thumbnail_url,
            notification.createdAt
          );
        } else {
          var notificationElement = createNotification(
            notification.seen,
            notification._id,
            notification.type,
            notification.link,
            notification.sender.name,
            notification.message,
            notification.sender_dp,
            notification.thumbnail_url,
            notification.createdAt
          );
        }
        notificationsContainer.appendChild(notificationElement);
      });
    }

    // Function to implement "load more" on scroll

    // document.querySelector('.notifications').appendChild(createNotification(
    //   'replyNotification',
    //   '/courses',
    //   'Ankur',
    //   'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quas et officia nobis, impedit accusantium deleniti eveniet ab eum aliquam commodi mollitia quo adipisci perspiciatis ipsa, sequi, voluptatibus sapiente quaerat fugiat?',
    //   'https://media.istockphoto.com/id/1345121223/photo/young-beautiful-woman-stock-photo.webp?b=1&s=170667a&w=0&k=20&c=H7SuMjLiOzm78vNDb_cGGke5Qsp_94pFugV2Adzs9Eo=',
    //   'https://ik.imagekit.io/sheryians/courses_gif/Mastering_the_Art_of_Resume-RESUMECOURSEPOSTER_NK6hP8IP8.jpg',
    // ))




    // To stop listening to orientation changes, call the cleanup function:
    // cleanupCallback();
  </script>
  
  <%- include('./new-partials/footer.ejs') %>