<%- include('../new-partials/header.ejs') %>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/css/common.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.1.1/build/css/intlTelInput.css">
    <link rel="stylesheet" href="/css/course-details-css/dsa-domination-cohort.css">

    <script defer="">
        function hidePreLoader() {
            document.querySelector('#preLoder').style.opacity = '0'
            setTimeout(() => {
                document.querySelector('#preLoder').style.display = 'none'
            }, 500);
        }

        function observeAndCallback(element, callback, options) {
            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        callback(entry.target); // Run the callback when the element is intersecting
                        observer.disconnect(); // Disconnect the observer after the callback is executed
                    }
                });
            }, options);

            observer.observe(element);
        }

        function applyLazyLoading() {
            document.querySelectorAll('[load-lazy]').forEach(lazyLoadingElement => {

                function callbackFunction() {
                    const src = lazyLoadingElement.getAttribute('load-lazy')
                    lazyLoadingElement.removeAttribute('load-lazy')
                    lazyLoadingElement.setAttribute('src', src)
                }

                observeAndCallback(lazyLoadingElement, callbackFunction, {
                    threshold: 0.5
                });
            })
        }

        window.addEventListener('DOMContentLoaded', (event) => {

            applyLazyLoading()
        })
    </script>
    <title>[ LIVE ] Front-End Domination Create Anything with Code.</title>
    <script>
        !function (f, b, e, v, n, t, s) {
            if (f.fbq) return; n = f.fbq = function () {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
            n.queue = []; t = b.createElement(e); t.async = !0;
            t.src = v; s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '632198189232294');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=632198189232294&ev=PageView&noscript=1" /></noscript>
    <!-- End Meta Pixel Code -->
    </head>

    <body>
        <!-- <%- include('../new-partials/holyBar.ejs') %> -->

        <div id="main">
            <section id="page1" class="page">
                <div class="left">
                    <div class="title">
                        <h1><span class="mask-bg">
                                <span>[ Live ]</span>
                                <span class="bloom">[ Live ]</span>
                            </span> DSA Domination Cohort</h1>
                    </div>
        
                    <div class="activeSupport">
                        Live classes <img
                            src="https://ik.imagekit.io/sheryians/Aptitude%20&amp;%20Reasoning/360_F_621906809_C9rwm43Z7HQzYnrbTfMhmeyznilVgRFk%201_84ajxxlmg.png"
                            alt="">
                    </div>
        
                    <div class="price">
                        <p>Only <span class="originalValue priceValue">₹ <%=course.fake_price ? course.fake_price : (course.display_price ||
                                    course.fees * 2) + 1%></span> <span class="priceValue"><span>₹</span>
                                <%=course.display_price || course.display_price || course.fees%>*
                            </span> <span class="discount">( <%=course.fake_price ? Math.round(((parseInt(course.fake_price) -
                                    course.display_price || course.fees) / course.fake_price) * 100) : 50%>% off )</span> </p>
                    </div>
                    <div class="buy">
                        <%if(status=="paid" ){%>
                            <p class="buyNow" onclick="window.location.href='/classroom/gotoclassroom/<%=course._id%>'">Classroom</p>
                            <%}else{%>
                                <p class="buyNow" onclick="enroll()">Buy Now - Start Learning</p>
                                <%}%>
                                    </div>
                    <p class="description">Master Data Structures & Algorithms in any language — C++, Java, Python, or JavaScript. Boost your logical reasoning and
                    problem-solving skills.
                    </p>
                </div>
                <div class="right">
                    <div id="playOverlay">
                        <i id="playBtn" class="ri-play-circle-line"></i>
                        <img src="https://ik.imagekit.io/sheryians/java&dsa/web-dsa-thumb-10_ZKtPNgmW_.webp" alt="">
                    </div>
                    <div id="embedded-promo">
                        <iframe id="promo-video" width="100%" height="100%"
                            src="https://www.youtube.com/embed/hcxdmWyQEKY?si=xqrt9nRm8EAVyu_u?autoplay=1&amp;vq=hd720"
                            title="YouTube video player" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen=""></iframe>
                    </div>
                    <div class="tags">
                        <div class="tag">
                            in-depth
                        </div>
                    </div>
                </div>
            </section>
        
            <section class="page page2">
        
                <div class="text">
                    <div class="maskText">
                        <img src="https://ik.imagekit.io/sheryians/Job%20Ready%20Live%20Batch/work_uuk9z1lNl.png" alt="">
                        <img class="bloom" src="https://ik.imagekit.io/sheryians/Job%20Ready%20Live%20Batch/work_uuk9z1lNl.png"
                            alt="">
                    </div>
                    <h2 class="subHeading">
                        in 4 months*
                        <br>
                    </h2>
                </div>
            </section>
            <section class="page page2">
        
                <div class="text">
                    <div class="maskText">
                        <img src="https://ik.imagekit.io/sheryians/java&dsa/20250528_1215_DSA-Centric%20Graphic%20Design_remix_01jwat3wcyes9s6v6z47n1rpgc_p_njXiaTK.webp?tr=e-removedotbg"
                            alt="">
                        <img class="bloom"
                            src="https://ik.imagekit.io/sheryians/java&dsa/20250528_1215_DSA-Centric%20Graphic%20Design_remix_01jwat3wcyes9s6v6z47n1rpgc_p_njXiaTK.webp?tr=e-removedotbg"
                            alt="">
                    </div>
                </div>
        
                <!-- <video src="Video2.mp4" muted autoplay loop></video> -->
                <img src="https://ik.imagekit.io/sheryians/java&dsa/dsa-master_atGnKcqgR.webp"
                    alt="Stats">
        
        
            </section>
        
            <section class="page page4">
        
                <div class="text">
                    <h1 class="heading">Dominate.</h1>
                    <h2 class="subTitle">From Start to Victory.</h2>
                </div>
        
        
            </section>
        
        
        
            <section class="page page5 syllabus">
        
        
        
        
        
        
                <div class="accordian active">
                    <div class="top">
                        <h1>Episode 1: Conquering Fundamentals</h1>
        
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>Basics of Programming</p>
                            <p>Conditional Statements</p>
                            <p>Loops & Pattern Programming</p>
                            <p>Methods & Functions</p>
                            <p>Array Data Structures</p>
                            <p>Complexity & Big-O Analysis</p>
                        </div>
                    </div>
                </div>
                <div class="accordian active">
                    <div class="top">
                        <h1>Episode 2: Majestic Mastery</h1>
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>OOPS Concepts</p>
                            <p>String Handling</p>
                            <p>Collections & Hashing</p>
                            <p>Dynamic Arrays</p>
                            <p>Bit Manipulation & Number Systems</p>
                        </div>
                    </div>
                </div>
                <div class="accordian active">
                    <div class="top">
                        <h1>Episode 3: Harnessing Inner Strength</h1>
        
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>Recursion & Memory Areas</p>
                            <p>Math Battles (GCD, Primes, Modulo)</p>
                            <p>Array & List Mastery</p>
                            <p>Sorting Algorithms</p>
                            <p>Binary Search</p>
                            <p>Hashing Zone</p>
                            <p>Matrix Warfare</p>
                            <p>Linked List Dominion</p>
                            <p>Stacks & Queues</p>
                        </div>
                    </div>
                </div>
                <div class="accordian active">
                    <div class="top">
                        <h1>Episode 4: Rising Beyond Limits</h1>
        
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>Recursion Revisit & Backtracking</p>
                            <p>Binary Tree</p>
                            <p>Binary Search Tree (BST)</p>
                            <p>Heap Kingdom</p>
                            <p>Sliding Window</p>
                            <p>Graph Theory</p>
                        </div>
                    </div>
                </div>
                <div class="accordian active">
                    <div class="top">
                        <h1>Episode 5: Unleashing the Warrior Within</h1>
        
                    </div>
                    <div class="panel">
                        <div class="panelContent">
                            <p>Dynamic Programming (Basics to Advanced)</p>
                            <p>Trie & Its Applications</p>
                            <p>Segment Tree & Binary Indexed Tree</p>
                        </div>
                    </div>
                </div>
            </section>
        
            <section class="page cta-syllabus">
                <a href="https://sheryians.notion.site/language-independent-dsa" class="btn buy" target="_blank">View Complete
                    Syllabus </a>
            </section>
        
            <section class="page page3">
                <div class="text">
                    <h1>Concepts</h1>
                    <h1>which <span><span>matters.</span> </span> </h1>
                    <!-- <h2 class="subHeading">Beyond Projects, Towards Purpose.</h2> -->
                </div>
                <div class="project">
                    <!-- <div class="heading">
                                <div class="buffer"></div>
                                <h1 class="btn-dark">Project 2</h1>
                            </div> -->
                    <!-- <h1 class="sub-heading">Dominate <br> Data Structure & Algorithm</h1> -->
                    <video src="https://ik.imagekit.io/sheryians/java&dsa/platform_pdi6w0XTxf.webm/ik-video.mp4" muted autoplay
                        loop playsinline playbackRate="2" onloadedmetadata="this.playbackRate = 2.0"></video>
                </div>
        
        
            </section>
        
        
            <section class="page page7">
                <div class="text">
                    <h1 class="heading">
                        More value,
                        <br>
                        Less Cost .
                    </h1>
                    <h2 class="subHeading">
                        Quality and value drive us, <span>delivering more </span> <br> for <span>less cost</span>.
                    </h2>
                    <small class="validity">*Course validity is for 1 year from date of purchase</small>
                </div>
                <div class="scratcher">
                    <small>Scratch this to get a discount coupon</small>
                    <div class="coupon">
                        <div class="container">
                            <div class="base">
                                <h3 id="couponCode" style="cursor:pointer;" title="Click to copy"> CRACK10 </h3>
                            </div>
                            <canvas id="scratch" height="280" width="420"></canvas>
                        </div>
                    </div>
                </div>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        var coupon = document.getElementById('couponCode');
                        if (coupon) {
                            coupon.addEventListener('click', function() {
                                var code = coupon.textContent.trim();
                                navigator.clipboard.writeText(code).then(function() {
                                    alert('Coupon code "' + code + '" copied to clipboard!');
                                });
                            });
                        }
                    });
                </script>
                <button class="start-learning bloom">
        
                    <span onclick="enroll()">Start Learning Now 😎</span>

                </button>
            </section>
        
            <section class="page page6">
                <div class="text">
                    <h1 class="heading">Frequently Asked Question (FAQs)</h1>
                </div>
                <div class="faqs">
                    <div class="indi-faqs">
                        <h3>Is this course language-specific?</h3>
                        <p>No. This is a language-independent DSA course. You can follow along in any language of your choice —
                            C++, Java, Python, or JavaScript.</p>
                    </div>
                    <div class="indi-faqs">
                        <h3>Are there any projects in this course?</h3>
                        <p>No, this course is focused purely on mastering DSA. The goal is to make you strong in algorithms and
                            data structures for interviews and coding rounds.</p>
                    </div>
                    <div class="indi-faqs">
                        <h3>Will I get a certificate?</h3>
                        <p>Yes, upon course completion, you will receive a certificate recognizing your mastery of Data
                            Structures & Algorithms.</p>
                    </div>
                    <div class="indi-faqs">
                        <h3>How long will I have access to the content?</h3>
                        <p>You will have access to the course content for 1 full year from the start date.</p>
                    </div>
                    <div class="indi-faqs">
                        <h3>Are recordings available if I miss a class?</h3>
                        <p>Yes, all live classes will be recorded and made available to you so you can learn at your own pace.
                        </p>
                    </div>
                    <div class="indi-faqs">
                        <h3>How can I get my doubts cleared?</h3>
                        <p>Doubts can be asked in our Discord group. We have mentors who respond actively and support learning
                            via group discussions.</p>
                    </div>
                    <div class="indi-faqs">
                        <h3>Is there one-on-one mentorship?</h3>
                        <p>No, we follow a group mentorship model via Discord to encourage collaborative problem solving and
                            faster support.</p>
                    </div>
                    <div class="indi-faqs">
                        <h3>What is the class schedule and timing?</h3>
                        <p>Classes will be held 6 days a week (Monday to Saturday) at 8:30 PM IST. The course duration is 4
                            months, covering 80–90 sessions.</p>
                    </div>
                    <div class="indi-faqs">
                        <h3>What are the prerequisites for this course?</h3>
                        <p>You just need a basic understanding of programming concepts. No prior DSA knowledge is required.</p>
                    </div>
                </div>
        
            </section>
        </div>
        <div class="popupp" id="phonePopup">
            <div id="popupElements">
                <div class="logo">
                    <img style="border-radius: 50%;"
                        src="https://ik.imagekit.io/sheryians/Logo/Shery_logo_qi_iCieHk.png?updatedAt=1715812884629" alt="">
                </div>
                <i id="popupClose" onclick="closePopup(this)" class="ri-close-circle-line"></i>
                <p>Don't worry, we won't call during dinner!" 🍽️</p>
                <input type="tel" id="">
                <p class="numberError">Number is invalid</p>
                <button onclick="checkPhoneNumberAndEnroll(this)">Enroll Now</button>
            </div>
            </div>
        <div id="content"></div>
        <div class="popupp" id="phonePopup">
            <div id="popupElements">
                <div class="logo">
                    <img style="border-radius: 50%;"
                        src="https://ik.imagekit.io/sheryians/Logo/Shery_logo_qi_iCieHk.png?updatedAt=1715812884629"
                        alt="">
                </div>
                <i id="popupClose" onclick="closePopup(this)" class="ri-close-circle-line"></i>
                <p>Don't worry, we won't call during dinner!" 🍽️</p>
                <input type="tel" id="">
                <p class="numberError">Number is invalid</p>
                <button onclick="checkPhoneNumberAndEnroll(this)">Enroll Now</button>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"
            integrity="sha512-NcZdtrT77bJr4STcmsGAESr06BYGE8woZdSdEgqnpyqac7sugNO+Tr4bGwGF3MsnEkGKhU2KL2xh6Ec+BqsaHA=="
            crossorigin="anonymous" referrerpolicy="no-referrer"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/ScrollTrigger.min.js"
            integrity="sha512-P2IDYZfqSwjcSjX0BKeNhwRUH8zRPGlgcWl5n6gBLzdi4Y5/0O4zaXrtO4K9TZK6Hn1BenYpKowuCavNandERg=="
            crossorigin="anonymous" referrerpolicy="no-referrer"></script>
        <script>
            gsap.to(".page4 .heading", {
                "--bgi-position": "0ch",
                scrollTrigger: {
                    trigger: ".page4",
                    start: "top 70%",
                    end: `top ${innerWidth > 600 ? "10%" : "30%"}`,
                    scrub: true
                },
            })
        </script>
        <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.1.1/build/js/intlTelInput.min.js"></script>
        <script defer="" src="/js/singleCourse.js"></script>
        <script src="/js/course-detail-pages/backend-domination.js"></script>
        <!-- <script src="script.js"></script> -->

        <script>
            feather.replace();

            document.querySelectorAll('.accordian .top').forEach(accordian => {
                accordian.addEventListener('click', (event) => {
                    event.target.closest('.accordian').classList.toggle('active')
                })
            })

            // const mouse = {
            //     x: 0,
            //     y: 0
            // }

            // addEventListener('mousemove', (event) => {
            //     mouse.x = event.clientX
            //     mouse.y = event.clientY
            // })

            // const images = document.querySelectorAll('.floatingImage')


            // function moveFloatingImages() {
            //     requestAnimationFrame(moveFloatingImages)

            //     images.forEach(image => {
            //         image.style.top = mouse.y + 'px'
            //         image.style.left = mouse.x + 'px'
            //     })



            // }
            // requestAnimationFrame(moveFloatingImages)

            function enroll() {

                if ("rec" == "live") {
                    window.location.href = "/Courses/enroll/<%=course._id%>"
                } else if ("rec" == "rec") {
                    window.location.href = "/Courses/enroll/<%=course._id%>/payfee"
                }
            }

        <% if (user) { %>
                window.addEventListener("scroll", () => {
                    if (window.hasAlertedBottom) return;

                    const scrolledToBottom =
                        window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 1600;
                    if (scrolledToBottom) {
                        fetch("/tracker/lead/scrolledToBottom", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                            },
                            body: JSON.stringify({
                                mobile: `<%= user ? user.phoneNumber : null %>`,
                                course: "Job Ready AI Powered Cohort",
                            }),
                        });
                        window.hasAlertedBottom = true;
                    }
                });
        <% } %>

        </script>

        <%- include('../new-partials/callToActionButtons.ejs') %>

            <%- include('../new-partials/footer-section.ejs') %>
                <%- include('../new-partials/footer.ejs') %>