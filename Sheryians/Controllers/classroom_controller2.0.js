const classroom_content2 = require("../Models/classroom_content2.0.js");
const classroom_content2_5 = require("../Models/classroom_content2.5.js");
const Courses = require("../Models/Courses.js");
const Feedback = require("../Models/courseFeedback.js");
const Profile_dp = require('../Models/profile_dp.js');
const Comments_Classroom2 = require('../Models/Comments_Classroom2.js');
const codingQuestions = require('../Models/codingQuestion.js');
const User = require('../Models/user.js');
const fetch = require('node-fetch');
const NodeCache = require('node-cache');
var crypto = require("crypto");
const rateLimit = require("express-rate-limit");
const profileDp = require("../Models/profile_dp.js");
const mongoose = require("mongoose");
const fs = require("fs");
const cache = new NodeCache({ stdTTL: 86400 }); // Cache video details for 24 hours
const tryforfreeCache = new NodeCache({ stdTTL: 86400 }); // Cache video details for 24 hours
const videoDetailsCache = new NodeCache({ stdTTL: 86400 }); // Cache video details for 24 hours
const usersProfilePicture = new NodeCache({ stdTTL: 3600 }); // Cache user profile pictures for 1 hour
const mcqCache = new NodeCache({ stdTTL: 86400 }); // Cache MCQ details for 24 hours
const formidable = require("formidable")
const Imagekit = require("imagekit");
const imagekit = new Imagekit({
    publicKey: 'public_DQRGKIIs98Gr4cHv0e2XAn73I2U=',
    privateKey: 'private_Hk3XPFhDJiyZSBfqdx+bhm537O4=',
    urlEndpoint: 'https://ik.imagekit.io/sheryians'
});

const {testdogAI, SdkAuthenticationError, SdkRequestError, SdkInputError} = require("@testdog/ai")
const { google } = require('googleapis');
const nodemailer = require('nodemailer');
const { c } = require("compile-run");
const { use } = require("passport");
const submissionStorage = require("../Models/submissionStorage.js");
const offline_class = require("../Models/offlineClasses.js");
const CLIENT_ID = process.env.CLIENT_ID;
const CLIENT_SECRET = process.env.CLIENT_SECRET;
const REDIRECT_URI = process.env.REDIRECT_URI;;
const REFRESH_TOKEN = process.env.REFRESH_TOKEN;
const oauth2Client = new google.auth.OAuth2(CLIENT_ID, CLIENT_SECRET, REDIRECT_URI);
oauth2Client.setCredentials({
    refresh_token: REFRESH_TOKEN
});

const unsupportBrowsersPage = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Browser Compatibility Warning</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
      text-align: center;
      margin-top: 100px;
    }
    .message {
      background-color: #fff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      width: 80%;
      margin: 0 auto;
    }
    h1 {
      font-size: 28px;
      color: #333;
    }
    p {
      font-size: 18px;
      color: #666;
      margin-bottom: 20px;
    }
    .funny {
      font-weight: bold;
      color: #ff4500;
    }
  </style>
</head>
<body>
  <div class="message">
    <h1>Oops! 🙊</h1>
    <p><span class="funny">Looks like you're using a browser that's not our favorite!</span></p>
    <p>Please use <a id="chromeLink" href="#">Google Chrome</a> or Chromium-based browsers to access the classroom.</p>
    <p>Our classroom works best with these browsers, and you might miss out on some cool features if you're not using them!</p>
    <p>Give it a try and switch over! 🚀</p>
  </div>
  <script>
    // Add event listener to the Chrome link
    document.getElementById('chromeLink').addEventListener('click', function(event) {
      event.preventDefault(); // Prevent default link behavior

      // Open Chrome browser using JavaScript (if available)
      window.location.href = 'googlechrome://';
    });
  </script>
</body>
</html>`

async function courseUpdateNotification(users,courseID,courseThumbnail){
    try {
        notificationObject = {
            type: "update", 
            message: "Update Added to Course Content",
            link: `/classroom/gotoclassroom/${courseID}`,
            seen: false,
            thumbnail_url: courseThumbnail,
        }
        await User.updateMany(
            { _id: { $in: users } },
            { $push: { notifications: notificationObject } }
        );
        console.log("done updating notification");
    } catch (error) {
        console.error('Error updating documents:', error);
    }
}
async function sendEmail(users,course) {
    const accessToken = await oauth2Client.getAccessToken();
    const failedEmails = [];

    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        type: 'OAuth2',
        user: '<EMAIL>',
        clientId: CLIENT_ID,
        clientSecret: CLIENT_SECRET,
        refreshToken: REFRESH_TOKEN,
        accessToken: accessToken
      }
    });
  
    try {
      await Promise.all(users.map(async (user) => {
        const mailOptions = {
          from: '<EMAIL>',
          to: user.email,
          subject: `Invitation to Special Interaction Session on ${course.course_name} - Join us on Google Meet!`,
          text: `Dear ${user.name},

We hope this message finds you well. As a valued member of our learning community, we are excited to invite you to a special interaction session tailored exclusively for students of ${course.course_name}.
          
Date: 16/12/2023
Time: 9:15 PM
Platform: Google Meet

This session aims to provide you with a unique opportunity to engage, ask questions, and gain insights from instructors about the course material.

To join the session, simply click on the following Google Meet link: https://meet.google.com/ftt-vgtr-yov.

We look forward to your active participation and meaningful contributions during this session. If you have any specific topics or questions you'd like addressed, please feel free to reply to this email or submit them in advance.

Thank you for being a part of our educational journey. We can't wait to connect with you on 16th dec 2023!

Best regards,
Team Sheryians`

        //   html: `
        //   <!DOCTYPE html>
        //   <html lang="en">
        //   <head>
        //   <meta charset="UTF-8">
        //   <meta name="viewport" content="width=device-width, initial-scale=1.0">
        //   <title>Course Update Notification</title>
          
        //   </head>
        //   <body style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;line-height: 1.6;background-color: #f9f9f9;margin: 0;padding: 20px;">
        //   <div class="container" style="max-width: 600px;margin: auto;background: #fff;border-radius: 8px;box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);">
        //       <div class="header" style="background-color: #10a37f;color: #fff;border-radius: 8px 8px 0 0;padding: 20px;text-align: center;">
        //       <h1 style="margin: 0;font-size: 24px;">Course Update Notification</h1>
        //       </div>
        //       <div class="content" style="padding: 20px;text-align: left;">
        //       <p style="color: #555;font-size: 16px;">Hello ${user.name},</p>
        //       <p style="color: #555;font-size: 16px;">We are excited to inform you that the course content of <b>${course.course_name}</b> has been updated!</p>
        //       <p style="color: #555;font-size: 16px;">Please log in to your account to access the latest materials and resources.</p>
        //       <p style="color: #555;font-size: 16px;">If you have any questions or need further assistance, feel free to contact us on <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
        //       <br>
        //       <a style="text-decoration: none;color: white;display: inline-block;padding: 10px 20px;background-color: #10a37f;border-radius: 5px;transition: background-color 0.3s ease-in-out; font-weight:600;" href="https://sheryians.com/classroom/gotoclassroom/${course._id}" class="cta-button">Go to Course</a>
        //       </div>
        //       <div class="footer" style="padding: 20px;font-size: 14px;color: #777;">
        //       <p style="color: #555;font-size: 16px;">Best Regards,<br> Sheryians <br></p>
        //       </div>
        //   </div>
        //   </body>
        //   </html>
       
        //   `
        //   text: 'Dear user, we are excited to inform you that a new update has been added to our course. Check it out now!'
        };
        try {
          await transporter.sendMail(mailOptions);
        } catch (err) {
          failedEmails.push(user);
          console.log('Error sending email to:', user);
        }
      }));
  
    //   if (failedEmails.length > 0) {
    //     console.error(`Failed to send emails to:`, failedEmails);
    //   } else {
    //     console.log('Email notifications sent successfully.');
    //   }
    } catch (error) {
      console.error('Error sending email notifications:', error);
    }
  }
const getThumbnail = async (guid) => {
    try {
        const options = {
            method: 'GET',
            headers: {
                accept: 'application/json',
                'AccessKey': 'b6d68e08-0bb7-46a0-beb3f4d3a83c-9da4-4b46', // Replace with your Bunny.net API access key
            },
        };

        const response = await fetch(`https://video.bunnycdn.com/library/54717/videos/${guid}`, options);

        if (response.ok) {
            const videoDetails = await response.json();
            return videoDetails.thumbnail;
        } else {
            console.error('Error fetching video details:', response.statusText);
            return null; // Handle the error as needed
        }
    } catch (error) {
        console.error('Error fetching video details:', error);
        return null; // Handle any other errors here
    }
};

// Initialize the SDK with your API keys
const testdog = new testdogAI({
    accessKey: process.env.TESTDOG_AI_ACCESSKEY,     // Replace with your actual Access Key
    secretKey: process.env.TESTDOG_AI_SECRETKEY,     // Replace with your actual Secret Key
    // apiBaseUrl: 'https://your-api.testdog.com/api/v1' // Optional: Override API base URL
  });



module.exports.recorded_lecture_delete = async function (req, res) {
    try {
        let course = await classroom_content.findByIdAndDelete(req.body.id);
        return res.redirect("back");
    } catch (err) {
        console.log("Error", err);
        return;
    }
}

module.exports.recorded_lecture = async function (req, res) {
    try {
        let course = await Courses.find({ course: req.params.course });
        return res.render("recorded_lecture_admin", {
            course: course
        });
    } catch (err) {
        console.log("Error", err);
        return;
    }
}
module.exports.recorded_lecture_edit = async function (req, res) {
    try {
        let course = await classroom_content2.find({ courseId: req.params.id });
        let course_name = await Courses.findOne({ courseId: req.params.id });
        course.sort((a, b) => (a.order > b.order) ? 1 : -1)
        return res.render("recorded_lecture_edit", {
            all_course: course,
            course_name
        });
    } catch (err) {
        console.log("Error", err);
        return;
    }
}

module.exports.recorded_lecture_save = async function (req, res) {
    try {
        req.body.data.forEach(async function (element) {
            if (element.id) {
                await classroom_content.findByIdAndUpdate(element.id, { $set: { order: element.order, title: element.title, lectures: element.lectures } })
            } else {
                let module = classroom_content.create(element)
            }
        })

    } catch (err) {
        console.log("Error", err);
        return;
    }
}

async function fancyTimeFormat(duration) {
    // Hours, minutes and seconds
    const hrs = ~~(duration / 3600);
    const mins = ~~((duration % 3600) / 60);
    const secs = ~~duration % 60;

    // Output like "1:01" or "4:03:59" or "123:03:59"
    let ret = "";

    if (hrs > 0) {
        ret += "" + hrs + ":" + (mins < 10 ? "0" : "");
    }

    ret += "" + mins + ":" + (secs < 10 ? "0" : "");
    ret += "" + secs;

    return ret;
}

module.exports.gotoofflineclassroom = async function (req, res) {
    const userAgent = req.headers['user-agent']; // Get user-agent from request headers

    if (userAgent && userAgent.includes('Firefox')) {
        return res.status(403).send(unsupportBrowsersPage);
    }
    try {
        const offlineClass = await offline_class.findById(req.params.course_id);
        if(!offlineClass){
            return res.redirect(`/classroom`)
        }
        const course_id = offlineClass.reference_courseID
        const user = await User.findOne({ _id: req.user._id });

        const paidCourseId = user.feeStatus.map((crse)=>{ if(crse.status == "paid"){return crse.course_id} }).filter((data)=> data)
        if(!paidCourseId.includes(req.params.course_id)){
            return res.redirect(`/classroom`)
        }
        const allowedLectures = offlineClass.allowed_lectures
        let classroomContent = await classroom_content2_5.find({ courseId: course_id }).sort({ order: 1 });
        classroomContent = classroomContent.filter((data)=> allowedLectures.includes(data._id))
        const course = await Courses.findOne({ _id: course_id }).select('+enrolled_students');
        if (!course || !classroomContent) {
            return res.status(404).json({ error: 'Course not found' });
        }
        if(classroomContent.length == 0){
            return res.send("Content Coming Soon!")
        }
        if ((course.commingSoon || course.course_status == "inactive") && !user.admin && user.status != "instructor" && user.status != "mentor" && user.status != "superMentor") {
            return res.redirect('/courses');
        }
        let videoLectures = 0
        let videoLecturesCompleted = 0
        let AssesmentLectures = 0
        let AssesmentLecturesCompleted = 0
        const courseName = course.course_name;
        const courseEnrolledStudent = course.enrolled_students.filter((student) => student.status === 'paid').length;
        const courseId = course_id;
        // Create an array to store video details for each lecture
        const promises = [];
        let totalPoints = 0;
        let currentPoints = 0;
        let pdflinks = {}
        const courseTrack = user.classroom_track.length > 0 ? user.classroom_track.find(track => track.courseId.toString() === course_id): null;
        const lectureCompletionData = {};
        let totalCompletionPercentage = 0;
        if (courseTrack) {
            for (const completedLecture of courseTrack.completedLectures) {
                if(completedLecture.percentageCompleted == -2){
                    lectureCompletionData[completedLecture.lectureId.toString()] = 0;
                }else if(completedLecture.markedOption == -1 && completedLecture.percentageCompleted == 0){
                    lectureCompletionData[completedLecture.lectureId.toString()] = -1;
                }else{
                    lectureCompletionData[completedLecture.lectureId.toString()] = completedLecture.percentageCompleted;
                    totalCompletionPercentage += completedLecture.percentageCompleted;
                }
            }
        }

        // Loop through each module and lecture to fetch video details
        for (const moduleWrapper of classroomContent) {
            moduleWrapper.modules.sort((a, b) => a.order - b.order);
            for (const module of moduleWrapper.modules) {
                module.lectures.sort((a, b) => a.order - b.order);
                for (const lecture of module.lectures) {

                    const lectureId = lecture._id.toString();
                    const completionPercentage = lectureCompletionData[lectureId] ? lectureCompletionData[lectureId] : 0;
                    let points = 0;
                    totalPoints += Number(lecture.points ? lecture.points : 0);
                    if(completionPercentage > 0){
                        points = Number(lecture.points);
                        currentPoints += Number((completionPercentage/100) * points);
                    }
                    if(lecture.type == "video"){
                        videoLectures++
                        if(completionPercentage > 90) videoLecturesCompleted++
                    }else{
                        AssesmentLectures++
                        if(completionPercentage > 90 || (lectureCompletionData[lectureId] != undefined && completionPercentage == 0)) AssesmentLecturesCompleted++
                    }
                    if(lecture.type == "video"){
                        const videoId = lecture.guid;
                        // Check if the video details are already cached
                        const cachedVideoDetails = await cache.get(videoId);
                        

                        if (cachedVideoDetails) {
                            // Use the cached video details
                            promises.push(Promise.resolve({ lectureId: lecture._id, videoDetails: cachedVideoDetails, access: true, completionPercentage, points }));
                        } else {
                            // Fetch video details for the current videoId
                            const options = {
                                method: "GET",
                                headers: {
                                    Accept: "application/json",
                                    "Content-Type": "application/json",
                                    Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
                                },
                                json: true,
                            };
                            const promise = fetch(`https://dev.vdocipher.com/api/videos/${videoId}`, options)
                                .then((response) => response.json())
                                .then(async (videoDetails) => {
                                    videoDetails.duration = await fancyTimeFormat(videoDetails.length);
                                    // Cache the video details for future use
                                    cache.set(videoId, videoDetails);
                                    // Return the video details
                                    return { lectureId: lecture._id, videoDetails, access: true, completionPercentage, points };
                                })
                                .catch((err) => {
                                    if (lecture.comingSoon) {
                                        return { lectureId: lecture._id, videoDetails: null, access: true, completionPercentage: 0, points: 0};
                                    }
                                    console.error('Error fetching video details:', err);
                                    return { lectureId: lecture._id, videoDetails: null, completionPercentage: 0, points: 0};
                                });

                            promises.push(promise);
                        }
                    }  
                    else if(lecture.type == "pdf"){
                        pdflinks[lecture._id] = lecture.pdf
                    } 
                }
            }
        }
        let profilePicture = user.avatar ? user.avatar : 'https://ik.imagekit.io/sheryians/user_nJd7Nc9el.png';
        
        // Wait for all the promises to resolve
        const videoDetailsByLecture = await Promise.all(promises);
        const progressPercentage = Math.floor((currentPoints / totalPoints) * 100);
        const lastWatchedLecture = user && user.lastWatchedLecture ? user.lastWatchedLecture : null;
        res.render('gotoofflineclassroom', {batchId:offlineClass._id, course, pageTitle: "Course Detail",AssesmentLectures,AssesmentLecturesCompleted,videoLectures,videoLecturesCompleted, courseImage: course.image, classroomContent, videoDetailsByLecture, profilePicture, courseName, courseId, title: courseName, request: req, lastWatchedLecture, progressPercentage, fullAccess: true, totalPoints, currentPoints,pdflinks,lectureCompletionData});
    } catch (err) {
        console.error(err);
        res.status(500).send('Internal Server Error');
    }
}

module.exports.gotoclassroom = async function (req, res) {
    const userAgent = req.headers['user-agent']; // Get user-agent from request headers

    if (userAgent && userAgent.includes('Firefox')) {
        return res.status(403).send(unsupportBrowsersPage);
    }
    
    try {
        const {course_id} = req.params
        const checkOfllineCourse = await offline_class.find({reference_courseID:course_id})   
        if(checkOfllineCourse.length > 0){
            return res.redirect(`/classroom`)
        } 
        const user = await User.findOne({ _id: req.user._id });
        const findUserFeeStatus = user.feeStatus.find((course) => course.course_id == course_id);
        
        if (!findUserFeeStatus && !user.admin && user.status != "instructor" && user.status != "mentor" && user.status != "superMentor") {
            return res.redirect(`/courses/enroll/${course_id}`);
        }

        if (findUserFeeStatus && findUserFeeStatus.status == 'pending' && !user.admin && user.status != "instructor" && user.status != "mentor" && user.status != "superMentor") {
            return res.redirect(`/courses/enroll/${course_id}/payfee`);
        }

        const classroomContent = await classroom_content2_5.find({ courseId: course_id }).sort({ order: 1 });
        const course = await Courses.findOne({ _id: course_id }).select('+enrolled_students');
        if (!course || !classroomContent) {
            return res.status(404).json({ error: 'Course not found' });
        }
        if(classroomContent.length == 0){
            return res.send("Content Coming Soon!")
        }
        if ((course.commingSoon || course.course_status == "inactive") && !user.admin && user.status != "instructor") {
            return res.redirect('/courses');
        }
        let videoLectures = 0
        let videoLecturesCompleted = 0
        let AssesmentLectures = 0
        let AssesmentLecturesCompleted = 0
        const courseName = course.course_name;
        const courseEnrolledStudent = course.enrolled_students.filter((student) => student.status === 'paid').length;
        const courseId = course_id;
        // Create an array to store video details for each lecture
        const promises = [];
        let totalPoints = 0;
        let currentPoints = 0;
        let pdflinks = {}
        const courseTrack = user.classroom_track.length > 0 ? user.classroom_track.find(track => track.courseId.toString() === course_id): null;
        const lectureCompletionData = {};
        let totalCompletionPercentage = 0;
        if (courseTrack) {
            for (const completedLecture of courseTrack.completedLectures) {
                if(completedLecture.percentageCompleted == -2){
                    lectureCompletionData[completedLecture.lectureId.toString()] = 0;
                }else if(completedLecture.markedOption == -1 && completedLecture.percentageCompleted == 0){
                    lectureCompletionData[completedLecture.lectureId.toString()] = -1;
                }else{
                    lectureCompletionData[completedLecture.lectureId.toString()] = completedLecture.percentageCompleted;
                    totalCompletionPercentage += completedLecture.percentageCompleted;
                }
            }
        }

        // Loop through each module and lecture to fetch video details
        for (const moduleWrapper of classroomContent) {
            moduleWrapper.modules.sort((a, b) => a.order - b.order);
            for (const module of moduleWrapper.modules) {
                module.lectures.sort((a, b) => a.order - b.order);
                for (const lecture of module.lectures) {

                    const lectureId = lecture._id.toString();
                    const completionPercentage = lectureCompletionData[lectureId] ? lectureCompletionData[lectureId] : 0;
                    let points = 0;
                    totalPoints += Number(lecture.points ? lecture.points : 0);
                    if(completionPercentage > 0){
                        points = Number(lecture.points);
                        currentPoints += Number((completionPercentage/100) * points);
                    }
                    if(lecture.type == "video"){
                        videoLectures++
                        if(completionPercentage > 90) videoLecturesCompleted++
                    }else{
                        AssesmentLectures++
                        if(completionPercentage > 90 || (lectureCompletionData[lectureId] != undefined && completionPercentage == 0)) AssesmentLecturesCompleted++
                    }
                    if(lecture.type == "video"){
                        const videoId = lecture.guid;
                        // Check if the video details are already cached
                        const cachedVideoDetails = await cache.get(videoId);
                        

                        if (cachedVideoDetails) {
                            // Use the cached video details
                            promises.push(Promise.resolve({ lectureId: lecture._id, videoDetails: cachedVideoDetails, access: true, completionPercentage, points }));
                        } else {
                            // Fetch video details for the current videoId
                            const options = {
                                method: "GET",
                                headers: {
                                    Accept: "application/json",
                                    "Content-Type": "application/json",
                                    Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
                                },
                                json: true,
                            };
                            const promise = fetch(`https://dev.vdocipher.com/api/videos/${videoId}`, options)
                                .then((response) => response.json())
                                .then(async (videoDetails) => {
                                    videoDetails.duration = await fancyTimeFormat(videoDetails.length);
                                    // Cache the video details for future use
                                    cache.set(videoId, videoDetails);
                                    // Return the video details
                                    return { lectureId: lecture._id, videoDetails, access: true, completionPercentage, points };
                                })
                                .catch((err) => {
                                    if (lecture.comingSoon) {
                                        return { lectureId: lecture._id, videoDetails: null, access: true, completionPercentage: 0, points: 0};
                                    }
                                    console.error('Error fetching video details:', err);
                                    return { lectureId: lecture._id, videoDetails: null, completionPercentage: 0, points: 0};
                                });

                            promises.push(promise);
                        }
                    }  
                    else if(lecture.type == "pdf"){
                        pdflinks[lecture._id] = lecture.pdf
                    } 
                }
            }
        }
        let profilePicture = user.avatar ? user.avatar : 'https://ik.imagekit.io/sheryians/user_nJd7Nc9el.png';
        
        // Wait for all the promises to resolve
        const videoDetailsByLecture = await Promise.all(promises);
        const progressPercentage = Math.floor((currentPoints / totalPoints) * 100);
        const lastWatchedLecture = user && user.lastWatchedLecture ? user.lastWatchedLecture : null;
        res.render('gotoclassroom', { course, pageTitle: "Course Detail",AssesmentLectures,AssesmentLecturesCompleted,videoLectures,videoLecturesCompleted, courseImage: course.image, classroomContent, videoDetailsByLecture, profilePicture, courseName, courseId, title: courseName, request: req, lastWatchedLecture, progressPercentage, fullAccess: true, totalPoints, currentPoints,pdflinks,lectureCompletionData});
    } catch (err) {
        console.error(err);
        res.status(500).send('Internal Server Error');
    }
}

module.exports.tryforfree = async function (req, res) {
    const userAgent = req.headers['user-agent']; // Get user-agent from request headers

    // Check if the user-agent contains 'Firefox'
    if (userAgent && userAgent.includes('Firefox')) {
        return res.status(403).send(unsupportBrowsersPage);
    }
    try {
        const { course_id } = req.params;
        const classroomContent = await classroom_content2.find({ courseId: course_id }).sort({ order: 1 });
        const course = await Courses.findOne({ _id: course_id }).select('+enrolled_students');
        if (!course || !classroomContent) {
            return res.status(404).json({ error: 'Course not found' });
        }
        if (course.commingSoon || course.course_status == "inactive") {
            return res.redirect('/courses');
        }
        const courseName = course.course_name;
        const courseEnrolledStudent = course.enrolled_students.length;
        const courseId = course_id;
        // Create an array to store video details for each lecture
        const promises = [];
        const tryforfree_module = 2;
        let moduleCounter = 0;


        // Loop through each module and lecture to fetch video details
        for (const module of classroomContent) {
            module.lectures.sort((a, b) => a.order - b.order);
            let state = false;
            if (moduleCounter < tryforfree_module) {
                state = true;
            }
            moduleCounter++;
            for (const lecture of module.lectures) {
                const videoId = lecture.guid;

                // Check if the video details are already cached
                const cachedVideoDetails = await tryforfreeCache.get(videoId);

                if (cachedVideoDetails) {
                    // Use the cached video details
                    promises.push(Promise.resolve({ lectureId: lecture._id, videoDetails: cachedVideoDetails, access: state, completionPercentage: 0 }));
                } else {
                    // Fetch video details for the current videoId
                    const options = {
                        method: "GET",
                        headers: {
                            Accept: "application/json",
                            "Content-Type": "application/json",
                            Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
                        },
                        json: true,
                    };
                    const promise = fetch(`https://dev.vdocipher.com/api/videos/${videoId}`, options)
                        .then((response) => response.json())
                        .then(async (videoDetails) => {
                            videoDetails.duration = await fancyTimeFormat(videoDetails.length);
                            // Cache the video details for future use
                            tryforfreeCache.set(videoId, videoDetails);
                            const completionPercentage = 0;
                            // Return the video details
                            return { lectureId: lecture._id, videoDetails, access: state, completionPercentage };
                        })
                        .catch((err) => {
                            console.error('Error fetching video details:', err);
                            return { lectureId: lecture._id, videoDetails: null };
                        });

                    promises.push(promise);
                }
            }
        }
        let profilePicture = 'https://ik.imagekit.io/sheryians/user_nJd7Nc9el.png';

        // Wait for all the promises to resolve
        const videoDetailsByLecture = await Promise.all(promises);
        // Set the Cache-Control header to prevent caching
        res.set('Cache-Control', 'no-store');
        // Pass the structured data to the EJS template for rendering
        res.render('gotoclassroom', { course: false, pageTitle: "Course Detail", courseImage: course.image, classroomContent, videoDetailsByLecture, profilePicture, courseName, courseEnrolledStudent, courseId, title: courseName, request: req.user ? req : null, progressPercentage: 0 , fullAccess: false });
    } catch (err) {
        console.error(err);
        res.status(500).send('Internal Server Error');
    }
}

module.exports.gotoclassroomEdit = async function (req, res) {
        if (!req.user.admin && req.user.status != "superMentor" && req.user.status != "instructor") {
            return res.redirect(`/classroom/gotoclassroom/${req.params.course_id}`)
        }
        try {
            const { course_id } = req.params;
            const classroomContent = await classroom_content2_5.find({ courseId: course_id }).sort({ order: 1 });
            const course = await Courses.findOne({ _id: course_id }).select('+enrolled_students');
            if (!course || !classroomContent) {
                return res.status(404).json({ error: 'Course not found' });
            }
            const courseName = course.course_name;
            const courseEnrolledStudent = course.enrolled_students.length;
            const courseId = course_id;
            // Create an array to store video details for each lecture
            const promises = [];

            // Loop through each module and lecture to fetch video details
            for (const moduleWrapper of classroomContent) {
                moduleWrapper.modules.sort((a, b) => a.order - b.order);
                for (const module of moduleWrapper.modules) {
                    module.lectures.sort((a, b) => a.order - b.order);
                    for (const lecture of module.lectures) {

                        if(lecture.type == "video"){
                            const videoId = lecture.guid;
                            // Check if the video details are already cached
                            const cachedVideoDetails = await cache.get(videoId);
                            // Fetch video details for the current videoId
                            const options = {
                                method: "GET",
                                headers: {
                                    Accept: "application/json",
                                    "Content-Type": "application/json",
                                    Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
                                },
                                json: true,
                            };
                            const promise = fetch(`https://dev.vdocipher.com/api/videos/${videoId}`, options)
                                .then((response) => response.json())
                                .then(async (videoDetails) => {
                                    videoDetails.duration = await fancyTimeFormat(videoDetails.length);
                                    // Cache the video details for future use
                                    cache.set(videoId, videoDetails);
                                    // console.log('Fetched video details')
                                    // Return the video details
                                    return { lectureId: lecture._id, videoDetails };
                                })
                                .catch((err) => {
                                    console.error('Error fetching video details:', err);
                                    return { lectureId: lecture._id, videoDetails: null };
                                });

                            promises.push(promise);
                        }
                    }
                }
            }
            let profilePicture = '';
            const cachedProfilePicture = await usersProfilePicture.get(req.user._id.toString());
            const ProfilePicture = req.user?.avatar
            usersProfilePicture.set(req.user._id.toString(), ProfilePicture.dp);
            profilePicture = ProfilePicture.dp;
            // Wait for all the promises to resolve
            const videoDetailsByLecture = await Promise.all(promises);
            const problemsId = await codingQuestions.find().select("id title")
            // Pass the structured data to the EJS template for rendering
            res.render('gotoclassroomedit.ejs', { courseNotes:course.live_class_notes, pageTitle: "Classroom Admin", classroomContent, videoDetailsByLecture, profilePicture, courseName, courseEnrolledStudent, courseId, title: courseName, request: req, progressPercentage: 0, fullAccess: true,problemsId:JSON.stringify(problemsId), courseStatus: course.status });
        } catch (err) {
            console.error(err);
            res.status(500).send('Internal Server Error');
        }
    }

module.exports.getVideoDetails = async function (req, res) {
    try {
        const { lectureId, moduleId, moduleWrapperId } = req.params;
        const cacheKey = `${moduleId}_${lectureId}`;
        const offset = 0;
        const limit = 10;
        var result = await classroom_content2_5
        .findOne(
            { _id: moduleWrapperId,'modules._id': moduleId, 'modules.lectures._id': lectureId },
            // { _id: 1, courseId: 1, lectures: { $elemMatch: { _id: lectureId } } }
        )
        .populate({
            path: 'modules.lectures.comments',
            model: 'Comments_Classroom2',
            // options: { sort: { createdAt: -1 }},
        }); // Only populate 10 comments field

        if(!result){ return res.status(404).json({ error: 'Lecture not found' }); }
        const courseId = result.courseId;

        const lecture = result.modules.find(mod => mod._id.toString() == moduleId).lectures.find(lec => lec._id.toString() == lectureId)
        if (!lecture) {
            return res.status(404).json({ error: 'Lecture not found' });
        }
        // get original url from req
        const originalUrl = req.originalUrl;
        // check originalURL contain tryforfree or not
        const isTryForFree = originalUrl.includes('tryforfree');
        // check lecture id and module id is lies in tryforfree module or not
        let user = null;

        if (isTryForFree) {

            const classroomContent = await classroom_content2.find({ courseId: lecture.courseId }).sort({ order: 1 }).limit(2);
            const moduleTryForFree = classroomContent.find(module => module._id.toString() === moduleId.toString());
            if (!moduleTryForFree) {
                return res.status(403).json({ error: 'Access denied' });
            }
            const lectureTryForFree = moduleTryForFree.lectures.find(lecture => lecture._id.toString() === lectureId.toString());
            if (!lectureTryForFree) {
                return res.status(403).json({ error: 'Access denied' });
            }
        }else{
            if(req.user) {

                user = await User.findById(req.user._id);
                // check paid or not
                const findUserFeeStatus = user.feeStatus.find((course) => course.course_id == result.courseId);

                // if (!findUserFeeStatus && !user.admin) {
                //     return res.redirect(`/courses/enroll/${result.courseId}`);
                // }

                if (findUserFeeStatus && findUserFeeStatus.status == 'pending' && !user.admin && user.status != "instructor" && user.status != "mentor" && user.status != "superMentor") {
                    return res.redirect(`/courses/enroll/${result.courseId}/payfee`);
                }

            }
        } 


        const videoId = lecture.guid;
        if (lecture.newLecture) {
            user = await User.findById(req.user._id);
            if (!req.user) return console.log("user not found")
            if (!user) return console.log("user not found")
            if (!user.seenLecture.includes(lecture._id)) {
                user.seenLecture.push(lecture._id)
            }
            await user.save()
        }
        // Check if the video details are already in the cache
        if(lecture.type == "video"){

        const cachedVideoDetails = await cache.get(videoId);
        let bunnydata = {};
        if (cachedVideoDetails) {
            bunnydata = {
                title: cachedVideoDetails.title,
                description: (cachedVideoDetails.description.length > 0) ? cachedVideoDetails.description : 'No description!',
                views: lecture.views ? lecture.views : 0 ,
            }
            const options = {
                method: 'POST',
                headers: {
                    Accept: "application/json",
                    "Content-Type": "application/json",
                    Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
                },
                body: JSON.stringify({
                    ttl:300,
                    userId: req.user ? req.user._id.toString() : null,
                    annotate: JSON.stringify([
                        {
                            type: "rtext",
                            text: req.user && req.user.email ? req.user.email : req.sessionID,
                            alpha: "0.7",
                            color: "#ffffff",
                            size: "16",
                            interval: "7000",
                        },
                    ])}),
                json: true
            };
            const response = await fetch(`https://dev.vdocipher.com/api/videos/${videoId}/otp`, options);
            const videoDetails = await response.json();
            var iframeSrc = `https://player.vdocipher.com/v2/?otp=${videoDetails.otp}&playbackInfo=${videoDetails.playbackInfo}&startTime=10`
            bunnydata.iframeSrc = iframeSrc;
        } else {
            const options = {
                method: 'POST',
                headers: {
                    Accept: "application/json",
                    "Content-Type": "application/json",
                    Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
                },
                body: JSON.stringify({
                    ttl:300,
                    userId: req.user ? req.user._id.toString() : null,
                    annotate: JSON.stringify([
                        {
                            type: "rtext",
                            text: req.user && req.user.email ? req.user.email : req.sessionID,
                            alpha: "0.7",
                            color: "#ffffff",
                            size: "16",
                            interval: "7000",
                        },
                    ])}),
                json: true
            };
            const response = await fetch(`https://dev.vdocipher.com/api/videos/${videoId}/otp`, options);
            const videoDetails = await response.json();
            var iframeSrc = `https://player.vdocipher.com/v2/?otp=${videoDetails.otp}&playbackInfo=${videoDetails.playbackInfo}&startTime=10`
            
            // Fetch video details for the current videoId
            var options2 = {
                method: 'GET',
                headers: {
                    Accept: "application/json",
                    "Content-Type": "application/json",
                    Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
                },
            }
            
            const response2 = await fetch(`https://dev.vdocipher.com/api/videos/${videoId}`, options2);
            const videoDetails2 = await response2.json();
            const title = videoDetails2.title;
            const description = (videoDetails2.description.length > 0) ? videoDetails2.description : 'No description!';
            const views = lecture.views ? lecture.views : 0;
            bunnydata = {
                title: title,
                description: description,
                views: views
            }
            bunnydata.iframeSrc = iframeSrc;
            // Cache the video details for future use
            videoDetailsCache.set(cacheKey, bunnydata);
        }
        if (req.user) {
            // Find the index of the course in classroom_track
            let courseIndex = req.user.classroom_track.findIndex(track => track.courseId.toString() === courseId.toString());
            if(courseIndex === -1) {
                // If the course is not found in classroom_track, add it
                req.user.classroom_track.push({ courseId, completedLectures: [], completedModules: [] });
                courseIndex = req.user.classroom_track.length - 1;
            }
            // Find the completed lecture in the user's data
            const completedLecture = req.user.classroom_track[courseIndex].completedLectures.find(lecture => lecture.lectureId.toString() === lectureId.toString());

            let completionPercentage = 0; // Default completion percentage

            if (completedLecture) {   
                completionPercentage = completedLecture.percentageCompleted;
            }

            const data = {
                links: lecture.links,
                iframe: bunnydata.iframeSrc,
                title: bunnydata.title,
                description: bunnydata.description,
                views: bunnydata.views,
                completionPercentage: completionPercentage,
                courseId: courseId,
                userId: req.user._id,
                share: `share/${moduleWrapperId}/${moduleId}/${lectureId}`,
                //   mentioned_links: lecture.lectures[0].mentioned_links,
                likes: lecture.likes.length,
                isLiked: lecture.likes.includes(req.user._id.toString()),
                isDisliked: lecture.dislikes.includes(req.user._id),
                moreComments: lecture.comments.length > 10 ? true : false,
                Allcomments: lecture.comments.length,
                comments: lecture.comments.slice(-limit, lecture.comments.length).map(comment => {
                    return {
                        _id: comment._id,
                        user: comment.user,
                        username: comment.username,
                        image: comment.image,
                        content: comment.content,
                        createdAt: comment.createdAt,
                        likes: comment.likes.length,
                        isLiked: comment.likes.includes(req.user._id),
                        isDisliked: comment.dislikes.includes(req.user._id),
                        moreReplies: comment.replies.length > 10 ? true : false,
                        repliesArray: comment.replies.slice(0, limit).map(reply => {
                            return {
                                _id: reply._id,
                                user: reply.user,
                                username: reply.username,
                                image: reply.image,
                                content: reply.content,
                                createdAt: reply.createdAt,
                                likes: reply.likes.length,
                                isLiked: reply.likes.includes(req.user._id),
                                isDisliked: reply.dislikes.includes(req.user._id),
                                isCurrentUserComment: reply.user.toString() === req.user._id.toString()
                            };
                        }),
                        nreplies: comment.replies.length,
                        isCurrentUserComment: comment.user.toString() === req.user._id.toString()
                    };
                })
                    .sort((a, b) => b.createdAt - a.createdAt)
            };
            res.json(data);
        } else {
            const data = {
                links: lecture.links,
                iframe: bunnydata.iframeSrc,
                title: bunnydata.title,
                description: bunnydata.description,
                views: bunnydata.views,
                completionPercentage: 0,
                courseId: courseId,
                userId: null,
                share: `tryforfree`,
                //   mentioned_links: lecture.lectures[0].mentioned_links,
                likes: lecture.likes.length,
                isLiked: false,
                isDisliked: false,
                moreComments: lecture.comments.length > 10 ? true : false,
                Allcomments: lecture.comments.length,
                comments: lecture.comments.slice(-limit, lecture.comments.length).map(comment => {
                    return {
                        _id: comment._id,
                        user: comment.user,
                        username: comment.username,
                        image: comment.image,
                        content: comment.content,
                        createdAt: comment.createdAt,
                        likes: comment.likes.length,
                        isLiked: false,
                        isDisliked: false,
                        moreReplies: comment.replies.length > 10 ? true : false,
                        repliesArray: comment.replies.slice(0, limit).map(reply => {
                            return {
                                _id: reply._id,
                                user: reply.user,
                                username: reply.username,
                                image: reply.image,
                                content: reply.content,
                                createdAt: reply.createdAt,
                                likes: reply.likes.length,
                                isLiked: false,
                                isDisliked: false,
                                isCurrentUserComment: false
                            };
                        }),
                        nreplies: comment.replies.length,
                        isCurrentUserComment: false
                    };
                })
                    .sort((a, b) => b.createdAt - a.createdAt)
            };
            res.json(data);
        }
        }else if(lecture.type == "mcq"){
            if (req.user) {
                // Find the index of the course in classroom_track
                let courseIndex = req.user.classroom_track.findIndex(track => track.courseId.toString() === courseId.toString());
                if(courseIndex === -1) {
                    // If the course is not found in classroom_track, add it
                    req.user.classroom_track.push({ courseId, completedLectures: [], completedModules: [] });
                    courseIndex = req.user.classroom_track.length - 1;
                }
                // Find the completed lecture in the user's data
                const completedLecture = req.user.classroom_track[courseIndex].completedLectures.find(lecture => lecture.lectureId.toString() === lectureId.toString());

                let completionPercentage = 0; // Default completion percentage

                if (completedLecture) {   
                    completionPercentage = completedLecture.percentageCompleted;
                }

                const data = {
                    points:lecture.points,
                    title: lecture.title,
                    question: lecture.question,
                    questionImage: lecture.questionImage,
                    options: lecture.options,
                    explanation: lecture.explanation,
                    explanationVideo: lecture.explanationVideo,
                    links: lecture.links,
                    correctOption: lecture.correctOption,
                    codeSnippet: lecture.codeSnippet,
                    codeSnippetLanguage: lecture.codeSnippetLanguage,
                    completionPercentage: completionPercentage,
                    courseId: courseId,
                    userId: req.user._id,
                };
                res.json(data);
            } else {
                const data = {
                    points:lecture.points,
                    title: lecture.title,
                    question: lecture.question,
                    questionImage: lecture.questionImage,
                    options: lecture.options,
                    explanation: lecture.explanation,
                    explanationVideo: lecture.explanationVideo,
                    links: lecture.links,
                    completionPercentage: 0,
                    courseId: courseId,
                    codeSnippet: lecture.codeSnippet,
                    codeSnippetLanguage: lecture.codeSnippetLanguage,
                    correctOption: lecture.correctOption,
                    userId: null,
                };
                res.json(data);
            }
        }else if(lecture.type == "code"){
            if (req.user) {
                // Find the index of the course in classroom_track
                let courseIndex = req.user.classroom_track.findIndex(track => track.courseId.toString() === courseId.toString());
                if(courseIndex === -1) {
                    // If the course is not found in classroom_track, add it
                    req.user.classroom_track.push({ courseId, completedLectures: [], completedModules: [] });
                    courseIndex = req.user.classroom_track.length - 1;
                }
                // Find the completed lecture in the user's data
                const completedLecture = req.user.classroom_track[courseIndex].completedLectures.find(lecture => lecture.lectureId.toString() === lectureId.toString());

                let completionPercentage = 0; // Default completion percentage

                if (completedLecture) {   
                    completionPercentage = completedLecture.percentageCompleted;
                }

                const data = {
                    points:lecture.points,
                    problemId: lecture.problemId,
                    problem_name: lecture.problem_name,
                    main_code: lecture.main_code,
                    code_template: lecture.code_template,
                    solution_code: lecture.solution_code,
                    compilerId: lecture.compilerId,
                    difficulty: lecture.difficulty,
                    links: lecture.links,
                    completionPercentage: completionPercentage,
                    courseId: courseId,
                    code: lecture.code,
                    userId: req.user._id,
                };
                res.json(data);
            } else {
                const data = {
                    points:lecture.points,
                    problemId: lecture.problemId,
                    problem_name: lecture.problem_name,
                    main_code: lecture.main_code,
                    code_template: lecture.code_template,
                    solution_code: lecture.solution_code,
                    compilerId: lecture.compilerId,
                    difficulty: lecture.difficulty,
                    links: lecture.links,
                    completionPercentage: 0,
                    courseId: courseId,
                    code: lecture.code,
                    userId: req.user._id,
                    userId: null,
                };
                res.json(data);
            }
        }

    } catch (err) {
        console.error('Error fetching video details:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

// Rate limiting setup (change options as per your requirement)
const viewLimiter = rateLimit({
    windowMs: 30 * 1000, // 30 seconds
    max: 1, // Limit each IP to 5 requests per windowMs
    message: 'Too many requests, please try again later.',
    keyGenerator: function (req) {
        return req.get('do-connecting-ip') || req.ip;
    },
});

const parseForm = (req) => {
    return new Promise((resolve, reject) => {
      const form = new formidable.IncomingForm();
  
      form.parse(req, (err, fields, files) => {
        if (err) {
          return reject(err);
        }
        resolve({ fields, files });
      });
    });
  };

module.exports.addMcqLectures = async (req, res) => {
    if (!req.user.admin && req.user.status != "superMentor" && req.user.status != "instructor" ) {
        return res.redirect(`/classroom/gotoclassroom/${req.params.course_id}`)
    }
    try{
        var result
        const { fields, files } = await parseForm(req);
        const { title, question, explaination, marks, lectureId, codeSnippet, codeSnippetLanguage, courseId, wrapperId, moduleId, correctOption } = fields;
        const choice = JSON.parse(fields.choice);
        if (files.file) {
            const filePath = files.file[0].filepath;
            result = await imagekit.upload({
              file: fs.createReadStream(filePath),
              fileName: files.file[0].newFilename
            });
        }  

        if (files.questionImage) {
            const filePath = files.questionImage[0].filepath;
            questionImageresult = await imagekit.upload({
              file: fs.createReadStream(filePath),
              fileName: files.questionImage[0].newFilename
            });
        }     

        const moduleWrapper = await classroom_content2_5.findById(wrapperId[0])
        if(!moduleWrapper) return res.status(400).json({message:"ModuleWrapper not found"})
        const module = moduleWrapper.modules.find(module => module._id.toString() == moduleId[0].toString())
        if(!module) return res.status(400).json({message:"Module not found"})



        if(lectureId[0]){
            const lecture = module.lectures.find(lec => lec._id.toString() == lectureId[0].toString())
            if(!lecture) return res.status(400).json({message:"lecture not found"})
            lecture.courseId = courseId[0]
            lecture.title = title[0]
            lecture.explanation = explaination[0]
            lecture.explanationVideo = files.file ? result.url : lecture.explanationVideo
            lecture.points = marks[0]
            lecture.options = choice
            lecture.correctOption = correctOption[0]
            lecture.question = question[0]
            lecture.questionImage = files.questionImage ? questionImageresult.url : lecture.questionImage
            lecture.codeSnippet = codeSnippet[0]
            lecture.codeSnippetLanguage = codeSnippetLanguage[0]
            await moduleWrapper.save()
        }else{
            module.lectures.push({
                type:"mcq",
                title : title[0],
                order:module.lectures.length+1,
                courseId: courseId[0],
                explanation : explaination[0],
                explanationVideo : files.file ? result.url : "",
                points : marks[0],
                options : choice,
                correctOption : correctOption[0],
                questionImage : files.questionImage ? questionImageresult.url : "",
                question : question[0],
                codeSnippetLanguage: codeSnippetLanguage[0],
                codeSnippet: codeSnippet[0]
            })
        }
        moduleWrapper.markModified("modules")
        moduleWrapper.markModified("lectures")
        await moduleWrapper.save()
        res.status(200).json({ message: "success"})
    }catch(err){
        console.log(err)
        res.status(400).json({ message: "Oops! Something went wrong, please report it to Anurag"})
    }
}

module.exports.addCodeLectures = async (req, res) => {
    if (!req.user.admin && req.user.status != "superMentor" && req.user.status != "instructor") {
        return res.redirect(`/classroom/gotoclassroom/${req.params.course_id}`)
    }
    try{
        const {lectureId,wrapperId,moduleId,problemId, problem_name,points, main_code, code_template, solution_code, compilerId, difficulty,code}  = req.body
        const moduleWrapper = await classroom_content2_5.findById(wrapperId)
        if(!moduleWrapper) return res.status(400).json({message:"ModuleWrapper not found"})
        const module = moduleWrapper.modules.find(module => module._id.toString() == moduleId.toString())
        if(!module) return res.status(400).json({message:"Module not found"})
        if(lectureId){
            const lecture = module.lectures.find(lec => lec._id.toString() == lectureId.toString())
            if(!lecture) return res.status(400).json({message:"lecture not found"})
            lecture.problemId = problemId
            lecture.problem_name = problem_name
            lecture.main_code = main_code
            lecture.code_template = code_template
            lecture.solution_code = solution_code
            lecture.difficulty = difficulty
            lecture.points = points
            lecture.code = code
            await moduleWrapper.save()
        }else{
            module.lectures.push({
                type:"code",
                problemId :problemId,
                problem_name:problem_name,
                main_code: main_code,
                code_template :code_template,
                solution_code :solution_code,
                difficulty :difficulty,
                points:points,
                order:module.lectures.length+1,
                code: code
            })
        }
        moduleWrapper.markModified("modules")
        moduleWrapper.markModified("lectures")
        await moduleWrapper.save()
        res.status(200).json({ message: "success"})
    }catch(err){
        console.log(err)
        res.status(400).json({ message: "Oops! Something went wrong, please report it to Anurag"})
    }
}

module.exports.postSubmitMCQ = async function (req, res) {

    const {choosenOption,id} = req.body
    const {moduleId,lectureId} = req.params
    if(!([1,2,3,4].includes(choosenOption))) return res.status(404).json({message:"Not a valid option"})
    if(!moduleId || !lectureId) return res.status(404).json({message:"Missing Fields"})
    
    const userData = await User.findById(req.user._id)
    if(!userData) return res.status(404).json({message:"User not found"})

    var result = await classroom_content2_5.findOne({'modules.lectures._id': lectureId })
    if(!result) return res.status(404).json({message:"Module not found"})

    const courseId = result.courseId
    const lecture = result.modules.find(modID => modID._id.toString() == moduleId.toString()).lectures.find(lecID =>lecID._id.toString() == lectureId.toString())
    if(!lecture) return res.status(404).json({message:"Lecture not found"})

    const classroom_track = userData.classroom_track.find(id => id.courseId.toString() == courseId.toString())
    if(classroom_track){
        const classroom_track_lecture = classroom_track.completedLectures.find(lecId => lecId.lectureId.toString() == lectureId.toString())
        if(classroom_track_lecture){
            classroom_track_lecture.markedOption = Number(choosenOption)
            classroom_track_lecture.percentageCompleted = choosenOption == lecture.correctOption ? 100 : 0
        }else{
            classroom_track.completedLectures.push({
                lectureId:lectureId,
                markedOption:id,
                percentageCompleted:choosenOption == lecture.correctOption ? 100 : 0
            })
        }
    }else{
        userData.classroom_track.push({
            courseId: courseId,
            completedLectures: [{
                lectureId:lectureId,
                markedOption:id,
                percentageCompleted:choosenOption == lecture.correctOption ? 100 : 0
            }],
            completedModules: []
        })
    }
    await userData.save()
    return res.status(200).json({correct:choosenOption == lecture.correctOption ? true : false,correctOption:lecture.correctOption,points:lecture.points,id:lecture._id})
}

module.exports.postSubmitPDF = async function (req, res) {
    try{
        const {lectureId,moduleId} = req.params
        if(!lectureId || !moduleId) return res.status(404).json({message:"Missing Fields"})

        const userData = await User.findById(req.user._id)
        if(!userData) return res.status(404).json({message:"User not found"})

        var result = await classroom_content2_5.findOne({'modules.lectures._id': lectureId })
        if(!result) return res.status(404).json({message:"Module not found"})

        const courseId = result.courseId
        const lecture = result.modules.find(modID => modID._id.toString() == moduleId.toString()).lectures.find(lecID =>lecID._id.toString() == lectureId.toString())
        if(!lecture) return res.status(404).json({message:"Lecture not found"})
        
        const classroom_track = userData.classroom_track.find(id => id.courseId.toString() == courseId.toString())
        if(classroom_track){
            const classroom_track_lecture = classroom_track.completedLectures.find(lecId => lecId.lectureId.toString() == lectureId.toString())
            if(classroom_track_lecture){
                if(classroom_track_lecture.percentageCompleted == 100) return res.status(200).json({message:"Lecture Already completed",lecture})
                classroom_track_lecture.percentageCompleted = 100
            }else{
                classroom_track.completedLectures.push({
                    lectureId:lectureId,
                    percentageCompleted:100
                })
            }
        }else{
            userData.classroom_track.push({
                courseId: courseId,
                completedLectures: [{
                    lectureId:lectureId,
                    percentageCompleted:100
                }],
                completedModules: []
            })
        }
        await userData.save()
        return res.status(200).json({message:"success",lecture})
    }catch(err){
        console.log(err)
    }
}

module.exports.getMCQDetails = async function (req, res) {
    try {
        const userData = await User.findById(req.user._id)
        if(!userData) return res.status(400).json({ message: 'User not found' });

        const { lectureId, moduleId, moduleWrapperId } = req.params;
        if(!lectureId || !moduleId || !moduleWrapperId) return res.status(400).json({ message: 'Missing Fields' });
        
        const result = await classroom_content2_5
        .findOne(
            { _id: moduleWrapperId,'modules._id': moduleId, 'modules.lectures._id': lectureId },
        )
        const findUserFeeStatus = userData.feeStatus.find((course) => course.course_id == result.courseId);

        if (!findUserFeeStatus && !userData.admin && !userData.status == "instructor") {
            return res.json({message:"Please enroll to access this content"})
        }

        if (findUserFeeStatus && findUserFeeStatus.status == 'pending' && !userData.admin && !userData.status == "instructor") {
            return res.json({message:"Please pay the fee to access this content"})
        }
        const courseId = result.courseId
        const course = await Courses.findById(courseId)
        const classroom_track = userData.classroom_track.find(id => id.courseId.toString() == courseId.toString())
        const lecture = result.modules.find(modID => modID._id.toString() == moduleId.toString()).lectures.find(lecID =>lecID._id.toString() == lectureId.toString())
        if (lecture.newLecture) {
            if (!userData.seenLecture.includes(lecture._id.toString())) {
                userData.seenLecture.push(lecture._id.toString())
            }
        }
        if(classroom_track) {
            const classroom_track_lecture = classroom_track.completedLectures.find(lecId => lecId.lectureId.toString() == lectureId.toString())
            if(!classroom_track_lecture){
                classroom_track.completedLectures.push({
                    lectureId:lectureId,
                    markedOption: -1,
                    percentageCompleted:0
                })
            }else{
                if(classroom_track_lecture.markedOption == -1){
                    await userData.save()
                    return res.render("mcq.ejs",{question:lecture.question,options:lecture.options,points:lecture.points,title:lecture.title,lectureId:lecture._id,explanation:lecture.explanation,questionImage:lecture.questionImage,explanationVideo:lecture.explanationVideo,moduleId,markedOption:false,correctOption:false,codeSnippet:lecture.codeSnippet,codeSnippetLanguage:lecture.codeSnippetLanguage,discord_role_id:course.discord_role_id,discord_channel_id:course.discord_channel_id,userId:req.user._id})
                }else{
                    await userData.save()
                    return res.render("mcq.ejs",{question:lecture.question,options:lecture.options,points:lecture.points,title:lecture.title,lectureId:lecture._id,explanation:lecture.explanation,questionImage:lecture.questionImage,explanationVideo:lecture.explanationVideo,moduleId,markedOption:classroom_track_lecture.markedOption,correctOption:lecture.correctOption,codeSnippet:lecture.codeSnippet,codeSnippetLanguage:lecture.codeSnippetLanguage,discord_role_id:course.discord_role_id,discord_channel_id:course.discord_channel_id,userId:req.user._id})
                }
            }
        }else{
            userData.classroom_track.push({
                courseId: courseId,
                completedLectures: [{
                    lectureId:lectureId,
                    markedOption: -1,
                    percentageCompleted:0
                }],
                completedModules: []
            })
        }
        await userData.save()
        return res.render("mcq.ejs",{question:lecture.question,options:lecture.options,points:lecture.points,title:lecture.title,lectureId:lecture._id,explanation:lecture.explanation,questionImage:lecture.questionImage, explanationVideo:lecture.explanationVideo,moduleId,markedOption:false,correctOption:false,codeSnippet:lecture.codeSnippet,codeSnippetLanguage:lecture.codeSnippetLanguage,discord_role_id:course.discord_role_id,discord_channel_id:course.discord_channel_id,userId:req.user._id})
    } catch (err) {
        console.error('Error fetching video details:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.getCodeDetails = async function (req, res) {
    try {
        const userData = await User.findById(req.user._id)
        if(!userData) return res.status(400).json({ message: 'User not found' });

        const { lectureId, moduleId, moduleWrapperId } = req.params;
        if(!lectureId || !moduleId || !moduleWrapperId) return res.status(400).json({ message: 'Missing Fields' });
        
        const result = await classroom_content2_5
        .findOne(
            { _id: moduleWrapperId,'modules._id': moduleId, 'modules.lectures._id': lectureId },
        )
        const findUserFeeStatus = userData.feeStatus.find((course) => course.course_id == result.courseId);
        if (findUserFeeStatus && findUserFeeStatus.status == 'pending' && !userData.admin && !userData.status == "instructor") {
            return res.json({message:"Please pay the fee to access this content"})
        }
        let seenSolution = false
        let completed = false
        let submission = await submissionStorage.find({userId:req.user._id,questionId:lectureId})
        const codeMap = submission.reduce((acc, sub) => {
            acc[sub.language] = sub.code;
            return acc;
        }, {});
        console.log(codeMap); 
        const courseId = result.courseId 
        const course = await Courses.findById(courseId) 
        const classroom_track = userData.classroom_track.find(id => id.courseId.toString() == courseId.toString())
        let lecture = result.modules.find(modID => modID._id.toString() == moduleId.toString()).lectures.find(lecID =>lecID._id.toString() == lectureId.toString())
        let problem = await codingQuestions.findOne({id:lecture.problemId})
        if (lecture.newLecture) {
            if (!userData.seenLecture.includes(lecture._id.toString())) {
                userData.seenLecture.push(lecture._id.toString())
            }
        }
        if(classroom_track) {
            const classroom_track_lecture = classroom_track.completedLectures.find(lecId => lecId.lectureId.toString() == lectureId.toString())
            if(!classroom_track_lecture){
                classroom_track.completedLectures.push({
                    lectureId:lectureId,
                    percentageCompleted:-2
                })
            }
            if(classroom_track_lecture && classroom_track_lecture.freezeMarks) seenSolution = true
            if(classroom_track_lecture && classroom_track_lecture.percentageCompleted == 100) completed = true
        }else{
            userData.classroom_track.push({
                courseId: courseId,
                completedLectures: [{
                    lectureId:lectureId,
                    percentageCompleted:-2
                }],
                completedModules: []
            })
        }
        await userData.save()
        lecture = {
            main_code:lecture.main_code,
            code_template:lecture.code_template,
            type:lecture.type,
            order:lecture.order,
            problem_name:lecture.problem_name,
            compilerId:lecture.compilerId,
            difficulty:lecture.difficulty,
            comingSoon:lecture.comingSoon,
            newLecture:lecture.newLecture,
            points:lecture.points,
            codeData: lecture.code instanceof Map
                ? Object.fromEntries(
                    Array.from(lecture.code?.entries?.() || []).map(([key, value]) => {
                        const { solutionCode, ...rest } = value?.toObject ? value.toObject() : value || {};
                        return [key, rest];
                    })
                    )
                : Object.fromEntries(
                    Object.entries(lecture.code || {}).map(([key, value]) => {
                        const { solutionCode, ...rest } = value || {};
                        return [key, rest];
                    })
                ),         
            _id:lecture._id,
        }
        console.log(codeMap);
        return res.render("codingLecture.ejs",{completed,seenSolution,code:codeMap,lecture,problem,moduleId,lectureId,courseId,discord_role_id:course.discord_role_id,discord_channel_id:course.discord_channel_id,userId:req.user._id})
    } catch (err) {
        console.error('Error fetching video details:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.getPDFDetails = async function (req, res) {
    try {
        const userData = await User.findById(req.user._id)
        if(!userData) return res.status(400).json({ message: 'User not found' });
        const { lectureId, moduleId, moduleWrapperId } = req.params;
        if(!lectureId || !moduleId || !moduleWrapperId) return res.status(400).json({ message: 'Missing Fields' });
        const result = await classroom_content2_5
        .findOne(
            { _id: moduleWrapperId,'modules._id': moduleId, 'modules.lectures._id': lectureId },
        )
        const courseId = result.courseId
        const lecture = result.modules.find(modID => modID._id.toString() == moduleId.toString()).lectures.find(lecID =>lecID._id.toString() == lectureId.toString())
        
        if (lecture.newLecture) {
            if (!userData.seenLecture.includes(lecture._id.toString())) {
                userData.seenLecture.push(lecture._id.toString())
            }
        }
        await userData.save()
        return res.render("pdf.ejs",{lecture})
    } catch (err) {
        console.error('Error fetching video details:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.getOfflineMCQDetails = async function (req, res) {
    try {
        const userData = await User.findById(req.user._id)
        if(!userData) return res.status(400).json({ message: 'User not found' });

        const paidIds = userData.feeStatus.map((crse)=>{ if(crse.status == "paid"){return crse.course_id} }).filter((data)=> data)
        
            if(!paidIds.includes(req.params.batchId)){
            return res.json({message:"Please pay the fee to access this content"})
        }

        const { lectureId, moduleId, moduleWrapperId } = req.params;
        if(!lectureId || !moduleId || !moduleWrapperId) return res.status(400).json({ message: 'Missing Fields' });
        
        const result = await classroom_content2_5
        .findOne(
            { _id: moduleWrapperId,'modules._id': moduleId, 'modules.lectures._id': lectureId },
        )
        // const paidCourseId = userData.feeStatus.map((crse)=>crse.feeStatus == "paid" && crse.course_id)
        
        // if (!findUserFeeStatus && !userData.admin) {
        //     return res.json({message:"Please enroll to access this content"})
        // }

        // if (findUserFeeStatus && findUserFeeStatus.status == 'pending' && !userData.admin) {
        //     return res.json({message:"Please pay the fee to access this content"})
        // }
        const courseId = result.courseId
        const course = await Courses.findById(courseId)
        const classroom_track = userData.classroom_track.find(id => id.courseId.toString() == courseId.toString())
        const lecture = result.modules.find(modID => modID._id.toString() == moduleId.toString()).lectures.find(lecID =>lecID._id.toString() == lectureId.toString())
        if (lecture.newLecture) {
            if (!userData.seenLecture.includes(lecture._id.toString())) {
                userData.seenLecture.push(lecture._id.toString())
            }
        }
        if(classroom_track) {
            const classroom_track_lecture = classroom_track.completedLectures.find(lecId => lecId.lectureId.toString() == lectureId.toString())
            if(!classroom_track_lecture){
                classroom_track.completedLectures.push({
                    lectureId:lectureId,
                    markedOption: -1,
                    percentageCompleted:0
                })
            }else{
                if(classroom_track_lecture.markedOption == -1){
                    await userData.save()
                    return res.render("mcq.ejs",{question:lecture.question,options:lecture.options,points:lecture.points,title:lecture.title,lectureId:lecture._id,explanation:lecture.explanation,questionImage:lecture.questionImage,explanationVideo:lecture.explanationVideo,moduleId,markedOption:false,correctOption:false,codeSnippet:lecture.codeSnippet,codeSnippetLanguage:lecture.codeSnippetLanguage,discord_role_id:course.discord_role_id,discord_channel_id:course.discord_channel_id,userId:req.user._id})
                }else{
                    await userData.save()
                    return res.render("mcq.ejs",{question:lecture.question,options:lecture.options,points:lecture.points,title:lecture.title,lectureId:lecture._id,explanation:lecture.explanation,questionImage:lecture.questionImage,explanationVideo:lecture.explanationVideo,moduleId,markedOption:classroom_track_lecture.markedOption,correctOption:lecture.correctOption,codeSnippet:lecture.codeSnippet,codeSnippetLanguage:lecture.codeSnippetLanguage,discord_role_id:course.discord_role_id,discord_channel_id:course.discord_channel_id,userId:req.user._id})
                }
            }
        }else{
            userData.classroom_track.push({
                courseId: courseId,
                completedLectures: [{
                    lectureId:lectureId,
                    markedOption: -1,
                    percentageCompleted:0
                }],
                completedModules: []
            })
        }
        await userData.save()
        return res.render("mcq.ejs",{question:lecture.question,options:lecture.options,points:lecture.points,title:lecture.title,lectureId:lecture._id,explanation:lecture.explanation,questionImage:lecture.questionImage, explanationVideo:lecture.explanationVideo,moduleId,markedOption:false,correctOption:false,codeSnippet:lecture.codeSnippet,codeSnippetLanguage:lecture.codeSnippetLanguage,discord_role_id:course.discord_role_id,discord_channel_id:course.discord_channel_id,userId:req.user._id})
    } catch (err) {
        console.error('Error fetching video details:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.getOfflineCodeDetails = async function (req, res) {
    try {
        const userData = await User.findById(req.user._id)
        if(!userData) return res.status(400).json({ message: 'User not found' });

        const paidIds = userData.feeStatus.map((crse)=>{ if(crse.status == "paid"){return crse.course_id} }).filter((data)=> data)
        if(!paidIds.includes(req.params.batchId)){
            return res.json({message:"Please pay the fee to access this content"})
        }

        const { lectureId, moduleId, moduleWrapperId } = req.params;
        if(!lectureId || !moduleId || !moduleWrapperId) return res.status(400).json({ message: 'Missing Fields' });
        
        const result = await classroom_content2_5
        .findOne(
            { _id: moduleWrapperId,'modules._id': moduleId, 'modules.lectures._id': lectureId },
        )
        // const paidCourseId = userData.feeStatus.map((crse)=>crse.feeStatus == "paid" && crse.course_id)
       
        // if (findUserFeeStatus && findUserFeeStatus.status == 'pending' && !userData.admin) {
        //     return res.json({message:"Please pay the fee to access this content"})
        // }
        let seenSolution = false
        let completed = false
        const submission = await submissionStorage.findOne({userId:req.user._id,questionId:lectureId})
        const courseId = result.courseId
        const course = await Courses.findById(courseId)
        const classroom_track = userData.classroom_track.find(id => id.courseId.toString() == courseId.toString())
        let lecture = result.modules.find(modID => modID._id.toString() == moduleId.toString()).lectures.find(lecID =>lecID._id.toString() == lectureId.toString())
        let problem = await codingQuestions.findOne({id:lecture.problemId})
        if (lecture.newLecture) {
            if (!userData.seenLecture.includes(lecture._id.toString())) {
                userData.seenLecture.push(lecture._id.toString())
            }
        }
        if(classroom_track) {
            const classroom_track_lecture = classroom_track.completedLectures.find(lecId => lecId.lectureId.toString() == lectureId.toString())
            if(!classroom_track_lecture){
                classroom_track.completedLectures.push({
                    lectureId:lectureId,
                    percentageCompleted:-2
                })
            }
            if(classroom_track_lecture && classroom_track_lecture.freezeMarks) seenSolution = true
            if(classroom_track_lecture && classroom_track_lecture.percentageCompleted == 100) completed = true
        }else{
            userData.classroom_track.push({
                courseId: courseId,
                completedLectures: [{
                    lectureId:lectureId,
                    percentageCompleted:-2
                }],
                completedModules: []
            })
        }
        await userData.save()
        lecture = {
            main_code:lecture.main_code,
            code_template:lecture.code_template,
            type:lecture.type,
            order:lecture.order,
            problem_name:lecture.problem_name,
            compilerId:lecture.compilerId,
            difficulty:lecture.difficulty,
            comingSoon:lecture.comingSoon,
            newLecture:lecture.newLecture,
            points:lecture.points,
            _id:lecture._id,
        }
        return res.render("codingLecture.ejs",{completed,seenSolution,code:submission ? submission.code : "",lecture,problem,moduleId,lectureId,courseId,discord_role_id:course.discord_role_id,discord_channel_id:course.discord_channel_id,userId:req.user._id})
    } catch (err) {
        console.error('Error fetching video details:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.getOfflinePDFDetails = async function (req, res) {
    try {
        const userData = await User.findById(req.user._id)
        if(!userData) return res.status(400).json({ message: 'User not found' });
        const { lectureId, moduleId, moduleWrapperId } = req.params;
        if(!lectureId || !moduleId || !moduleWrapperId) return res.status(400).json({ message: 'Missing Fields' });
        const result = await classroom_content2_5
        .findOne(
            { _id: moduleWrapperId,'modules._id': moduleId, 'modules.lectures._id': lectureId },
        )
        const paidIds = userData.feeStatus.map((crse)=>{ if(crse.status == "paid"){return crse.course_id} }).filter((data)=> data)
        if(!paidIds.includes(req.params.batchId)){
            return res.json({message:"Please pay the fee to access this content"})
        }        
        const courseId = result.courseId
        const lecture = result.modules.find(modID => modID._id.toString() == moduleId.toString()).lectures.find(lecID =>lecID._id.toString() == lectureId.toString())
        
        if (lecture.newLecture) {
            if (!userData.seenLecture.includes(lecture._id.toString())) {
                userData.seenLecture.push(lecture._id.toString())
            }
        }
        await userData.save()
        return res.render("pdf.ejs",{lecture})
    } catch (err) {
        console.error('Error fetching video details:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}



module.exports.updateViews = [viewLimiter, async function (req, res) {
    const {moduleWrapperId, lectureId, moduleId } = req.params;
    console.log('Updated views');
    try {
        // Find the module containing the lecture
        const module = await classroom_content2_5.findOne({
            _id: moduleWrapperId
        });

        if (!module) {
            return res.status(404).json({ error: 'Module or Lecture not found' });
        }
        const lecture = module.modules.find(mod => mod._id.toString() == moduleId).lectures.find(lec => lec._id.toString() == lectureId)
        // Find the lecture inside the module
        // const lecture = module.lectures.id(lectureId);

        if (!lecture) {
            return res.status(404).json({ error: 'Lecture not found' });
        }

        // update the views 
        lecture.views += 1;
        await module.save();
        res.json({ views: lecture.views });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}]

module.exports.moreReplies = async function (req, res) {
    try {
        // tr{req.paramonsole.log("more replies")
        const { commentId } = req.params;
        const comment = await Comments_Classroom2.findById(commentId)
        const limit = 10;
        if (!comment) {
            return res.status(404).json({ error: 'comment not found' });
        }
        if (req.user) {
            const data = {
                moreReplies: true,
                repliesArray: comment.replies.slice(0 + req.body.repliesNumber, limit + req.body.repliesNumber > comment.replies.length ? comment.replies.length : limit + req.body.repliesNumber).map(reply => {
                    return {
                        _id: reply._id,
                        user: reply.user,
                        username: reply.username,
                        image: reply.image,
                        content: reply.content,
                        createdAt: reply.createdAt,
                        likes: reply.likes.length,
                        isLiked: reply.likes.includes(req.user._id),
                        isDisliked: reply.dislikes.includes(req.user._id),
                        isCurrentUserComment: reply.user.toString() === req.user._id.toString(),
                    };
                }),
            }
            if (req.body.repliesNumber + 10 >= comment.replies.length) {
                data.moreReplies = false
            }

            res.json(data)
        } else {
            const data = {
                moreReplies: true,
                repliesArray: comment.replies.slice(0 + req.body.repliesNumber, limit + req.body.repliesNumber > comment.replies.length ? comment.replies.length : limit + req.body.repliesNumber).map(reply => {
                    return {
                        _id: reply._id,
                        user: reply.user,
                        username: reply.username,
                        image: reply.image,
                        content: reply.content,
                        createdAt: reply.createdAt,
                        likes: reply.likes.length,
                        isLiked: false,
                        isDisliked: false,
                        isCurrentUserComment: false,
                    };
                }),
                nreplies: comment.replies.length,
                isCurrentUserComment: true
            };
            if (req.body.repliesNumber + 10 >= comment.replies.length) {
                data.moreReplies = false
            }
            res.json(data)
        }

    } catch (err) {

    }
}
module.exports.moreComments = async function (req, res) {
    try {
        const {moduleWrapperId, lectureId, moduleId } = req.params;
        const limit = 10;
        const offset = 0;
        var result = await classroom_content2_5
        .findOne(
            { _id: moduleWrapperId,'modules._id': moduleId, 'modules.lectures._id': lectureId },
            // { _id: 1, courseId: 1, lectures: { $elemMatch: { _id: lectureId } } }
        )
        .populate({
            path: 'modules.lectures.comments',
            model: 'Comments_Classroom2',
            // options: { sort: { createdAt: -1 }},
        }); // Only populate 10 comments field

        const lecture = result.modules.find(mod => mod._id.toString() == moduleId).lectures.find(lec => lec._id.toString() == lectureId)

        if (!lecture) {
            return res.status(404).json({ error: 'Lecture not found' });
        }
        // Check if the video details are already in the cache
        if (req.user) {
            const data = {
                moreComments: true,
                comments: lecture.comments.slice(-limit - req.body.numberOfComments, lecture.comments.length - req.body.numberOfComments <= 0 ? 0 : lecture.comments.length - req.body.numberOfComments).map(comment => {
                    return {
                        _id: comment._id,
                        user: comment.user,
                        username: comment.username,
                        image: comment.image,
                        content: comment.content,
                        createdAt: comment.createdAt,
                        likes: comment.likes.length,
                        isLiked: comment.likes.includes(req.user._id),
                        isDisliked: comment.dislikes.includes(req.user._id),
                        moreReplies: comment.replies.length > 10 ? true : false,
                        repliesArray: comment.replies.slice(offset, offset + limit).map(reply => {
                            return {
                                _id: reply._id,
                                user: reply.user,
                                username: reply.username,
                                image: reply.image,
                                content: reply.content,
                                createdAt: reply.createdAt,
                                likes: reply.likes.length,
                                isLiked: reply.likes.includes(req.user._id),
                                isDisliked: reply.dislikes.includes(req.user._id),
                                isCurrentUserComment: reply.user.toString() === req.user._id.toString()
                            };
                        }),
                        nreplies: comment.replies.length,
                        isCurrentUserComment: comment.user.toString() === req.user._id.toString()
                    };
                })
                    .sort((a, b) => b.createdAt - a.createdAt)
            };
            if (lecture.comments.length - (req.body.numberOfComments + 10) <= 0) {
                data.moreComments = false
            }
            res.json(data);
        } else {
            const data = {
                moreComments: true,
                comments: lecture.comments.slice(-limit - req.body.numberOfComments, lecture.comments.length - req.body.numberOfComments).map(comment => {
                    return {
                        _id: comment._id,
                        user: comment.user,
                        username: comment.username,
                        image: comment.image,
                        content: comment.content,
                        createdAt: comment.createdAt,
                        likes: comment.likes.length,
                        isLiked: false,
                        isDisliked: false,
                        moreReplies: comment.replies.length > 10 ? true : false,
                        repliesArray: comment.replies.slice(offset, offset + limit).map(reply => {
                            return {
                                _id: reply._id,
                                user: reply.user,
                                username: reply.username,
                                image: reply.image,
                                content: reply.content,
                                createdAt: reply.createdAt,
                                likes: reply.likes.length,
                                isLiked: false,
                                isDisliked: false,
                                isCurrentUserComment: false
                            };
                        }),
                        nreplies: comment.replies.length,
                        isCurrentUserComment: false
                    };
                })
                    .sort((a, b) => b.createdAt - a.createdAt)
            };
            if (lecture.comments.length - (req.body.numberOfComments + 10) <= 0) {
                data.moreComments = false
            }
            res.json(data);
        }

    } catch (err) {
        console.error('Error fetching Comments:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.likeLecture = async function (req, res) {
    const { moduleWrapperId, lectureId, moduleId } = req.params;
    try {
        // Find the module containing the lecture
        const module = await classroom_content2_5.findById(moduleWrapperId);
        if (!module) {
            return res.status(404).json({ error: 'Module or Lecture not found' });
        }

        // Find the lecture inside the module
        const lecture = module.modules.find(mod => mod._id.toString() == moduleId).lectures.find(lec => lec._id.toString() == lectureId)
        if (!lecture) {
            return res.status(404).json({ error: 'Lecture not found' });
        }

        const userLikes = lecture.likes.includes(req.user._id);
        const userDislikes = lecture.dislikes.includes(req.user._id);
        if (userLikes) {
            // User has already liked the lecture
            lecture.likes.pull(req.user._id);
        } else if (!userLikes) {
            // User has not liked the lecture
            lecture.likes.push(req.user._id);
            if (userDislikes) {
                lecture.dislikes.pull(req.user._id);
            }
        }

        // Update the total count of likes and dislikes for the lecture
        var likesCount = lecture.likes.length;

        lecture.markModified('dislikes');
        lecture.markModified('likes');
        // Save the updated module (which includes the updated lecture)
        await module.save();

        // Send a success response with the updated counts to the client
        res.status(200).json({ likes: likesCount });
    } catch (err) {
        console.error(err);
        res.status(500).send('Internal Server Error');
    }
}

module.exports.dislikeLecture = async function (req, res) {
    const {moduleWrapperId, lectureId, moduleId } = req.params;

    try {
        // Find the module containing the lecture
        const module = await classroom_content2_5.findById(moduleWrapperId);
        if (!module) {
            return res.status(404).json({ error: 'Module or Lecture not found' });
        }

        // Find the lecture inside the module
        const lecture = module.modules.find(mod => mod._id.toString() == moduleId).lectures.find(lec => lec._id.toString() == lectureId)
        if (!lecture) {
            return res.status(404).json({ error: 'Lecture not found' });
        }

        const userDislikes = lecture.dislikes.includes(req.user._id);
        const userLikes = lecture.likes.includes(req.user._id);
        if (userDislikes) {
            // User has already disliked the lecture
            lecture.dislikes.pull(req.user._id);
        } else {
            // User has not disliked the lecture, so add the dislike
            lecture.dislikes.push(req.user._id);
            if (userLikes) {
                lecture.likes.pull(req.user._id);
            }
        }

        // Update the total count of dislikes for the lecture
        var likesCount = lecture.likes.length;

        lecture.markModified('dislikes');
        lecture.markModified('likes');
        // Save the updated module (which includes the updated lecture)
        await module.save();

        // Send a success response with the updated counts to the client
        res.status(200).json({ likes: likesCount });
    } catch (err) {
        console.error(err);
        res.status(500).send('Internal Server Error');
    }
};

module.exports.addcomment = async function (req, res) {
    try {
        const { moduleId, lectureId,moduleWrapperId } = req.params;
        const { user, username, image, content } = req.body;

        const newComment = new Comments_Classroom2({ user, username, image, content, module_id: moduleId, lecture_id: lectureId });
        const savedComment = await newComment.save();
        
        // Add the comment ID to the corresponding lecture's comments array
        const module = await classroom_content2_5.findById(moduleWrapperId);

        // Find the lecture inside the module
        const lecture = module.modules.find(mod => mod._id.toString() == moduleId).lectures.find(lec => lec._id.toString() == lectureId)
        // const lecture = await classroom_content2.findOneAndUpdate(
        //     { _id: moduleId, 'lectures._id': lectureId },
        //     { $push: { 'lectures.$.comments': savedComment._id } },
        //     { new: true }
        // );
        lecture.comments.push(savedComment._id)
        await module.save()
        if (!lecture) {
            return res.status(404).json({ error: 'Lecture not found' });
        }

        res.status(200).json({ commentId: savedComment._id, userId: req.user._id, success: true });
    } catch (err) {
        console.error('Error adding comment:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.deleteComment = async function (req, res) {
    try {
        const { commentId, userId } = req.params;
        // Assuming you have the Comment model imported
        if (req.user._id.toString() !== userId && !req.user.admin && req.user.status != 'instrcutor') {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        await Comments_Classroom2.findByIdAndDelete(commentId);
        res.json({ message: 'Comment deleted successfully' });
    } catch (err) {
        console.error('Error deleting comment:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.likeComment = async function (req, res) {
    try {
        const { commentId, userId } = req.params;
        const comment = await Comments_Classroom2.findById(commentId);
        if (!comment) {
            return res.status(404).json({ error: 'Comment not found' });
        }
        if (!req.user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        const userLikes = comment.likes.includes(req.user._id);
        const userDislikes = comment.dislikes.includes(req.user._id);
        if (userLikes) {
            // User has already liked the comment
            comment.likes.pull(req.user._id);
        } else if (!userLikes) {
            // User has not liked the comment
            comment.likes.push(req.user._id);
            if (userDislikes) {
                comment.dislikes.pull(req.user._id);
            }
        }
        // Update the total count of likes and dislikes for the comment
        var likesCount = comment.likes.length;
        var dislikesCount = comment.dislikes.length;
        comment.markModified('dislikes');
        comment.markModified('likes');
        await comment.save();
        res.status(200).json({ replyId: comment._id, userId: req.user._id });
    } catch (err) {
        console.error('Error liking comment:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.dislikeComment = async function (req, res) {
    try {
        const { commentId, userId } = req.params;
        const comment = await Comments_Classroom2.findById(commentId);
        if (!comment) {
            return res.status(404).json({ error: 'Comment not found' });
        }
        if (!req.user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        const userDislikes = comment.dislikes.includes(req.user._id);
        const userLikes = comment.likes.includes(req.user._id);
        if (userDislikes) {
            // User has already disliked the comment
            comment.dislikes.pull(req.user._id);
        } else {
            // User has not disliked the comment, so add the dislike
            comment.dislikes.push(req.user._id);
            if (userLikes) {
                comment.likes.pull(req.user._id);
            }
        }
        // Update the total count of dislikes for the comment
        var likesCount = comment.likes.length;
        var dislikesCount = comment.dislikes.length;
        comment.markModified('dislikes');
        comment.markModified('likes');
        await comment.save();
        res.status(200).json({ likes: likesCount, dislikes: dislikesCount });
    } catch (err) {
        console.error('Error disliking comment:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

module.exports.addReply = async function (req, res) {
    try {
        // Extract data from the request body
        const { user, username, image, content, parentCommentId, course_id, parentCommentUser } = req.body;
        const { moduleId, lectureId,moduleWrapperId } = req.params;

        // Find the parent comment by its ID
        const parentComment = await Comments_Classroom2.findById(parentCommentId);

        if (!parentComment) {
            return res.status(404).json({ message: 'Parent comment not found' });
        }

        // Create a new reply
        const newReply = {
            user: user,
            username: username,
            image: image,
            content: content,
            module_id: moduleId,
            lecture_id: lectureId,
            parentComment: parentCommentId,
        };


        // Push the new reply to the parent comment's replies array
        parentComment.replies.push(newReply);

        // Save the updated parent comment
        await parentComment.save();

        // Get the _id of the newly created reply
        const reply = parentComment.replies[ parentComment.replies.length - 1 ];

        // Notify the user who made the parent comment
        const userWhoMadeParentComment = await User.findById(parentCommentUser); // Replace 'user' with the actual field containing the user's ID in your Comment model
        const module = await classroom_content2_5.findOne({ _id: moduleWrapperId});

        const lecture = module.modules.find(mod => mod._id.toString() == moduleId).lectures.find(lec => lec._id.toString() == lectureId)

        if (!lecture) {
            return res.status(404).json({ error: 'Lecture not found' });
        }

        const videoId = lecture.guid;
        const thumbnail = 'https://vz-53036e60-5d9.b-cdn.net/' + videoId + '/thumbnail.jpg'
        const user_dp = await Profile_dp.findOne({ user_id: user });
        const user_dp_url = user_dp.dp;
        // Limit the message to 100 characters and append "..." if it exceeds
        const truncatedMessage = content.length > 100 ? content.slice(0, 100) + '...' : content;
        if (userWhoMadeParentComment && userWhoMadeParentComment._id.toString() !== user.toString()) {
            const newNotification = {
                type: 'reply', // You can specify the type as 'reply' or any other suitable value
                sender: user, // The user who posted the reply
                message: truncatedMessage, // Customize the notification message
                guid: videoId, // Provide a thumbnail image URL if available
                link: `/classroom/gotoclassroom/${course_id}/${moduleId}/${lectureId}`, // Provide a link to the comment or the relevant discussion
                createdAt: new Date(),
                thumbnail_url: thumbnail,
                sender_dp: user_dp_url
            };

            userWhoMadeParentComment.notifications.push(newNotification);

            await userWhoMadeParentComment.save();
        }
        // Optionally, you can send a response indicating success
        return res.status(200).json({ reply: reply });
    } catch (error) {
        console.error('Error adding reply:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
}

module.exports.updateReplyLikes = async function (req, res) {
    try {
        // Extract user ID from the request (assuming you have user authentication in place)
        const userId = req.user.id; // Replace with your actual authentication logic

        // Extract reply ID from the request parameters
        const { replyId } = req.params;

        // Find the comment containing the reply
        const comment = await Comments_Classroom2.findOne({ 'replies._id': replyId });

        if (!comment) {
            return res.status(404).json({ message: 'Comment not found' });
        }

        // Find the specific reply within the comment's replies array
        const reply = comment.replies.find(reply => reply._id.equals(replyId));

        if (!reply) {
            return res.status(404).json({ message: 'Reply not found' });
        }

        // Check if the user has already liked this reply
        if (reply.likes.includes(userId)) {
            // User has already liked this reply, remove the like
            reply.likes.pull(userId);
        } else {
            // User has not liked this reply, add the like
            reply.likes.push(userId);

            const userDisliked = reply.dislikes.includes(userId);
            if (userDisliked) {
                reply.dislikes.pull(userId);
            }
        }

        // Save the updated comment (which includes the updated reply)
        await comment.save();

        // Send a success response
        return res.status(200).json({ message: 'Reply liked/disliked successfully' });
    } catch (error) {
        console.error('Error liking/disliking reply:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
}

module.exports.updateReplyDislikes = async function (req, res) {
    try {
        // Extract user ID from the request (assuming you have user authentication in place)
        const userId = req.user.id; // Replace with your actual authentication logic

        // Extract reply ID from the request parameters
        const { replyId } = req.params;

        // Find the comment containing the reply
        const comment = await Comments_Classroom2.findOne({ 'replies._id': replyId });

        if (!comment) {
            return res.status(404).json({ message: 'Comment not found' });
        }

        // Find the specific reply within the comment's replies array
        const reply = comment.replies.find(reply => reply._id.equals(replyId));

        if (!reply) {
            return res.status(404).json({ message: 'Reply not found' });
        }

        // Check if the user has already disliked this reply
        const userDisliked = reply.dislikes.includes(userId);

        if (userDisliked) {
            // User has already disliked this reply, remove the dislike
            reply.dislikes.pull(userId);
        } else {
            // User has not disliked this reply, add the dislike
            reply.dislikes.push(userId);

            // Check if the user had previously liked this reply and remove the like
            const userLiked = reply.likes.includes(userId);
            if (userLiked) {
                reply.likes.pull(userId);
            }
        }

        // Save the updated comment (which includes the updated reply)
        await comment.save();

        // Send a success response
        return res.status(200).json({ message: 'Reply disliked successfully' });
    } catch (error) {
        console.error('Error disliking reply:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
}

module.exports.deleteReply = async function (req, res) {
    try {
        // Extract user ID from the request (assuming you have user authentication in place)
        const userId = req.user.id; // Replace with your actual authentication logic

        // Extract reply ID from the request parameters
        const { replyId } = req.params;

        // Find the comment containing the reply
        const comment = await Comments_Classroom2.findOne({ 'replies._id': replyId });

        if (!comment) {
            return res.status(404).json({ message: 'Comment not found' });
        }

        // Find the specific reply within the comment's replies array
        const reply = comment.replies.find(reply => reply._id.equals(replyId));

        if (!reply) {
            return res.status(404).json({ message: 'Reply not found' });
        }

        // Check if the user is the author of the reply
        if (!reply.user.equals(userId)) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        // Remove the reply from the replies array
        comment.replies.pull(replyId);

        // Save the updated comment (which includes the updated reply)
        await comment.save();

        // Send a success response
        return res.status(200).json({ message: 'Reply deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting reply:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
}

module.exports.markLectureAsCompleted = async function (req, res) {
    const { userId, courseId, lectureId, moduleId, percentageCompleted, totalVideos } = req.body;
    try {
        let user = await User.findById(userId);

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        let courseIndex = user.classroom_track.findIndex(track => track.courseId.toString() === courseId);
        if (courseIndex === -1) {
            user.classroom_track.push({
                courseId: mongoose.Types.ObjectId(courseId),
                completedLectures: [],
                completedModules: [],
            });
            courseIndex = user.classroom_track.length - 1;
        }

        const completedLectureIndex = user.classroom_track[courseIndex]
            .completedLectures.findIndex(lecture => lecture.lectureId.toString() === lectureId);
        if (completedLectureIndex === -1) {
            user.classroom_track[courseIndex].completedLectures.push({
                lectureId: mongoose.Types.ObjectId(lectureId),
                percentageCompleted: percentageCompleted || 100,
            });
        } else if (user.classroom_track[courseIndex].completedLectures[completedLectureIndex].percentageCompleted < 100 && user.classroom_track[courseIndex].completedLectures[completedLectureIndex].percentageCompleted < percentageCompleted) {
            user.classroom_track[courseIndex].completedLectures[completedLectureIndex].percentageCompleted = percentageCompleted || 0;
        }

        await user.save();
        let totalCompletionPercentage = 0;
        if (user.classroom_track[courseIndex]) {
            for (const completedLecture of user.classroom_track[courseIndex].completedLectures) {
                totalCompletionPercentage += completedLecture.percentageCompleted;
            }
        }
        const targetCompletionPercentage = totalVideos * 100;
        const progressPercentage = Math.floor((totalCompletionPercentage / targetCompletionPercentage) * 100);
        let points = 0;
        if (percentageCompleted >= 100) {
            const lecture = await classroom_content2_5.findOne({"modules.lectures._id": lectureId});
            if (lecture) {
                points = lecture.modules.find(mod => mod._id.toString() == moduleId).lectures.find(lec => lec._id.toString() == lectureId).points;
            }
        }
        res.status(200).json({ message: 'Lecture marked as completed successfully', percentageCompleted: percentageCompleted || 0, progressPercentage: progressPercentage || 0, points: points });
    } catch (err) {
        console.error('Error marking lecture as completed:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}



module.exports.updateNotification = async function (req, res) {
    try {
        const user = await User.findById(req.user._id);
        if (!user) return res.status(400).json({ message: "user not found" })
        const idx = user.notifications.findIndex((notify) => notify._id.toString() == req.body.notificationId.toString())
        user.notifications[ idx ].seen = true
        await user.save()
        console.log("updated notification")
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({ message: "Server Error" })
    }
}

module.exports.getNotifications = async function (req, res) {
    try {
        // Get the page number from the request query parameters
        const page = parseInt(req.params.page) || 1; // Default to page 1 if not specified
        // Get the number of notifications to skip
        const numNotificationsToSkip = (page - 1) * 10;
        // Get the number of notifications to return
        const numNotificationsToReturn = 10;
        // Find the user
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        // Get the notifications for the user
        const notifications = user.notifications.reverse().slice(numNotificationsToSkip, numNotificationsToSkip + numNotificationsToReturn);
        // Send the notifications to the client

        let more = true
        if (user.notifications.length <= page * 10) more = false
        return res.status(200).json({ notifications, more });
    }
    catch (error) {
        console.error('Error getting notifications:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
}


module.exports.updateLastWatchedLecture = async function (req, res) {
    const { courseId, moduleId, lectureId } = req.body;
    try {
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }
        const lastWatchedLecture = user.lastWatchedLecture.find((lecture) => lecture.courseId.toString() === courseId.toString());
        if (lastWatchedLecture) {
            lastWatchedLecture.moduleId = moduleId;
            lastWatchedLecture.lectureId = lectureId;
        }
        else {
            user.lastWatchedLecture.push({ courseId, moduleId, lectureId });
        }
        await user.save();
        res.json({ success: true });
    } catch (err) {
        console.error('Error updating last watched lecture:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }

}

module.exports.getLastWatchedLecture = async function (req, res) {
    const { courseId } = req.params;
    try {

    } catch (err) {
        console.error('Error fetching last watched lecture:', err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}


async function deleteModuleWrapper(id){  
    await classroom_content2_5.findOneAndDelete({ _id: id })
}
async function deleteModule(obj){
    try {
        const content = await classroom_content2_5.findOneAndUpdate(
          { _id: obj.moduleWrapperID },
          { $pull: { modules: { _id: obj.moduleID } } },
          { new: true } // Return the updated document
        );
        if (!content) {
          return; // Or take other actions as needed
        }
      } catch (err) {
        console.error('Error deleting module:', err);
        // Handle other potential errors during deletion
      }
}

module.exports.deleteLecture = async (req, res) => {
    try {
        if (req.user.admin || req.user.status == "superMentor" || req.user.status == "instructor") {
            const content = await classroom_content2_5.findOne({ _id: req.params.module_wrapper_id });

            if (!content) {
                return res.status(404).send('Content not found');
            }
            
            const moduleIdx = content.modules.findIndex(module => module._id.toString() === req.params.module_id);
            if (moduleIdx === -1) {
                return res.status(404).send('Module not found');
            }

            content.modules[moduleIdx].lectures = content.modules[moduleIdx].lectures.filter(lecture => lecture._id.toString() !== req.params.lec_id);

            await content.save();

            return res.status(200).send("ok");
        }
    } catch (err) {
        console.error(err);
        return res.status(500).send('Internal Server Error');
    }
}

module.exports.deleteLink = async (req, res) => {
    try {
        if (req.user.admin || req.user.status == "superMentor" || req.user.status == "instructor") {
            const content = await classroom_content2_5.findOne({ _id: req.params.module_wrapper_id });

            if (!content) {
                return res.status(404).send('Content not found');
            }

            const moduleIdx = content.modules.findIndex(module => module._id.toString() === req.params.module_id);
            if (moduleIdx === -1) {
                return res.status(404).send('Module not found');
            }

            const lecIdx = content.modules[moduleIdx].lectures.findIndex(lec => lec._id.toString() === req.params.lec_id);
            if (lecIdx === -1) {
                return res.status(404).send('Lecture not found');
            }

            content.modules[moduleIdx].lectures[lecIdx].links = content.modules[moduleIdx].lectures[lecIdx].links.filter(link => link._id.toString() !== req.params.link_id);

            await content.save();

            return res.status(200).send("ok");
        }
    } catch (err) {
        console.error(err);
        return res.status(500).send('Internal Server Error');
    }
}

async function createAndUpdateFeedback(body, user, course) {
    await Feedback.create({
        feedbackText: body.feedback,
        userName: user.name,
        userEmail: body.email,
        courseId: body.courseId,
        lectureId: body.lectureId,
        courseName: course.course_name,
        date: Date.now()
    });
}

module.exports.sendBulkUpdateEmail = async (req, res) => {
    try{
        if(!req.user || !req.user.admin) return res.status(400).json({message:"Unauthorized"})

        const course = await Courses.findById(req.params.courseID).select('+enrolled_students')
        if(!course) return res.status(400).json({message:"Course Not found"})

        const studentIDs = course.enrolled_students.filter(stud=>stud.status == "paid").map(stud=>stud.student_id)
        // courseUpdateNotification(studentIDs,course._id,course.image)

        const students = await User.find({_id:{$in:studentIDs}})
        let userData = students.map(stud=>{return {email:stud.email,name:stud.name}})
        
        // // Extracting emails
        // const emails = userData.map(user => user.email);

        // // Writing emails to a text file named 'emails.txt'
        // fs.writeFileSync('emails.txt', emails.join('\n'));

        sendEmail([],course)

        return res.status(200).json({message:"Completed"})
    }catch(err){
        console.log(err)
        return res.status(500).json({message:"Internal Server error", error: err})
    }
}
module.exports.feedback = async (req, res) => {
    try {
        if (!req.user) {
            return res.status(401).json({ message: "Login to continue" });
        }

        if (req.body.feedback.length > 200) {
            return res.status(429).json({ message: "Feedback too long" });
        }

        const courseId = req.body.courseId;
        const currentUser = await User.findById(req.user._id);

        if (!currentUser) {
            return res.status(400).json({ message: "User not found" });
        }

        if (!currentUser.enrolledCourses.includes(courseId)) {
            return res.status(401).json({ message: "You don't have access to this course" });
        }

        const crse = await Courses.findById(courseId);

        if (!crse || crse.course_status === "inactive" || crse.commingSoon) {
            return res.status(403).json({ message: "Invalid or inactive course" });
        }

        const idx = currentUser.feedbacks.findIndex((e) => e.courseId.toString() === courseId.toString());
        let feedback = currentUser.feedbacks[ idx ];

        if (idx !== -1) {
            if (feedback.feedback < 16) {
                createAndUpdateFeedback(req.body, currentUser, crse);
                feedback.feedback++;
                feedback.date = Date.now();
                await currentUser.save();
                return res.status(200).json({ message: "Feedback submitted" });

            } else if ((Number(feedback.time) + 86400000) <= Date.now()) {
                createAndUpdateFeedback(req.body, currentUser, crse);
                feedback.feedback = 1;
                feedback.date = Date.now();
                await currentUser.save();
                return res.status(200).json({ message: "Feedback submitted" });

            } else {
                return res.status(429).json({ message: "Too many feedbacks" });
            }
        } else {
            await createAndUpdateFeedback(req.body, currentUser, crse);
            currentUser.feedbacks.push({
                courseId: req.body.courseId,
                feedback: 1,
                time: Date.now(),
            });
            await currentUser.save();
            return res.status(200).json({ message: "Feedback registered" });
        }
    } catch (err) {
        console.log(err);
        return res.status(500).json({ message: "Internal Server Error" });
    }
}
// 


module.exports.updateCourse = async (req, res) => {

    if ((req.user && req.user.admin) || (req.user && req.user.status == "superMentor") || (req.user && req.user.status == "instructor")) {
        let courseId
        for (const moduleWrapper of req.body.saveObj){
            if (moduleWrapper._id) { //checking if id exists (if not it means its a new moduleWrapper)
                const content = await classroom_content2_5.findOne({ _id: moduleWrapper._id })
                content.title = moduleWrapper.title;
                content.order = moduleWrapper.order;
                content.comingSoonDate = moduleWrapper.comingSoonDate ? moduleWrapper.comingSoonDate : "";
                
                const course = await Courses.findById(moduleWrapper.courseId)
                course.live_class_notes = req.body.live_class_notes
                await course.save()

                for (const module of moduleWrapper.modules){ 
                    if(module._id){ //checking if id exists (if not it means its a new module)
                        var modIdx = content.modules.findIndex((mod) => module._id.toString() == mod._id.toString());
                        if(modIdx != -1){ // if module is found we update the module
                            content.modules[modIdx].title = module.title;
                            content.modules[modIdx].order = module.order;
                            content.modules[modIdx].comingSoonDate = module.comingSoonDate;
                            for (const lecture of module.lectures){
                                if(lecture.type == "mcq" || lecture.type == "code"){
                                    const lec = content.modules[modIdx].lectures.find((lec) => lecture.lec_id.toString() === lec._id.toString());
                                    if(lec){
                                        lec.order = lecture.order
                                        lec.newLecture = lecture.newLecture
                                    }else{
                                        const lecture_document = await classroom_content2_5.findOne({ "modules.lectures._id": lecture.lec_id });
                                        if(lecture_document._id.toString() == content._id.toString()){ // to prevent multiple saving of same document
                                            for (const module of content.modules) {
                                                var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                                if(tempLec) {
                                                    tempLec = JSON.parse(JSON.stringify(tempLec));
                                                    module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                                    content.modules[modIdx].lectures.push(tempLec);
                                                }
                                            }
                                        }else{
                                            for (const module of lecture_document.modules) {
                                                var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                                if(tempLec) {
                                                    tempLec = JSON.parse(JSON.stringify(tempLec));
                                                    module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                                    content.modules[modIdx].lectures.push(tempLec);
                                                    await lecture_document.save()
                                                }
                                            }
                                        }
                                    }
                                }
                                if (lecture.comingSoon) {
                                    if (lecture.lec_id) {
                                        const lec = content.modules[modIdx].lectures.find((lec) => lecture.lec_id.toString() === lec._id.toString());
                                        if (lec) {
                                            if(lec.type == "pdf"){
                                                lec.title = lecture.title
                                                lec.pdf = lecture.pdf
                                                lec.points = lecture.marks
                                                lec.order = lecture.order
                                                lec.newLecture = lecture.newLecture


                                            }else if(lec.type == "video"){
                                                lec.guid = lecture.guid;
                                                lec.order = lecture.order;
                                                lec.comingSoon = lecture.comingSoon;
                                                lec.comingSoonDetails = lecture.comingSoonDetails;
                                                lec.newLecture = lecture.newLecture
                                                lec.points = lecture.marks
                                            }
                                        }
                                        else{
                                            const lecture_document = await classroom_content2_5.findOne({ "modules.lectures._id": lecture.lec_id });
                                            if(lecture_document._id.toString() == content._id.toString()){ // to prevent multiple saving of same document
                                                for (const module of content.modules) {
                                                    var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                                    if(tempLec) {
                                                        tempLec = JSON.parse(JSON.stringify(tempLec));
                                                        module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                                        content.modules[modIdx].lectures.push(tempLec);
                                                    }
                                                }
                                            }else{
                                                for (const module of lecture_document.modules) {
                                                    var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                                    if(tempLec) {
                                                        tempLec = JSON.parse(JSON.stringify(tempLec));
                                                        module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                                        content.modules[modIdx].lectures.push(tempLec);
                                                        await lecture_document.save()
                                                    }
                                                }
                                                lecture_document.markModified('modules.lectures');
                                                lecture_document.markModified('modules');
                                                await lecture_document.save()
                                            }
                                            

                                        }
                                    } else {
                                        if(lec.type == "pdf"){
                                            content.modules[modIdx].lectures.push({
                                                type: "pdf",
                                                order: lecture.order,
                                                title: lecture.title,
                                                pdf: lecture.pdf,
                                                points:lecture.marks,
                                                newLecture: lecture.newLecture
                                            })

                                        }else if(lec.type == "video"){
                                            content.modules[modIdx].lectures.push({
                                                type: "video",
                                                order: lecture.order,
                                                guid: lecture.guid,
                                                comingSoonDetails: lecture.comingSoonDetails,
                                                comingSoon: lecture.comingSoon,
                                                newLecture: lecture.newLecture,
                                                points:lecture.marks
                                            })
                                        }
                                    }
    
                                } else {
                                    var modIdx = content.modules.findIndex((mod) => module._id.toString() == mod._id.toString());
                                    if (lecture.lec_id) {
                                        const lec = content.modules[modIdx].lectures.find((lec) => lecture.lec_id.toString() === lec._id.toString());
                                        if (lec) {
                                            if(lec.type == "pdf"){
                                                lec.order = lecture.order
                                                lec.title = lecture.title
                                                lec.pdf = lecture.pdf
                                                lec.points =lecture.marks
                                                lec.newLecture = lecture.newLecture
                                            }
                                            else if(lec.type == "video"){
                                                lec.guid = lecture.guid;
                                                lec.order = lecture.order;
                                                lec.links = lecture.links;
                                                lec.comingSoon = lecture.comingSoon;
                                                lec.newLecture = lecture.newLecture;
                                                lec.points = lecture.marks;
                                            }
                                        }
                                        else{
                                            const lecture_document = await classroom_content2_5.findOne({ "modules.lectures._id": lecture.lec_id });
                                            if(lecture_document._id.toString() == content._id.toString()){
                                                for (const module of content.modules) {
                                                    var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                                    if(tempLec) {
                                                        tempLec = JSON.parse(JSON.stringify(tempLec));
                                                        module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                                        content.modules[modIdx].lectures.push(tempLec);
                                                    }
                                                }
                                            }else{
                                                for (const module of lecture_document.modules) {
                                                    var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                                    if(tempLec) {
                                                        tempLec = JSON.parse(JSON.stringify(tempLec));
                                                        module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                                        content.modules[modIdx].lectures.push(tempLec); 
                                                        // await lecture_document.save()
                                                    }
                                                }
                                                lecture_document.markModified('modules.lectures');
                                                lecture_document.markModified('modules');
                                                await lecture_document.save()
                                            }
                                            
                                        }
    
                                    } else {
                                        if(lecture.type == "pdf"){
                                            content.modules[modIdx].lectures.push({
                                                type: "pdf",
                                                order: lecture.order,
                                                title: lecture.title,
                                                pdf: lecture.pdf,
                                                points:lecture.marks,
                                                newLecture: lecture.newLecture

                                            })

                                        }else if(lecture.type == "video"){
                                            content.modules[modIdx].lectures.push({
                                                type: "video",
                                                order: lecture.order,
                                                guid: lecture.guid,
                                                links: lecture.links,
                                                newLecture: lecture.newLecture,
                                                points: lecture.marks
                                            })
                                        }
                                    }
                                }
                            }
                        }
                        else{
                            const module_document = await classroom_content2_5.findOne({ "modules._id": module._id });
                            if(module_document){
                                var tempModule = module_document.modules.find(mod => mod._id.toString() == module._id.toString())
                                if(tempModule){
                                    tempModule = JSON.parse(JSON.stringify(tempModule))
                                    content.modules.push(tempModule)
                                    module_document.modules = module_document.modules.filter(mod => mod._id.toString() != module._id.toString())
                                    await module_document.save()
                                }
                            }
                        }
                    }else{
                        let lectureArray = []
                        for (const lecture of module.lectures){
                            if(lecture.lec_id){

                                const lecture_document = await classroom_content2_5.findOne({ "modules.lectures._id": lecture.lec_id });
                                if(lecture_document._id.toString() == content._id.toString()){
                                    for (const module of content.modules) {
                                        var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                        if(tempLec) {
                                            tempLec = JSON.parse(JSON.stringify(tempLec));
                                            module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                            lectureArray.push(tempLec);
                                        }
                                    }
                                }else{
                                    for (const module of lecture_document.modules) {
                                        var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                        if(tempLec) {
                                            tempLec = JSON.parse(JSON.stringify(tempLec));
                                            module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                            lectureArray.push(tempLec); 
                                        }
                                    }
                                    lecture_document.markModified('modules.lectures');
                                    lecture_document.markModified('modules');
                                    await lecture_document.save()
                                }

                            }else{
                                lectureArray.push(lecture)
                            }
                        }
                        content.modules.push({
                            courseId: moduleWrapper.courseId,
                            title: module.title,
                            order: module.order,
                            lectures: lectureArray, 
                            comingSoonDate:module.comingSoonDate
                        })
                    }
                }
                content.markModified('modules');
                content.markModified('modules.lectures');
                content.markModified('modules.lectures.links');
                await content.save()

            } else {
                var ModuleArray = []
                for (const module of moduleWrapper.modules){
                    if(module._id){
                        let lectureArray = []
                        const module_document = await classroom_content2_5.findOne({ "modules._id": module._id });
                        var foundModule = module_document.modules.find((mod) => module._id.toString() == mod._id.toString());
                        for (const lecture of module.lectures){
                            if(lecture.lec_id){
                                const lecture_document = await classroom_content2_5.findOne({ "modules.lectures._id": lecture.lec_id }); 
                                for (const module of lecture_document.modules) {
                                    var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                    if(tempLec) {
                                        tempLec = JSON.parse(JSON.stringify(tempLec));
                                        module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                        lectureArray.push(tempLec); 
                                    }
                                }
                                lecture_document.markModified('modules.lectures');
                                lecture_document.markModified('modules');
                                if(lecture_document._id.toString() != module_document._id.toString()){
                                    await lecture_document.save()                   
                                }
                            }else{
                                lectureArray.push(lecture)
                            }
                        }
                        module_document.modules = module_document.modules.filter((mod) => module._id.toString() != mod._id.toString());
                        await module_document.save()
                        ModuleArray.push({
                            title:foundModule.title,
                            order:foundModule.order,
                            courseId:moduleWrapper.courseId,
                            lectures:lectureArray
                        })
                    }else{
                        let lectureArray
                        for (const module of moduleWrapper.modules){ 
                            lectureArray = []
                            for (const lecture of module.lectures){
                                if(lecture.lec_id){
                                    const lecture_document = await classroom_content2_5.findOne({ "modules.lectures._id": lecture.lec_id });                                    
                                    for (const module of lecture_document.modules) {
                                        var tempLec = module.lectures.find((lec) => lec._id.toString() == lecture.lec_id.toString())
                                        if(tempLec) {
                                            tempLec = JSON.parse(JSON.stringify(tempLec));
                                            module.lectures = module.lectures.filter((lec) => lec._id.toString() != lecture.lec_id.toString());
                                            lectureArray.push(tempLec); 
                                        }
                                    }
                                    lecture_document.markModified('modules.lectures');
                                    lecture_document.markModified('modules');
                                    await lecture_document.save()
                                }else{
                                    lectureArray.push(lecture)
                                }
                            }
                        }
                        ModuleArray.push({
                            title:module.title,
                            order:module.order,
                            comingSoonDate:module.comingSoonDate,
                            courseId:moduleWrapper.courseId,
                            lectures:lectureArray
                        })
                    }
                }
                await classroom_content2_5.create({
                    courseId: moduleWrapper.courseId,
                    title: moduleWrapper.title,
                    order: moduleWrapper.order,
                    modules: ModuleArray,
                    comingSoonDate:moduleWrapper.comingSoonDate,
                })
            }
        }
        //deleteing Modules and module Array
        for (const moduleID of req.body.deleteModuleArray){
            deleteModule(moduleID)
        }
        for (const moduleWrapperID of req.body.deleteModuleWrapperArray){
            deleteModuleWrapper(moduleWrapperID)
        }
        return res.send("ok")

    }
}

// module.exports.getSharedLecture = async (req, res) => {
//     if (!req.user) {
//         return res.redirect('/signIn');
//     }
//     try {
//         const { course_id, moduleId, lectureId,moduleWrapperId } = req.params;
//         try {
//             // check paid or not
//             const user = await User.findOne({ _id: req.user._id });
//             const findUserFeeStatus = user.feeStatus.find((course) => course.course_id == course_id);

//             if (!findUserFeeStatus) {
//                 return res.redirect(`/courses/enroll/${course_id}`);
//             }
//             if (findUserFeeStatus.status == 'pending') {
//                 return res.redirect(`/courses/enroll/${course_id}/payfee`);
//             }
//             const classroomContent = await classroom_content2_5.find({ courseId: course_id }).sort({ order: 1 });
//             const course = await Courses.findOne({ _id: course_id }).select('+enrolled_students');
//             if (!course || !classroomContent) {
//                 return res.status(404).json({ error: 'Course not found' });
//             }
//             if (course.commingSoon || course.course_status == "inactive") {
//                 return res.redirect('/courses');
//             }
//             const courseName = course.course_name;
//             const courseEnrolledStudent = course.enrolled_students.length;
//             const courseId = course_id;
//             // Create an array to store video details for each lecture
//             const promises = [];
//             let totalPoints = 0;
//             let currentPoints = 0;
//             let pdflinks = {}
//             const courseTrack = user.classroom_track.length > 0 ? user.classroom_track.find(track => track.courseId.toString() === course_id): null;
//             const lectureCompletionData = {};
//             let totalCompletionPercentage = 0;
//             if (courseTrack) {
//                 for (const completedLecture of courseTrack.completedLectures) {
//                     if(completedLecture.markedOption && completedLecture.markedOption == -1 && !(completedLecture.percentageCompleted >= 50)){
//                         lectureCompletionData[completedLecture.lectureId.toString()] = -1;
//                     }else{
//                         lectureCompletionData[completedLecture.lectureId.toString()] = completedLecture.percentageCompleted;
//                         totalCompletionPercentage += completedLecture.percentageCompleted;
//                     }
//                 }
//             }
//             // Loop through each module and lecture to fetch video details
//             for (const moduleWrapper of classroomContent) {
//                 moduleWrapper.modules.sort((a, b) => a.order - b.order);
//                 for (const module of moduleWrapper.modules) {
//                     module.lectures.sort((a, b) => a.order - b.order);
//                     for (const lecture of module.lectures) {
    
//                         const lectureId = lecture._id.toString();
//                         const completionPercentage = lectureCompletionData[lectureId] ? lectureCompletionData[lectureId] : 0;
//                         let points = 0;
//                         totalPoints += Number(lecture.points ? lecture.points : 0);
//                         if(completionPercentage > 100){
//                             points = Number(lecture.points);
//                             currentPoints += Number(points);
//                         }
//                         if(lecture.type == "video"){
//                             const videoId = lecture.guid;
//                             // Check if the video details are already cached
//                             const cachedVideoDetails = await cache.get(videoId);
                            
    
//                             if (cachedVideoDetails) {
//                                 // Use the cached video details
//                                 promises.push(Promise.resolve({ lectureId: lecture._id, videoDetails: cachedVideoDetails, access: true, completionPercentage, points }));
//                             } else {
//                                 // Fetch video details for the current videoId
//                                 const options = {
//                                     method: "GET",
//                                     headers: {
//                                         Accept: "application/json",
//                                         "Content-Type": "application/json",
//                                         Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
//                                     },
//                                     json: true,
//                                 };
//                                 const promise = fetch(`https://dev.vdocipher.com/api/videos/${videoId}`, options)
//                                     .then((response) => response.json())
//                                     .then(async (videoDetails) => {
//                                         videoDetails.duration = await fancyTimeFormat(videoDetails.length);
//                                         // Cache the video details for future use
//                                         cache.set(videoId, videoDetails);
//                                         // Return the video details
//                                         return { lectureId: lecture._id, videoDetails, access: true, completionPercentage, points };
//                                     })
//                                     .catch((err) => {
//                                         if (lecture.comingSoon) {
//                                             return { lectureId: lecture._id, videoDetails: null, access: true, completionPercentage: 0, points: 0};
//                                         }
//                                         console.error('Error fetching video details:', err);
//                                         return { lectureId: lecture._id, videoDetails: null, completionPercentage: 0, points: 0};
//                                     });
    
//                                 promises.push(promise);
//                             }
//                         }  
//                         else if(lecture.type == "pdf"){
//                             pdflinks[lecture._id] = lecture.pdf
//                         } 
//                     }
//                 }
//             }
//             let profilePicture = user.avatar ? user.avatar : 'https://ik.imagekit.io/sheryians/user_nJd7Nc9el.png';
    
//             // Wait for all the promises to resolve
//             const videoDetailsByLecture = await Promise.all(promises);
//             // Pass the structured data to the EJS template for rendering
//             // Set the Cache-Control header to prevent caching
//             res.set('Cache-Control', 'no-store');
//             const targetCompletionPercentage = videoDetailsByLecture.length * 100;
//             const progressPercentage = Math.floor((totalCompletionPercentage / targetCompletionPercentage) * 100);
//             console.log(course)
//             res.render('gotoclassroom', { course: false, pageTitle: "Course Detail",currentPoints,totalPoints, courseImage: course.image, classroomContent, videoDetailsByLecture, profilePicture, courseName, courseEnrolledStudent, courseId, title: courseName, request: req, progressPercentage, fullAccess: true, lecture_open: { courseId: courseId, moduleId: moduleId, lectureId: lectureId } });
//         } catch (err) {
//             console.error(err);
//             res.status(500).send('Internal Server Error');
//         }

//     } catch (err) {

//     }
// }

module.exports.getSharedLecture = async (req, res) => {
    if (!req.user) {
        return res.redirect('/signIn');
    }
        const { course_id, moduleId, lectureId,moduleWrapperId } = req.params;
        try {
            const checkOfllineCourse = await offline_class.find({reference_courseID:course_id})   
            if(checkOfllineCourse.length > 0){
                return res.redirect(`/classroom`)
            } 
            const user = await User.findOne({ _id: req.user._id });
            const findUserFeeStatus = user.feeStatus.find((course) => course.course_id == course_id);
    
            if (!findUserFeeStatus && !user.admin && user.status != "instructor" && user.status != "mentor" && user.status != "superMentor") {
                return res.redirect(`/courses/enroll/${course_id}`);
            }
            if (findUserFeeStatus && findUserFeeStatus.status == 'pending' && !user.admin && user.status != "instructor" && user.status != "mentor" && user.status != "superMentor") {
                return res.redirect(`/courses/enroll/${course_id}/payfee`);
            }
            const classroomContent = await classroom_content2_5.find({ courseId: course_id }).sort({ order: 1 });
            const course = await Courses.findOne({ _id: course_id }).select('+enrolled_students');
            if (!course || !classroomContent) {
                return res.status(404).json({ error: 'Course not found' });
            }
            if(classroomContent.length == 0){
                return res.send("Content Coming Soon!")
            }
            if ((course.commingSoon || course.course_status == "inactive") && !user.admin && user.status != "instructor" && user.status != "mentor" && user.status != "superMentor") {
                return res.redirect('/courses');
            }
            let videoLectures = 0
            let videoLecturesCompleted = 0
            let AssesmentLectures = 0
            let AssesmentLecturesCompleted = 0
            const courseName = course.course_name;
            const courseEnrolledStudent = course.enrolled_students.filter((student) => student.status === 'paid').length;
            const courseId = course_id;
            // Create an array to store video details for each lecture
            const promises = [];
            let totalPoints = 0;
            let currentPoints = 0;
            let pdflinks = {}
            const courseTrack = user.classroom_track.length > 0 ? user.classroom_track.find(track => track.courseId.toString() === course_id): null;
            const lectureCompletionData = {};
            let totalCompletionPercentage = 0;
            if (courseTrack) {
                for (const completedLecture of courseTrack.completedLectures) {
                    if(completedLecture.percentageCompleted == -2){
                        lectureCompletionData[completedLecture.lectureId.toString()] = 0;
                    }else if(completedLecture.markedOption == -1 && completedLecture.percentageCompleted == 0){
                        lectureCompletionData[completedLecture.lectureId.toString()] = -1;
                    }else{
                        lectureCompletionData[completedLecture.lectureId.toString()] = completedLecture.percentageCompleted;
                        totalCompletionPercentage += completedLecture.percentageCompleted;
                    }
                }
            }
    
            // Loop through each module and lecture to fetch video details
            for (const moduleWrapper of classroomContent) {
                moduleWrapper.modules.sort((a, b) => a.order - b.order);
                for (const module of moduleWrapper.modules) {
                    module.lectures.sort((a, b) => a.order - b.order);
                    for (const lecture of module.lectures) {
    
                        const lectureId = lecture._id.toString();
                        const completionPercentage = lectureCompletionData[lectureId] ? lectureCompletionData[lectureId] : 0;
                        let points = 0;
                        totalPoints += Number(lecture.points ? lecture.points : 0);
                        if(completionPercentage > 0){
                            points = Number(lecture.points);
                            currentPoints += Number((completionPercentage/100) * points);
                        }
                        if(lecture.type == "video"){
                            videoLectures++
                            if(completionPercentage > 90) videoLecturesCompleted++
                        }else{
                            AssesmentLectures++
                            if(completionPercentage > 90 || (lectureCompletionData[lectureId] != undefined && completionPercentage == 0)) AssesmentLecturesCompleted++
                        }
                        if(lecture.type == "video"){
                            const videoId = lecture.guid;
                            // Check if the video details are already cached
                            const cachedVideoDetails = await cache.get(videoId);
                            
    
                            if (cachedVideoDetails) {
                                // Use the cached video details
                                promises.push(Promise.resolve({ lectureId: lecture._id, videoDetails: cachedVideoDetails, access: true, completionPercentage, points }));
                            } else {
                                // Fetch video details for the current videoId
                                const options = {
                                    method: "GET",
                                    headers: {
                                        Accept: "application/json",
                                        "Content-Type": "application/json",
                                        Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
                                    },
                                    json: true,
                                };
                                const promise = fetch(`https://dev.vdocipher.com/api/videos/${videoId}`, options)
                                    .then((response) => response.json())
                                    .then(async (videoDetails) => {
                                        videoDetails.duration = await fancyTimeFormat(videoDetails.length);
                                        // Cache the video details for future use
                                        cache.set(videoId, videoDetails);
                                        // Return the video details
                                        return { lectureId: lecture._id, videoDetails, access: true, completionPercentage, points };
                                    })
                                    .catch((err) => {
                                        if (lecture.comingSoon) {
                                            return { lectureId: lecture._id, videoDetails: null, access: true, completionPercentage: 0, points: 0};
                                        }
                                        console.error('Error fetching video details:', err);
                                        return { lectureId: lecture._id, videoDetails: null, completionPercentage: 0, points: 0};
                                    });
    
                                promises.push(promise);
                            }
                        }  
                        else if(lecture.type == "pdf"){
                            pdflinks[lecture._id] = lecture.pdf
                        } 
                    }
                }
            }
            let profilePicture = user.avatar ? user.avatar : 'https://ik.imagekit.io/sheryians/user_nJd7Nc9el.png';
            
            // Wait for all the promises to resolve
            const videoDetailsByLecture = await Promise.all(promises);
            const progressPercentage = Math.floor((currentPoints / totalPoints) * 100);
            const lastWatchedLecture = user && user.lastWatchedLecture ? user.lastWatchedLecture : null;
            res.render('gotoclassroom', {lecture_open: { courseId: courseId, moduleId: moduleId, lectureId: lectureId }, course, pageTitle: "Course Detail",AssesmentLectures,AssesmentLecturesCompleted,videoLectures,videoLecturesCompleted, courseImage: course.image, classroomContent, videoDetailsByLecture, profilePicture, courseName, courseId, title: courseName, request: req, lastWatchedLecture, progressPercentage, fullAccess: true, totalPoints, currentPoints,pdflinks,lectureCompletionData});
        } catch (err) {
            console.error(err);
            res.status(500).send('Internal Server Error');
        }
}


/* testdog controllers */

module.exports.getChatToken=async (req,res)=>{

    if (!req.user) {
        return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const lectureId = req.params.lectureId; 

    try{

        
        const lectureData = await classroom_content2_5.aggregate([
            { $match: { "modules.lectures._id": mongoose.Types.ObjectId(lectureId) } },
            { $unwind: "$modules" },
            { $unwind: "$modules.lectures" },
            { $match: { "modules.lectures._id": mongoose.Types.ObjectId(lectureId) } },
            { $project: { lecture: "$modules.lectures", _id: 0 } },
            { $project:{ guid:"$lecture.guid"} }
        ]);
        
        // Extract the lecture from the result
        const uniqueid = lectureData.length > 0 ? lectureData[0].guid : null;

        if(!uniqueid){
            return res.status(404).json({ error: 'Lecture not found' });
        }

        const iframeUrl = await testdog.generateIframeUrl({
            studentId: req.user._id,
            studentName: req.user.name,
            sourceId: uniqueid,
        })


        res.status(200).json({ iframeUrl });
        
    }catch(err){
        console.error(err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
    

    
}


module.exports.chatFrame = async (req, res) => {
    if (!req.user) {
        return res.status(401).json({ error: 'Unauthorized' });
    }

    const lectureId = req.params.lectureId;


    try {

        const lectureData = await classroom_content2_5.aggregate([
            { $match: { "modules.lectures._id": mongoose.Types.ObjectId(lectureId) } },
            { $unwind: "$modules" },
            { $unwind: "$modules.lectures" },
            { $match: { "modules.lectures._id": mongoose.Types.ObjectId(lectureId) } },
            { $project: { lecture: "$modules.lectures", _id: 0 } },
            { $project:{ guid:"$lecture.guid"} }
        ]);
        
        // Extract the lecture from the result
        const uniqueid = lectureData.length > 0 ? lectureData[0].guid : null;

        if(!uniqueid){
            return res.status(404).json({ error: 'Lecture not found' });
        }

        const iframeUrl = await testdog.generateIframeUrl({
            studentId: req.user._id,
            studentName: req.user.name,
            sourceId: uniqueid,
        })

        const html = `
        <html>
        <head>
            <title>sheryians</title>
            <link rel="icon" type="image/png" sizes="192x192" href="https://ik.imagekit.io/sheryians/Sheryians_Logo_wFKd9VClG.png">
            <style>
            body,html {
                height:100%;
                width:100%;
                }
                
                *{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            iframe{
                width: 100%;
                height: 100%;
                border: none;
                outline: none;
            }
            </style>
        </head>
        <body>
            <iframe src="${iframeUrl}" allow="camera; microphone; fullscreen; display-capture; clipboard-read; clipboard-write" allowfullscreen></iframe>
        </body>
        </html>`;
        
        res.send(html);
        
    }catch(err){
        console.error(err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

/* testdog controllers */

// const userAgent = req.headers['user-agent']; // Get user-agent from request headers

//     // Check if the user-agent contains 'Firefox'
//     if (userAgent && userAgent.includes('Firefox')) {
//         return res.status(403).send(unsupportBrowsersPage);
//     }

//     try {
//         const { course_id } = req.params;
//         // check paid or not
//         const user = await User.findOne({ _id: req.user._id });
//         const findUserFeeStatus = user.feeStatus.find((course) => course.course_id == course_id);

//         if (!findUserFeeStatus) {
//             return res.redirect(`/courses/enroll/${course_id}`);
//         }
//         if (findUserFeeStatus.status == 'pending') {
//             return res.redirect(`/courses/enroll/${course_id}/payfee`);
//         }
//         const classroomContent = await classroom_content2_5.find({ courseId: course_id }).sort({ order: 1 });
//         const course = await Courses.findOne({ _id: course_id });
//         if (!course || !classroomContent) {
//             return res.status(404).json({ error: 'Course not found' });
//         }
//         if(classroomContent.length == 0){
//             return res.send("Content Coming Soon!")
//         }
//         if (course.commingSoon || course.course_status == "inactive") {
//             return res.redirect('/courses');
//         }
//         const courseName = course.course_name;
//         const courseEnrolledStudent = course.enrolled_students.filter((student) => student.status === 'paid').length;
//         const courseId = course_id;
//         // Create an array to store video details for each lecture
//         const promises = [];
//         let totalPoints = 0;
//         let currentPoints = 0;
//         let pdflinks = {}
//         const courseTrack = user.classroom_track.length > 0 ? user.classroom_track.find(track => track.courseId.toString() === course_id): null;
//         const lectureCompletionData = {};
//         let totalCompletionPercentage = 0;
//         if (courseTrack) {
//             for (const completedLecture of courseTrack.completedLectures) {
//                 if(completedLecture.markedOption && completedLecture.markedOption == -1 && !(completedLecture.percentageCompleted >= 50)){
//                     lectureCompletionData[completedLecture.lectureId.toString()] = -1;
//                 }else{
//                     lectureCompletionData[completedLecture.lectureId.toString()] = completedLecture.percentageCompleted;
//                     totalCompletionPercentage += completedLecture.percentageCompleted;
//                 }
//             }
//         }
//         // Loop through each module and lecture to fetch video details
//         for (const moduleWrapper of classroomContent) {
//             moduleWrapper.modules.sort((a, b) => a.order - b.order);
//             for (const module of moduleWrapper.modules) {
//                 module.lectures.sort((a, b) => a.order - b.order);
//                 for (const lecture of module.lectures) {

//                     const lectureId = lecture._id.toString();
//                     const completionPercentage = lectureCompletionData[lectureId] ? lectureCompletionData[lectureId] : 0;
//                     let points = 0;
//                     totalPoints += Number(lecture.points ? lecture.points : 0);
//                     if(completionPercentage == 100){
//                         points = Number(lecture.points);
//                         currentPoints += Number(points);
//                     }
//                     if(lecture.type == "video"){
//                         const videoId = lecture.guid;
//                         // Check if the video details are already cached
//                         const cachedVideoDetails = await cache.get(videoId);
                        

//                         if (cachedVideoDetails) {
//                             // Use the cached video details
//                             promises.push(Promise.resolve({ lectureId: lecture._id, videoDetails: cachedVideoDetails, access: true, completionPercentage, points }));
//                         } else {
//                             // Fetch video details for the current videoId
//                             const options = {
//                                 method: "GET",
//                                 headers: {
//                                     Accept: "application/json",
//                                     "Content-Type": "application/json",
//                                     Authorization: "Apisecret EFBM9ouLWhdusvGH2i7vbT64ZK4AAu63xzrnITNYxWm5RR3rSd2q2KDR05L4JKuD",
//                                 },
//                                 json: true,
//                             };
//                             const promise = fetch(`https://dev.vdocipher.com/api/videos/${videoId}`, options)
//                                 .then((response) => response.json())
//                                 .then(async (videoDetails) => {
//                                     videoDetails.duration = await fancyTimeFormat(videoDetails.length);
//                                     // Cache the video details for future use
//                                     cache.set(videoId, videoDetails);
//                                     // Return the video details
//                                     return { lectureId: lecture._id, videoDetails, access: true, completionPercentage, points };
//                                 })
//                                 .catch((err) => {
//                                     if (lecture.comingSoon) {
//                                         return { lectureId: lecture._id, videoDetails: null, access: true, completionPercentage: 0, points: 0};
//                                     }
//                                     console.error('Error fetching video details:', err);
//                                     return { lectureId: lecture._id, videoDetails: null, completionPercentage: 0, points: 0};
//                                 });

//                             promises.push(promise);
//                         }
//                     }  
//                     else if(lecture.type == "pdf"){
//                         pdflinks[lecture._id] = lecture.pdf
//                     } 
//                 }
//             }
//         }
//         let profilePicture = user.avatar ? user.avatar : 'https://ik.imagekit.io/sheryians/user_nJd7Nc9el.png';

//         // Wait for all the promises to resolve
//         const videoDetailsByLecture = await Promise.all(promises);
//         const progressPercentage = Math.floor((currentPoints / totalPoints) * 100);
//         const lastWatchedLecture = user && user.lastWatchedLecture ? user.lastWatchedLecture : null;
//         res.render('gotoclassroom', { course, pageTitle: "Course Detail", courseImage: course.image, classroomContent, videoDetailsByLecture, profilePicture, courseName, courseEnrolledStudent, courseId, title: courseName, request: req, lastWatchedLecture, progressPercentage, fullAccess: true, totalPoints, currentPoints,pdflinks,lectureCompletionData});
//     } catch (err) {
//         console.error(err);
//         res.status(500).send('Internal Server Error');
//     }
// }

