const kodrModel = require("../Models/kodrRegistration")

module.exports.getKodr = async(req,res)=>{
    try{
        res.redirect("/")
        // const occupied = await kodrModel.countDocuments({status:"accepted"})
        // const seatsLeft = 24-occupied;
        // return res.render("course-detail-pages/kodr.ejs",{seatsLeft,status:req && req.user ? req.user : false, title:"Kodr | Sheryians", request: req.user ? req : null, pageTitle:"Kodr", course:{}, content:{}, check_paid:false, batch_status:""})
    }catch(err){
        console.log(err)
    }
}

module.exports.createSubmission = async (req,res)=>{
    try{
        const {name, email, contact, education, experience, dob} = req.body
        const emailExists = await kodrModel.findOne({"email":email})
        if(emailExists){
            return res.status(400).json({message:"<PERSON>ail already registered"})
        }
        const phoneExists = await kodrModel.findOne({"contact":contact})
        if(phoneExists){
            return res.status(400).json({message:"Phone already registered"})
        }
        await kodrModel.create(req.body)
        res.status(200).json({message:"Success"})
    }catch(err){
        res.status(500).json({message:"Internal Server ERROR"})
        console.log(err)
    }
}

module.exports.allRegistrations = async function (req, res) {
    try{
        let filter = {};
        const total = await kodrModel.countDocuments()
        const approved = await kodrModel.countDocuments({status: 'accepted' })
        const rejected = await kodrModel.countDocuments({status: 'rejected' })
        const onHold = await kodrModel.countDocuments({status: 'onhold' })

        if (req.query.filter && req.query.filter) {
            filter.status = req.query.filter;
        }

        if (req.query.call) {
            filter.call_status = req.query.call;
        }
        console.log(filter)
        const summary = {approved,rejected,onHold}
        const teams = await kodrModel.find(filter)
        res.render("kodrAdmin.ejs",{callFilter:req.query.call,summary,total:total,teams,filter:req.query.filter ? req.query.filter : ""})
    }catch(err){
        console.log(err)
        res.send("Internal error")
    }
}

module.exports.updateCallStatus = async function (req, res) {
    try{
        const {id,value} = req.body
        console.log(req.body.value)
        await kodrModel.findByIdAndUpdate(id,{call_status:value})
        res.status(200).json({message:"success"})
    }catch(err){
        console.log(err)
        res.status(500).json({message:"internal server error"})
    }
}

module.exports.updateStatus = async function (req, res) {
    try{
        const {id,value} = req.body
        await kodrModel.findByIdAndUpdate(id,{status:value})
        res.status(200).json({message:"success"})
    }catch(err){
        console.log(err)
        res.status(500).json({message:"internal server error"})
    }
}