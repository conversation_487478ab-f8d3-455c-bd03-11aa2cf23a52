const formidable = require("formidable");
const MarathonQuestions = require("../Models/marathonTestQuestions");
const MarathonSubmission = require("../Models/marathonTestSubmission");
const MarathonTest = require("../Models/marathonTest");
const Imagekit = require("imagekit");
const JSZip = require("jszip");
const fs = require("fs");
const axios = require("axios");
const XLSX = require('xlsx');

const codingQuestion = require("../Models/codingQuestion");
const submissionStorage = require("../Models/submissionStorage");

const imagekit = new Imagekit({
    publicKey: "public_DQRGKIIs98Gr4cHv0e2XAn73I2U=",
    privateKey: "private_Hk3XPFhDJiyZSBfqdx+bhm537O4=",
    urlEndpoint: "https://ik.imagekit.io/sheryians",
});

let headers = {
    "x-Auth-Token": process.env.JUDGE_API_KEY,
    "Content-Type": "application/json",
};

// let headers = {
//     "x-rapidapi-key": process.env.JUDGE_RAPIDAPI_KEY,
//     "x-rapidapi-host": "judge0-ce.p.rapidapi.com",
//     "Content-Type": "application/json",
// };
const parseForm = (req) => {
    return new Promise((resolve, reject) => {
        const form = new formidable.IncomingForm();

        form.parse(req, (err, fields, files) => {
            if (err) {
                return reject(err);
            }
            resolve({ fields, files });
        });
    });
};

async function getTotalMarks(questions){
    const totalMarks = questions.reduce(async (accumulatorPromise, question) => {
        const accumulator = await accumulatorPromise;
        if(question.problemId){
            const marks = await getMarksfromProblemId(question.problemId);
            return accumulator + Number(marks);
        }else{
            return accumulator + Number(question.points);
        }
    }, Promise.resolve(0));
    return totalMarks;

}

async function getMarksfromProblemId(problemId) {
    try {
        const problem = await codingQuestion.findOne({ id: problemId });
        const totalMarks = problem.mainTestcases.reduce((acc, testcase) => acc + (testcase.marks || 10), 0);
        return Number(totalMarks);
    } catch (err) {
        console.log(err);   
    }
}

function generateMarksForXlsxFile(data) {
    let allMarks = 0
    if (data.totalScore){
        allMarks = Number(data.totalScore)
    }else{

        allMarks = data.answers.reduce((accumulator, currentValue) => {
            if (currentValue.option) {
                return accumulator + Number(currentValue.correct ? currentValue.marks : 0);
            } else {
                const testcaseMarks = currentValue.testcases
                    .filter(testcase => testcase.passed)
                    .reduce((sum, testcase) => sum + Number(testcase.marks || 0), 0);
                return accumulator + testcaseMarks;
            }
        }, 0);
    }

    return {
        Name: data.user.name,
        Marks: allMarks
    }
}

async function make_C_Zip(lecture, code) {
    const zip = new JSZip();
    var submission;
    if (lecture.code_template) {
        submission = {
            "main.c": lecture.main_code || "",
            "helper.c": code || "",
            compile: `gcc -o my_program main.c helper.c`,
            run: `./my_program`,
        };
    } else {
        submission = {
            "main.c": code || "",
            compile: `gcc -o my_program main.c`,
            run: `./my_program`,
        };
    }
    try {
        for (const [fileName, fileContent] of Object.entries(submission)) {
            zip.file(fileName, fileContent);
        }
        return await zip.generateAsync({ type: "base64" });
    } catch (error) {
        console.error("Error generating Python ZIP file:", error);
        throw new Error("Failed to create Python ZIP file for submission.");
    }
}

// async function make_Java_Zip(lecture, code) {
//     const zip = new JSZip();
//     var submission;
//     if (lecture.code_template) {
//         submission = {
//             "Main.java": lecture.main_code || "",
//             "Solution.java": code || "",
//             compile: `#!/bin/bash\njavac Main.java Solution.java`,
//             run: `#!/bin/bash\njava Main`,
//         };
//     } else {
//         submission = {
//             "Main.java": code || "",
//             compile: `#!/bin/bash\njavac Main.java`,
//             run: `#!/bin/bash\njava Main`,
//         };
//     }
//     try {
//         for (const [fileName, fileContent] of Object.entries(submission)) {
//             zip.file(fileName, fileContent);
//         }
//         return await zip.generateAsync({ type: "base64" });
//     } catch (error) {
//         console.error("Error generating Python ZIP file:", error);
//         throw new Error("Failed to create Python ZIP file for submission.");
//     }
// }
async function make_Java_Zip(lecture, code) {
    const zip = new JSZip();
    var submission;
    if (lecture.code_template) {
        submission = {
            "Main.java": lecture.main_code || "",
            "Solution.java": code || "",
            compile: `javac Main.java Solution.java`,
            run: `java Main`,
        };
    } else {
        submission = {
            "Main.java": code || "",
            compile: `javac Main.java`,
            run: `java Main`,
        };
    }
    try {
        for (const [fileName, fileContent] of Object.entries(submission)) {
            zip.file(fileName, fileContent);
        }
        return await zip.generateAsync({ type: "base64" });
    } catch (error) {
        console.error("Error generating Python ZIP file:", error);
        throw new Error("Failed to create Python ZIP file for submission.");
    }
}

async function make_Python_Zip(lecture, code) {
    const zip = new JSZip();
    var submission;
    if (lecture.code_template) {
        submission = {
            "main.py": lecture.main_code || "",
            "helper.py": code || "",
            run: `python3 main.py`,
        };
    } else {
        submission = {
            "main.py": code || "",
            run: `python3 main.py`,
        };
    }
    try {
        for (const [fileName, fileContent] of Object.entries(submission)) {
            zip.file(fileName, fileContent);
        }
        return await zip.generateAsync({ type: "base64" });
    } catch (error) {
        console.error("Error generating Python ZIP file:", error);
        throw new Error("Failed to create Python ZIP file for submission.");
    }
}

// async function make_JavaScript_Zip(lecture, code) {
//     const zip = new JSZip();
//     let submission;

//     if (lecture.code_template) {
//         submission = {
//             "main.js": lecture.main_code, // The main template code for the lecture
//             "helper.js": code, // User-provided helper code
//             "run": `#!/bin/bash
//                 source /usr/local/bin/conda_init && conda activate nodejs20.17.0 && node main.js`
//         };
//     } else {
//         submission = {
//             "main.js": code, // The user's main code
//             "run": `#!/bin/bash
//                 source /usr/local/bin/conda_init && conda activate nodejs20.17.0 && node main.js`
//         };
//     }

//     for (const [fileName, fileContent] of Object.entries(submission)) {
//         zip.file(fileName, fileContent);
//     }

//     return await zip.generateAsync({ type: "base64" });
// }
async function make_JavaScript_Zip(lecture, code) {
    const zip = new JSZip();
    let submission;

    if (lecture.code_template) {
        submission = {
            "main.js": lecture.main_code, // The main template code for the lecture
            "helper.js": code, // User-provided helper code
            "run": `node main.js`
        };
    } else {
        submission = {
            "main.js": code, // The user's main code
            "run": `node main.js`
        };
    }

    for (const [fileName, fileContent] of Object.entries(submission)) {
        zip.file(fileName, fileContent);
    }

    return await zip.generateAsync({ type: "base64" });
}

async function returnSubmissionArray(lecture, codeData, code) {
    if (lecture.compilerId == 71) {
        const base64zip = await make_Python_Zip(lecture, code, codeData);
        const submissions = codeData.mainTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                ...(testcase.maxTime && { cpu_time_limit: testcase.maxTime }),
                ...(testcase.maxMemory && { memory_limit: testcase.maxMemory }),
                ...(testcase.marks && { command_line_arguments: testcase.marks }),
            };
        });
        return { submissions };
    } else if (lecture.compilerId == 62) {
        const base64zip = await make_Java_Zip(lecture, code, codeData);
        const submissions = codeData.mainTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                ...(testcase.maxTime && { cpu_time_limit: testcase.maxTime }),
                ...(testcase.maxMemory && { memory_limit: testcase.maxMemory }),
                ...(testcase.marks && { command_line_arguments: testcase.marks }),
            };
        });
        return { submissions };
    } else if (lecture.compilerId == 49) {
        const base64zip = await make_C_Zip(lecture, code, codeData);
        const submissions = codeData.mainTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                ...(testcase.maxTime && { cpu_time_limit: testcase.maxTime }),
                ...(testcase.maxMemory && { memory_limit: testcase.maxMemory }),
                ...(testcase.marks && { command_line_arguments: testcase.marks }),
            };
        });
        return { submissions };

    } else if (lecture.compilerId == 63) {
        const base64zip = await make_JavaScript_Zip(lecture, code, codeData);
        const submissions = codeData.mainTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                ...(testcase.maxTime && { cpu_time_limit: testcase.maxTime }),
                ...(testcase.maxMemory && { memory_limit: testcase.maxMemory }),
                ...(testcase.marks && { command_line_arguments: testcase.marks }),
            };
        });
        return { submissions };
    }
}

async function returnRunSubmissionArray(lecture, codeData, code) {
    if (lecture.compilerId == 71) {
        const base64zip = await make_Python_Zip(lecture, code, codeData);
        const submissions = codeData.sampleTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
            };
        });
        return submissions;
    } else if (lecture.compilerId == 62) {
        const base64zip = await make_Java_Zip(lecture, code, codeData);
        const submissions = codeData.sampleTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
            };
        });
        return submissions;
    } else if (lecture.compilerId == 49) {
        const base64zip = await make_C_Zip(lecture, code, codeData);
        const submissions = codeData.sampleTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
            };
        });
        return submissions;

    } else if (lecture.compilerId == 63) {
        const base64zip = await make_JavaScript_Zip(lecture, code, codeData);
        const submissions = codeData.sampleTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
            };
        });
        return submissions;
    }
}

const manageMarathonSubmission = async (req, codeSubmission) => {
    const { queId, markedOption, code } = req.body;
    const question = await MarathonQuestions.findById(queId);
    const submission = await MarathonSubmission.findOne({ user: req.user._id, test: question.testId });
    if (submission) {
        submissionQuestion = submission.answers.find((que) => que.question.toString() == question._id.toString());
        if (question.type == "mcq" && submissionQuestion) {
            submissionQuestion.option = markedOption;
            (submissionQuestion.correct = question.correctOption == markedOption ? true : false), (submissionQuestion.marks = question.points || 10);
        } else if (question.type == "mcq" && !submissionQuestion) {
            submission.answers.push({
                question: question._id,
                option: markedOption,
                correct: question.correctOption == markedOption ? true : false,
                marks: question.points || 0,
            });
        }
        if (question.type == "code" && submissionQuestion) {
            const correct = codeSubmission.filter((e) => e.status.id == 3).length;
            const correctPercentage = (correct / codeSubmission.length) * 100;
            submissionQuestion.code = req.body.code;
            const testcases = codeSubmission.map((e,i) => {
                return {
                    input: e.stdin,
                    output: e.stdout,
                    expected: e.expected_output,
                    passed: e.status.id == 3 || (submissionQuestion.testcases[i] && submissionQuestion.testcases[i].passed) ? true : false,
                    marks: Number(e.marks || 10),
                };
            })
            submissionQuestion.testcases = testcases;
            if (correctPercentage == 100) {
                submissionQuestion.marks = question.points;
                submissionQuestion.correct = true;
            }
        } else if (question.type == "code" && !submissionQuestion) {
            console.log("second")
            const correct = codeSubmission.filter((e) => e.status.id == 3).length;
            const correctPercentage = (correct / codeSubmission.length) * 100;
            const testcases = codeSubmission.map((e) => {
                return {
                    input: e.stdin,
                    output: e.stdout,
                    expected: e.expected_output,
                    passed: e.status.id == 3 ? true : false,
                    marks: Number(e.marks || 10),
                };
            })
            submission.answers.push({
                marks: correctPercentage == 100 ? question.points : 0,
                correct: correctPercentage == 100 ? true : false,
                question: question._id,
                testcases,
                code: req.body.code,
            });
        }
        await submission.save();
    } else {
        const submission = new MarathonSubmission();
        submission.test = question.testId;
        submission.user = req.user._id;
        if (question.type == "mcq") {
            submission.answers.push({
                question: question._id,
                option: markedOption,
                code: req.body.code,
                correct: question.correctOption == markedOption ? true : false,
                marks: question.points,
            });
        } else if (question.type == "code") {
            console.log("third")

            const correct = codeSubmission.filter((e) => e.status.id == 3).length;
            const correctPercentage = (correct / codeSubmission.length) * 100;
            const testcases = codeSubmission.map((e) => {
                return {
                    input: e.stdin,
                    output: e.stdout,
                    expected: e.expected_output,
                    passed: e.status.id == 3 ? true : false,
                    marks: Number(e.marks || 10),
                };
            })
            submission.answers.push({
                question: question._id,
                code: code,
                testcases,
                correct: correctPercentage == 100 ? true : false,
            });
        }

        await submission.save();
    }
};

module.exports.getEditMarathon = async (req, res) => {
    try {
        const submissions = await MarathonSubmission.find({ test: req.params.id }).populate({ path: "user", select: "name email" }).select("totalScore answers").sort("totalScore");
       
        // submissions.map((sub) => {
        //     return {
        //         name: sub.user.name,
        //         score: sub.totalScore
        //             ? sub.totalScore
        //             : sub.answers.reduce((accumulator, currentValue) => {
        //                 // If the answer has testcases, calculate the sum of marks for passed testcases
        //                 console.log(accumulator)
        //                 if (currentValue.option) {
        //                     return accumulator + Number(currentValue.marks || 0);
        //                 } else {
        //                     const testcaseMarks = currentValue.testcases
        //                         .filter(testcase => testcase.passed)
        //                         .reduce((sum, testcase) => sum + Number(testcase.marks || 0), 0);
        //                     return accumulator + testcaseMarks;
        //                     // console.log(currentValue)
        //                     // For non-testcase answers (like MCQs), use the original logic
        //                 }
        //             }, 0),
        //     };
        // })
        const marathon = await MarathonTest.findById(req.params.id).populate({ path: "questions"});
        const totalMarks = await marathon.questions.reduce(async (accumulatorPromise, question) => {
            const accumulator = await accumulatorPromise;
            if(question.problemId){
                const marks = await getMarksfromProblemId(question.problemId);
                return accumulator + Number(marks);
            }else{
                return accumulator + Number(question.points);
            }
        }, Promise.resolve(0));
        return res.render("createMarathon.ejs", {totalMarks, students: submissions, title: "Create Marathon", pageTitle: "", request: req, marathon });
    } catch (err) {
        console.log(err);
    }
};

module.exports.postSubmitMarathon = async (req, res) => {
    try {
        const submission = await MarathonSubmission.findOne({ user: req.user._id, test: req.body.id });
        const marathon = await MarathonTest.findById(req.body.id).populate("questions");

        const totalMarks = submission.answers.reduce((accumulator, currentValue) => {
            if (currentValue.option) {
                return accumulator + Number(currentValue.correct ? currentValue.marks : 0);
            } else {
                const testcaseMarks = currentValue.testcases.reduce((sum, testcase) => sum + Number(testcase.passed ? testcase.marks : 0), 0);
                return accumulator + testcaseMarks;
            }
        }, 0);
                
        submission.submitted = {
            status: true,
            time: Date.now(),
        };
        submission.totalScore = totalMarks;
        await submission.save();
        res.status(200).json({ marks: totalMarks, allMarks:submission.totalMarks });
    } catch (err) {
        console.log(err);
        res.status(500).json({ message: "Server Error" });
    }
};

module.exports.getMarathonTest = async (req, res) => {
    try {
        const marathon = await MarathonTest.findById(req.params.id).populate({ path: "questions"});
        let currentTimeInKolkata = new Date().toLocaleString("en-US", { timeZone: "Asia/Kolkata" });
        const currentDate = new Date(currentTimeInKolkata);
        // Parse marathon start and end times
        const marathonStartTime = new Date(new Date(currentTimeInKolkata).setHours(...marathon.startTime.split(":")));
        const marathonEndTime = new Date(new Date(currentTimeInKolkata).setHours(...marathon.endTime.split(":")));
        const marathonScheduleDate = new Date(marathon.scheduleOn);

        // Normalize dates for comparison (set time to 00:00:00)
        const normalizedCurrentDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
        const normalizedMarathonScheduleDate = new Date(marathonScheduleDate.getFullYear(), marathonScheduleDate.getMonth(), marathonScheduleDate.getDate());

        // Determine status based on the comparisons
        let status = "";
        if (marathonEndTime < currentDate || normalizedMarathonScheduleDate < normalizedCurrentDate) {
            status = "completed";
        } else if (marathonStartTime > currentDate || normalizedMarathonScheduleDate > normalizedCurrentDate) {
            status = "upcoming";
        }
        const submission = await MarathonSubmission.findOne({ user: req.user._id, test: marathon._id });
        let percentageCompleted = 0;
        let mcqCount = 0;
        let codeCount = 0;
        let completedQuestionsId = [];
        let startTime;
        if (submission) {
            percentageCompleted = (submission.answers.length / marathon.questions.length) * 100;
            mcqCount = submission.answers.filter((data) => data.option).length;
            codeCount = submission.answers.filter((data) => data.code).length;
            completedQuestionsId = submission.answers.map((data) => data.question.toString());
            startTime = submission.startTime;
        } else {
            const tempSubmission = await MarathonSubmission.create({
                user: req.user._id,
                test: req.params.id,
                startTime: new Date().toLocaleString("en-US", { timeZone: "Asia/Kolkata" }),
                totalMarks:await getTotalMarks(marathon.questions)
            });
            startTime = tempSubmission.startTime;
        }

        const lastLectureId = submission?.answers[submission.answers.length - 1];
        const currentDateInKolkata = new Date(currentTimeInKolkata);
        console.log(marathon.scheduleOn,marathon.endTime)
        const marathonEndTimeInKolkata = new Date(marathon.scheduleOn).setHours(...marathon.endTime.split(":"));
        let minuteRemianing = Math.max(0, Math.floor((marathonEndTimeInKolkata - currentDateInKolkata) / (1000 * 60)));
        if(req.user.admin){
            status = ""
            minuteRemianing = 30
        } 
        return res.render("marathon.ejs", { lastLectureId: { id: lastLectureId?.question, type: lastLectureId?.option ? "mcq" : "code" }, title: "Marathon", pageTitle: "", completedQuestionsId, percentageCompleted, codeCount, mcqCount, request: req, marathon, minuteRemianing, alreadySubmitted: submission && submission.submitted.status ? true : false, status });
    } catch (err) {
        console.log(err);
    }
};

module.exports.getReviewMarathonTest = async (req, res) => {
    try {
        const marathon = await MarathonTest.findById(req.params.id).populate({ path: "questions", select: "type title _id points" });
        let submission;
        if (req.user.admin) {
            submission = await MarathonSubmission.findOne({ user: req.query.userid ? req.query.userid : req.user._id, test: marathon._id });
        } else {
            submission = await MarathonSubmission.findOne({ user: req.user._id, test: marathon._id });
        }
        let percentageCompleted = 0;
        let mcqCount = 0;
        let codeCount = 0;
        if (submission) {
            percentageCompleted = (submission.answers.length / marathon.questions.length) * 100;
            mcqCount = submission.answers.filter((data) => data.option).length;
            codeCount = submission.answers.filter((data) => data.code).length;
        } else {
            return res.redirect(`/marathon/${marathon._id}`);
        }
        let currentTimeInKolkata = new Date().toLocaleString("en-US", { timeZone: "Asia/Kolkata" });
        let marathonEndTime = new Date(new Date(marathon.scheduleOn).setHours(...marathon.endTime.split(":")));
        let status = marathonEndTime.getTime() < new Date(currentTimeInKolkata).getTime() ? "completed" : "incomplete";
        if (status == "incomplete") {
            return res.send("Test hasn't ended yet");
        }
        return res.render("reviewMarathon.ejs", { status, userId: req.user.admin ? req.query.userid : req.user._id, title: "Marathon", pageTitle: "", completedQuestionsId: submission.answers, percentageCompleted, codeCount, mcqCount, request: req, marathon });
    } catch (err) {
        console.log(err);
    }
};

module.exports.getAllMarathons = async (req, res) => {
    const marathons = await MarathonTest.find({}).populate("creator");
    return res.render("allMarathon.ejs", { marathons });
};

module.exports.getDeleteMarathon = async (req, res) => {
    await MarathonTest.findByIdAndDelete(req.params.id);
    return res.redirect("/marathon");
};

module.exports.getCreateQuestion = (req, res) => {
    return res.render("createMarathonQue.ejs", { question: null });
};

module.exports.getUpdateQuestion = async (req, res) => {
    const question = await MarathonQuestions.findById(req.params.id);
    if (!question) return res.status(404).json({ message: "Question not found" });
    return res.render("createMarathonQue.ejs", { question });
};

module.exports.getCreateMarathon = (req, res) => {
    return res.render("createMarathon.ejs", { students: [], title: "Create Marathon", pageTitle: "", request: req, marathon: null });
};

module.exports.getQuestion = async (req, res) => {
    const questions = await MarathonQuestions.find({});
    return res.render("marathonQue.ejs", { questions });
};

module.exports.postCreateMarathon = async (req, res) => {
    try {
        const { title, batch, duration, scheduleOn, startTime, endTime, status, questions, allowed_users } = req.body;
        const marathon = await MarathonTest.create({
            creator: req.user._id,
            title,
            batch,
            questions,
            duration,
            status,
            scheduleOn,
            startTime,
            endTime,
            allowed_users,
        });

        await MarathonQuestions.updateMany(
            { _id: { $in: questions } }, // Find questions with IDs in questionIds array
            { $set: { testId: marathon._id } } // Set marathonId (or testId) to the provided value
        );
        return res.status(200).json({ id: marathon._id });
    } catch (err) {
        console.log(err);
    }
};

module.exports.getUpdateMarathon = async (req, res) => {
    try {
        const marathonId = req.params.id;

        let marathon = await MarathonTest.findById(marathonId);
        if (!marathon) {
            return res.status(404).send("Marathon not found");
        }

        const { title, batch, duration, scheduleOn, startTime, endTime, status, questions, allowed_users } = req.body;

        marathon.title = title || marathon.title;
        marathon.batch = batch || marathon.batch;
        marathon.duration = duration || marathon.duration;
        marathon.scheduleOn = scheduleOn || marathon.scheduleOn;
        marathon.startTime = startTime || marathon.startTime;
        marathon.endTime = endTime || marathon.endTime;
        marathon.status = status || marathon.status;
        marathon.questions = questions || marathon.questions;
        marathon.allowed_users = allowed_users || marathon.allowed_users;
        await marathon.save();

        await MarathonQuestions.updateMany(
            { _id: { $in: questions } }, // Find questions with IDs in questionIds array
            { $set: { testId: marathon._id } } // Set marathonId (or testId) to the provided value
        );

        return res.status(200).json({ message: "Update Successful" });
    } catch (err) {
        console.error(err);
        return res.status(500).json({ message: "An error occurred while updating the marathon." });
    }
};

module.exports.postGetMarathonQuestion = async (req, res) => {
    try {
        const lecture = await MarathonQuestions.findById(req.params.id);
        const attempted = await MarathonSubmission.findOne({ user: req.user._id, test: lecture.testId });
        let attemptedQuestion;
        if (attempted) {
            attemptedQuestion = attempted.answers.find((e) => e.question.toString() == lecture._id.toString());
        }
        if (lecture.type == "mcq") {
            return res.render("marathonMCQ.ejs", { completed: false, attempted: attemptedQuestion ? attemptedQuestion : "", testId: lecture.testId, question: lecture.question, options: lecture.options, points: lecture.points, title: lecture.title, lectureId: lecture._id, explanation: lecture.explanation, questionImage: lecture.questionImage, explanationVideo: lecture.explanationVideo, markedOption: false, correctOption: false, codeSnippet: lecture.codeSnippet, codeSnippetLanguage: lecture.codeSnippetLanguage, userId: req.user._id });
        } else {
            const problem = await codingQuestion.findOne({ id: lecture.problemId });
            return res.render("marathonCode.ejs", { completed: false, attempted: attemptedQuestion ? attemptedQuestion : "", lecture, testId: lecture.testId, problem, userId: req.user._id });
        }
    } catch (err) {
        console.log(err);
        return res.status(500).json({ message: "Internal server error" });
    }
};

module.exports.postGetMarathonQuestionResult = async (req, res) => {
    try {
        const lecture = await MarathonQuestions.findById(req.params.id);
        const attempted = await MarathonSubmission.findOne({ user: req.params.userid, test: lecture.testId });
        let attemptedQuestion;
        let completed;

        if (attempted) {
            attemptedQuestion = attempted.answers.find((e) => e.question.toString() == lecture._id.toString());
        }
        if (lecture.type == "mcq") {
            return res.render("marathonMCQ.ejs", { completed: true, attempted: attemptedQuestion ? attemptedQuestion : "", testId: lecture.testId, question: lecture.question, options: lecture.options, points: lecture.points, title: lecture.title, lectureId: lecture._id, explanation: lecture.explanation, questionImage: lecture.questionImage, explanationVideo: lecture.explanationVideo, markedOption: false, codeSnippet: lecture.codeSnippet, codeSnippetLanguage: lecture.codeSnippetLanguage, userId: req.user._id, correctOption: lecture.correctOption });
        } else {
            const problem = await codingQuestion.findOne({ id: lecture.problemId });
            console.log(attemptedQuestion)
            return res.render("marathonCode.ejs", { completed: true, attempted: attemptedQuestion ? attemptedQuestion : "", lecture, testId: lecture.testId, problem, userId: req.user._id });
        }
    } catch (err) {
        console.log(err);
        return res.status(500).json({ message: "Internal server error" });
    }
};

module.exports.postGetQuestion = async (req, res) => {
    try {
        const searchTerm = req.body.searchTerm || "";
        if (!searchTerm) return res.status(200).json({ questions: [] });

        const regex = new RegExp(searchTerm, "i");
        const questions = await MarathonQuestions.find({ title: regex }).select("_id title type");

        return res.status(200).json({ questions });
    } catch (err) {
        return res.status(500).json({ message: "Internal server error" });
    }
};

module.exports.postUpdateCodeQuestion = async (req, res) => {
    const marathonOriginalQuestion = await MarathonQuestions.findById(req.params.id);
    console.log(await getMarksfromProblemId(marathonOriginalQuestion.problemId));

    const { title, problemId, problem_name, points, main_code, code_template, compilerId, difficulty } = req.body;
    const marathonQuestion = await MarathonQuestions.findByIdAndUpdate(req.params.id, {
        type: "code",
        title: title || marathonOriginalQuestion.title,
        problemId: problemId || marathonOriginalQuestion.problemId,
        problem_name: problem_name || marathonOriginalQuestion.problem_name,
        main_code: main_code || marathonOriginalQuestion.main_code,
        code_template: code_template || marathonOriginalQuestion.code_template,
        compilerId: compilerId || marathonOriginalQuestion.compilerId,
        difficulty: difficulty || marathonOriginalQuestion.difficulty,
        points: await getMarksfromProblemId(problemId) || marathonOriginalQuestion.points,
    });
    res.status(200).json({ message: "success" });
};

module.exports.postUpdateMCQQuestion = async (req, res) => {
    try {
        var result;
        const { fields, files } = await parseForm(req);
        const marathonOriginalQuestion = await MarathonQuestions.findById(req.params.id);
        const { title, question, marks, codeSnippet, codeSnippetLanguage, correctOption } = fields;
        const choice = JSON.parse(fields.choice);

        if (files.questionImage) {
            const filePath = files.questionImage[0].filepath;
            questionImageresult = await imagekit.upload({
                file: fs.createReadStream(filePath),
                fileName: files.questionImage[0].newFilename,
            });
        }
        const marathonQuestion = await MarathonQuestions.findByIdAndUpdate(req.params.id, {
            type: "mcq",
            title: title[0] || marathonOriginalQuestion.title,
            points: marks[0] || marathonOriginalQuestion.points,
            options: choice || marathonOriginalQuestion.options,
            correctOption: correctOption[0] || marathonOriginalQuestion.correctOption,
            questionImage: files.questionImage ? questionImageresult.url : marathonOriginalQuestion.questionImage,
            question: question[0] || marathonOriginalQuestion.question,
            codeSnippetLanguage: codeSnippetLanguage[0] || marathonOriginalQuestion.codeSnippetLanguage,
            codeSnippet: codeSnippet[0] || marathonOriginalQuestion.codeSnippet,
        });
        res.status(200).json({ message: "success" });
    } catch (err) {
        console.log(err);
        res.status(400).json({
            message: "Oops! Something went wrong, please report it to Anurag",
        });
    }
};
module.exports.addMcqLectures = async (req, res) => {
    try {
        var result;
        const { fields, files } = await parseForm(req);
        const { title, question, marks, codeSnippet, codeSnippetLanguage, correctOption } = fields;
        const choice = JSON.parse(fields.choice);

        // if (files.file) {
        //     const filePath = files.file[0].filepath;
        //     result = await imagekit.upload({
        //         file: fs.createReadStream(filePath),
        //         fileName: files.file[0].newFilename,
        //     });
        // }

        if (files.questionImage) {
            const filePath = files.questionImage[0].filepath;
            questionImageresult = await imagekit.upload({
                file: fs.createReadStream(filePath),
                fileName: files.questionImage[0].newFilename,
            });
        }

        const marathonQuestion = await MarathonQuestions.create({
            type: "mcq",
            title: title[0],
            // explanation: explaination[0],
            // explanationVideo: files.file ? result.url : "",
            points: marks[0],
            options: choice,
            correctOption: correctOption[0],
            questionImage: files.questionImage ? questionImageresult.url : "",
            question: question[0],
            codeSnippetLanguage: codeSnippetLanguage[0],
            codeSnippet: codeSnippet[0],
        });
        res.status(200).json({ message: "success" });
    } catch (err) {
        console.log(err);
        res.status(400).json({
            message: "Oops! Something went wrong, please report it to Anurag",
        });
    }
};

module.exports.postSubmitMCQ = async (req, res) => {
    if (![1, 2, 3, 4].includes(req.body.markedOption)) return res.status(404).json({ message: "Not a valid option" });
    manageMarathonSubmission(req, [], "");
    return res.status(200).json({ message: "Response Registered" });
};

module.exports.resetSubmission = async (req, res) => {
    try {
        await MarathonSubmission.findOneAndDelete({user:req.params.userID,test:req.params.marathonID})
        res.status(200).json({ message: "success" });
    } catch (err) {
        console.log(err);
        res.status(400).json({ message: "Oops! Something went wrong, please report it to Anurag" });
    }
};

module.exports.addCodeLectures = async (req, res) => {
    try {
        const { title, problemId, problem_name, main_code, code_template, compilerId, difficulty } = req.body;
        const points = await getMarksfromProblemId(problemId);

        const marathonQuestion = await MarathonQuestions.create({
            type: "code",
            title: title,
            problemId: problemId,
            problem_name: problem_name,
            main_code: main_code,
            code_template: code_template,
            compilerId: compilerId,
            difficulty: difficulty,
            points: points,
        });
        res.status(200).json({ message: "success" });
    } catch (err) {
        console.log(err);
        res.status(400).json({ message: "Oops! Something went wrong, please report it to Anurag" });
    }
};

module.exports.runCode = async (req, res) => {
    try {
        const { code, queId } = req.body;

        if (!code || typeof code !== "string" || code.trim() === "") {
            return res.status(400).json({ message: "Code is required and must be a non-empty string" });
        }
        
        const result = await MarathonQuestions.findById(queId);
        if (!result) {
            console.error(`Lecture not found for queId: ${queId}`);
            return res.status(400).json({ message: "Lecture Not Found" });
        }

        const problem = await codingQuestion.findOne({ id: result.problemId });
        if (!problem) {
            console.error(`Coding question not found for problemId: ${result.problemId}`);
            return res.status(400).json({ message: "Coding Question Not Found" });
        }
        const submissions = await returnRunSubmissionArray(result, problem, code);
        console.log(submissions)
        const submissionRoute = submissions.map((data) => {
            const options = {
                method: "POST",
                url: process.env.JUDGE_URL+"/submissions",
                params: {
                    base64_encoded: "true",
                    wait: "false",
                    fields: "*",
                },
                headers: headers,
                data: data,
            };

            return axios
                .request(options)
                .then((response) => response.data)
                .catch((error) => {
                    console.error("Error in submission:", error.message);
                    return null; // or handle the error as needed
                });
        });

        const response = await Promise.all(submissionRoute);
        const tokens = response.map((data) => data.token);
        console.log(tokens)
        res.status(200).json({ tokens: tokens });
    } catch (err) {
        console.log(err);
        res.status(500).json({ message: "Internal Error", error: err.message });
    }
};

module.exports.submitCode = async (req, res) => {
    try {
        const { code, queId } = req.body;
        if (!code) return res.status(400).json({ message: "Fields Missing" });

        if (!code || typeof code !== "string" || code.trim() === "") {
            return res.status(400).json({ message: "Code is required and must be a non-empty string" });
        }
        
        if (!queId) {
            return res.status(400).json({ message: "Invalid or missing Question ID" });
        }

        const result = await MarathonQuestions.findById(queId);
        if (!result) {
            console.error(`Lecture not found for queId: ${queId}`);
            return res.status(400).json({ message: "Lecture Not Found" });
        }

        const problem = await codingQuestion.findOne({ id: result.problemId });
        if (!problem) {
            console.error(`Coding question not found for problemId: ${result.problemId}`);
            return res.status(400).json({ message: "Coding Question Not Found" });
        }

        const submissions = await returnSubmissionArray(result, problem, code);
        // Validate submission payload
            if (!Array.isArray(submissions.submissions) , submissions.submissions.some(sub => !sub.language_id || !sub.additional_files)) {
            console.error("Invalid submissions array:", submissions);
            return res.status(500).json({ message: "Invalid submission payload" });
        }
        const response = await axios.request({
            method: "POST",
            url: process.env.JUDGE_URL + "/submissions/batch",
            params: { base64_encoded: "true" },
            headers: headers,
            data: submissions,
        });
        
        if (!response || !response.data) {
            console.error("Empty response from Judge0 API:", response);
            return res.status(500).json({ message: "Failed to process submissions" });
        }
        
        res.status(200).json({ tokens: response.data });
    } catch (err) {
        console.log(err)
        console.error("Error during submission:", err.message, err.stack);
        res.status(500).json({
            message: "Internal Error",
            error: err,
            details: err.stack,
        });
    }
};

module.exports.checkStatus = async (req, res) => {
    try {
        const { data } = await axios.request({
            method: "GET",
            url: process.env.JUDGE_URL+"/submissions/batch",
            params: {
                tokens: req.body.token.join(","),
                base64_encoded: "true",
                fields: "*",
            },
            headers: headers,
        });
        const newData = data.submissions.map((submission) => ({
            time: submission.time,
            memory: submission.memory,
            stdin: submission.stdin ? Buffer.from(submission.stdin, "base64").toString("utf-8") : submission.stderr,
            stdout: submission.stdout ? Buffer.from(submission.stdout, "base64").toString("utf-8") : submission.stderr,
            expected_output: submission.expected_output ? Buffer.from(submission.expected_output, "base64").toString("utf-8") : submission.stderr,
            stderr: submission.stderr ? Buffer.from(submission.stderr, "base64").toString("utf-8") : submission.stderr,
            message: submission.message ? Buffer.from(submission.message, "base64").toString("utf-8") : submission.message,
            status: submission.status,
            compile_error: submission.compile_output ? Buffer.from(submission.compile_output, "base64").toString("utf-8") : submission.compile_output,
            error: submission.error,
            marks: submission.command_line_arguments || 0, // Extract marks from metadata
        }));
        res.status(200).json({ data: newData });
        if (data.submissions.every((state) => state.status.id != 1 && state.status.id != 2)) {
            manageMarathonSubmission(
                req,
                newData.map((e) => {return {stdin:e.stdin,stdout:e.stdout,expected_output:e.expected_output,status:e.status,marks:e.marks,time:e.time,memory:e.memory}}),
            );
        }
    } catch (err) {
        console.log(err);
        console.log(err.message);
    }
};

module.exports.sendResult = async (req, res) => {
    try{
        const { testId } = req.params;
        const testName = await MarathonTest.findById(testId)
        // Fetch submissions from the database
        const submissions = await MarathonSubmission.find({ test: testId })
            .populate("user")
    
        // Prepare data for Excel
        const rows = submissions.map((submission) => generateMarksForXlsxFile(submission));
        console.log(rows)
        // Add a heading row and column headers
        const sheetData = [
            [{ v: "testName", s: { bold: true }, span: 2 }], // Merge and add test name
            ["Name", "Marks"], // Column headers
            ...rows.map((row) => [row.Name, row.Marks]), // Data rows
        ];
    
        // Create a new workbook and worksheet
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.aoa_to_sheet(sheetData);
    
        // Merge the header cell to span two columns
        worksheet["!merges"] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 1 } }]; // Row 0, col 0 to col 1
    
        // Append the worksheet to the workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, "Test Results");
    
        // Write the Excel file to a buffer
        const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
        // Set headers for file download
        res.setHeader('Content-Disposition', `attachment; filename="results.xlsx"`);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    
        // Send the buffer as a response
        res.send(buffer);
    }catch(err){
        console.log(err)
    }
}

module.exports.checkRunStatus = async (req, res) => {
    try {
        const checkRoute = req.body.token.map((token) =>
            axios.request({
                method: "GET",
                url: process.env.JUDGE_URL+`/submissions/${token}`,
                params: {
                    base64_encoded: "true",
                    fields: "*",
                },
                headers: headers,
            })
        );
        const response = await Promise.all(checkRoute);
        const data = response.map((deta) => ({
            language_id: deta.data.language_id,
            stdin: deta.data.stdin ? Buffer.from(deta.data.stdin, "base64").toString("utf-8") : deta.data.stdin,
            expected_output: deta.data.expected_output ? Buffer.from(deta.data.expected_output, "base64").toString("utf-8") : deta.data.expected_output,
            stdout: deta.data.stdout ? Buffer.from(deta.data.stdout, "base64").toString("utf-8") : deta.data.stdout,
            time: deta.data.time,
            memory: deta.data.memory,
            stderr: deta.data.stderr ? Buffer.from(deta.data.stderr, "base64").toString("utf-8") : deta.data.stderr,
            token: deta.data.token,
            compile_error: deta.data.compile_output ? Buffer.from(deta.data.compile_output, "base64").toString("utf-8") : deta.data.compile_output,
            message: deta.data.message ? Buffer.from(deta.data.message, "base64").toString("utf-8") : deta.data.message,
            status: deta.data.status,
        }));

        res.status(200).json({ data });
    } catch (err) {
        console.log(err.message);
    }
};

module.exports.saveCode = async (req, res) => {
    try {
        const { quesId, code } = req.body;
        const question = await MarathonQuestions.findById(quesId);
        if (!quesId) return res.status(401).json({ message: "Question not found" });

        const submission = await MarathonSubmission.findOne({ user: req.user._id, test: question.testId });
        if (submission) {
            submissionQuestion = submission.answers.find((que) => que.question.toString() == question._id.toString());
            if (question.type == "code" && submissionQuestion) {
                submissionQuestion.code = code;
            } else if (question.type == "code" && !submissionQuestion) {
                submission.answers.push({ question: quesId, code, marks: 0 });
            }
            await submission.save();
        } else {
            const submission = new MarathonSubmission();
            submission.answers.push({
                question: question._id,
                code: code,
                marks: 0,
            });
            await submission.save();
        }
        res.status(200).json({ message: "success" });
    } catch (err) {
        console.log(err);
        res.status(500).json({ message: "Server Error" });
    }
};

// async function cleanseArray(){
//     const marathonSubmission = await MarathonSubmission.find({test:"66f83d28991455ae0476ae00"})
//     marathonSubmission.forEach(async (submission)=>{
//         submission.answers = submission.answers.filter((value, index, self) =>
//             index === self.findIndex((obj) => obj.question.toString() === value.question.toString())
//         );
//         console.log(submission.answers)  
//         // await submission.save()
//     })
//     marathonSubmission.forEach(async (submission)=>{
//         let marks = 0
//         submission.answers.forEach(async (answer)=>{
//             marks += Number(answer.marks)
//         })
//         submission.totalScore = marks
//         await submission.save()
//     })
//     console.log("done")
    
// }

// cleanseArray()
const xlsx = require("xlsx");

module.exports.getMarathonData = async (req,res) => {
    try {
        const testId = req.params.testId;

        // Fetch submissions from the database
        const submissions = await MarathonSubmission.find({ test: req.params.id })
            .populate("user")
            .sort({ totalScore: -1 });

        // Prepare data for the Excel file
        const data = submissions.map((submission) => ({
            Name: submission.user.name,
            Marks: submission.totalScore,
            Email: submission.user.email,
        }));

        // Convert the data to a worksheet
        const worksheet = xlsx.utils.json_to_sheet(data);

        // Create a new workbook and append the worksheet
        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, worksheet, "Submissions");

        // Write the workbook to a buffer
        const excelBuffer = xlsx.write(workbook, { type: "buffer", bookType: "xlsx" });

        // Set the response headers to prompt download
        res.setHeader("Content-Disposition", `attachment; filename=Submissions_${testId}.xlsx`);
        res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        // Send the Excel file as a response
        res.send(excelBuffer);
    } catch (error) {
        console.error("Error generating Excel file:", error);
        res.status(500).send("An error occurred while generating the Excel file.");
    }
}

