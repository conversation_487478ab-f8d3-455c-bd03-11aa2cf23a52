const { body, validationResult } = require("express-validator");
const he = require("he"); // HTML entity decoder

// Middleware to validate src registration fields
module.exports.validateSrcFields = [
    body("fullname").trim().notEmpty().withMessage("Full name is required."),
    body("phone").trim().isLength({ min: 8 }).withMessage("Valid phone number is required."),
    body("email").trim().isEmail().withMessage("Valid email is required."),
    body("expertise").isArray({ min: 1 }).withMessage("Expertise must be a non-empty array."),
    body("expertise.*")
        .customSanitizer((value) => he.decode(value))
        .trim()
        .isIn(["AI/ML", "Web Development", "Cybersecurity", "DevOps", "Product Thinker", "UI/UX", "Cinematography", "Video Editing", "Social Strategist", "Electronics & IoT", "Other", "Web 3.0"])
        .withMessage("Each expertise must be a valid option."),
    body("city").trim().notEmpty().withMessage("City is required."),
    body("worklink").notEmpty().withMessage("Work link must be an array."),
    body("applied_for_role")
        .customSanitizer((value) => he.decode(value))
        .trim()
        .isIn(["Web Developer", "AI / ML Engineer", "Cybersecurity Lead", "DevOps Engineer", "Creative Technologist", "Product Thinker", "UI/UX Designer", "Video Editor / Media Lead", "Content & Social Strategist", "Operations Lead", "Vision & Strategy Lead", "none"])
        .withMessage("Valid applied role is required."),
    // Optional fields: socialProfile, techBuild
    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(422).json({ success: false, errors: errors.mapped() });
        }
        next();
    },
];

// Middleware to validate check-exists endpoint
module.exports.validateCheckExists = [
    body("type").trim().notEmpty().withMessage("Type is required.").isIn(["email", "phone"]).withMessage("Type must be either 'email' or 'phone'"),
    body("value")
        .trim()
        .notEmpty()
        .withMessage("Value is required.")
        .custom((value, { req }) => {
            // Apply specific validation based on type
            if (req.body.type === "email") {
                // Use express-validator's isEmail
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    throw new Error("Invalid email format");
                }
            } else if (req.body.type === "phone") {
                // Basic phone validation
                if (!/^\+?[0-9\s\-()]{8,20}$/.test(value)) {
                    throw new Error("Invalid phone number format");
                }
            }
            return true;
        }),
    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(422).json({ success: false, errors: errors.mapped() });
        }
        next();
    },
];
