const { name } = require('ejs');
const courses = require('../Models/Courses.js');
const courses_content = require('../Models/classroom_content2.5.js');
const User = require('../Models/user.js');

const meritto = require('../utils/meritto.js');

const { parsePhoneNumber } = require('libphonenumber-js');

function parsePhone(phoneStr) {
  try {
    const parsed = parsePhoneNumber(phoneStr);
    return {
      countryCode: `+${parsed.countryCallingCode}`,
      number: parsed.nationalNumber
    };
  } catch (err) {
    return {
      countryCode: '',
      number: phoneStr 
    };
  }
}

module.exports.getCoursesDetails = async function (req, res) {
    let options = {};
    if(req.params.name){
        options = { course_name: req.params.name };
    }else {
        options = { _id: req.params.course_id };
    }

    courses.findOne(options).select('+enrolled_students').exec(async (err, course) => {
        if (!course) {
            console.log(err);
            return res.redirect('back');
        }
        if (course.commingSoon || course.course_status == "inactive") {
            return res.redirect('/courses');
        }
        
        const contentPipeline = [
            // Match documents based on the courseId
            { 
                $match: { courseId: course._id} 
            },
            // Sort documents by the order field
            { 
                $sort: { order: 1 } 
            },
            // Project the required structure
            { 
                $project: {
                    _id: 0,
                    title: 1,
                    modules: {
                        $map: {
                            input: "$modules",
                            as: "module",
                            in: {
                                title: "$$module.title"
                            }
                        }
                    }
                }
            }
        ];
        const content = await courses_content.aggregate(contentPipeline);
        // console.log(content)
        // return res.json(content)
        
        if (req.user) {
            User.findOne({ _id: req.user._id }, async (err, user) => {
                if (err) {
                    console.log(err);
                    return res.status(500).send("Internal Server Error");
                }
                try {
                    var status = course.enrolled_students.filter((student) => student.student_id.toString() == req.user._id.toString())[ 0 ].status
                } catch (err) {
                    status = "not enrolled"
                }
                if (status == "not enrolled") {
                    const enrolledUser = await User.find({ _id: req.user.id, enrolledCourses: { $in: [ courses._id ] } }).populate('enrolledCourses').exec();
                    if (enrolledUser.length > 0) {
                        course.enrolled_students.push({
                            status: "paid",
                            student_id: req.user._id
                        })
                        status = "paid"
                        await course.save()
                    } else {
                        status = "pending"
                    }
                }

                let batch_full = 0;
                if (course.course_id == 'proj2') {
                    batch_full = 101;
                }

                const getCountryCodeandPhone = parsePhone(user.phoneNumber);

                req.session.returnTo = req.originalUrl;
                if(course.course_id == 'rb01'){
                    // check if user is already enrolled in the course or not
                    const isEnrolled = user.enrolledCourses.includes(course._id);
                    if (!isEnrolled && user.isVerified) {
                        meritto.updateCourseLead({
                            user_id: user.crm_user_id,
                            name: user.name,
                            country_dial_code: getCountryCodeandPhone.countryCode,
                            mobile: getCountryCodeandPhone.number,
                            email: user.email,
                            field_current_status_sheriyans: "Explore",
                            field_form_stage: "Explore Batch",
                            field_application_status: "Explore Batch",
                            source: req.cookies.source || "website",
                            medium: req.cookies.medium || "organic",
                            campaign: req.cookies.campaign || "organic"
                        }, course.course_id, user._id);
                    }
                    return res.render('course-detail-pages/backend-domination.ejs', {
                        title: course.course_name,
                        pageTitle: "Details",
                        course: course,
                        status,
                        user: user,
                        request: req,
                        content,
                        batch_status: batch_full
                    });
                }
                if(course.course_id == 'mco-01'){
                    // check if user is already enrolled in the course or not
                    const isEnrolled = user.enrolledCourses.includes(course._id);
                    if (!isEnrolled && user.isVerified) {
                        meritto.updateCourseLead({
                            user_id: user.crm_user_id,
                            name: user.name,
                            country_dial_code: getCountryCodeandPhone.countryCode,
                            mobile: getCountryCodeandPhone.number,
                            email: user.email,
                            field_current_status_sheriyans: "Explore",
                            field_form_stage: "Explore Batch",
                            field_application_status: "Explore Batch",
                            source: req.cookies.source || "website",
                            medium: req.cookies.medium || "organic",
                            campaign: req.cookies.campaign || "organic"
                        }, course.course_id, user._id);
                    }
                    return res.render('course-detail-pages/job-ready-live-batch.ejs', {
                        title: course.course_name,
                        pageTitle: "Details",
                        course: course,
                        status,
                        user: user,
                        request: req,
                        content,
                        batch_status: batch_full
                    });
                }
                if(course.course_id == 'rf01'){
                    // check if user is already enrolled in the course or not
                    const isEnrolled = user.enrolledCourses.includes(course._id);
                    if (!isEnrolled && user.isVerified) {
                        meritto.updateCourseLead({
                            user_id: user.crm_user_id,
                            name: user.name,
                            country_dial_code: getCountryCodeandPhone.countryCode,
                            mobile: getCountryCodeandPhone.number,
                            email: user.email,
                            field_current_status_sheriyans: "Explore",
                            field_form_stage: "Explore Batch",
                            field_application_status: "Explore Batch",
                            source: req.cookies.source || "website",
                            medium: req.cookies.medium || "organic",
                            campaign: req.cookies.campaign || "organic"
                        }, course.course_id, user._id);
                    }
                    return res.render('course-detail-pages/frontend-domination.ejs', {
                        title: course.course_name,
                        pageTitle: "Details",
                        course: course,
                        status,
                        content,
                        user: user,
                        request: req,
                        batch_status: batch_full
                    });
                }
                if(course.course_id == 'ap01'){
                    // check if user is already enrolled in the course or not
                    const isEnrolled = user.enrolledCourses.includes(course._id);
                    if (!isEnrolled && user.isVerified) {
                        meritto.updateCourseLead({
                            user_id: user.crm_user_id,
                            name: user.name,
                            country_dial_code: getCountryCodeandPhone.countryCode,
                            mobile: getCountryCodeandPhone.number,
                            email: user.email,
                            field_current_status_sheriyans: "Explore",
                            field_form_stage: "Explore Batch",
                            field_application_status: "Explore Batch",
                            source: req.cookies.source || "website",
                            medium: req.cookies.medium || "organic",
                            campaign: req.cookies.campaign || "organic"
                        }, course.course_id, user._id);
                    }
                    return res.render('course-detail-pages/aptitude-reasoning.ejs', {
                        title: course.course_name,
                        pageTitle: "Details",
                        course: course,
                        status,
                        content,
                        user: user,
                        request: req,
                        batch_status: batch_full
                    });
                }
                if(course.course_id == 'dsa01'){
                    // check if user is already enrolled in the course or not
                    const isEnrolled = user.enrolledCourses.includes(course._id);
                    if (!isEnrolled && user.isVerified) {
                        meritto.updateCourseLead({
                            user_id: user.crm_user_id,
                            name: user.name,
                            country_dial_code: getCountryCodeandPhone.countryCode,
                            mobile: getCountryCodeandPhone.number,
                            email: user.email,
                            field_current_status_sheriyans: "Explore",
                            field_form_stage: "Explore Batch",
                            field_application_status: "Explore Batch",
                            source: req.cookies.source || "website",
                            medium: req.cookies.medium || "organic",
                            campaign: req.cookies.campaign || "organic"
                        }, course.course_id, user._id);
                    }
                    return res.render('course-detail-pages/java-dsa-domination.ejs', {
                        title: course.course_name,
                        pageTitle: "Details",
                        course: course,
                        status,
                        content,
                        user: user,
                        request: req,
                        batch_status: batch_full
                    });
                }
                if(course.course_id == 'tj01'){
                    // check if user is already enrolled in the course or not
                    const isEnrolled = user.enrolledCourses.includes(course._id);
                    if (!isEnrolled && user.isVerified) {
                        meritto.updateCourseLead({
                            user_id: user.crm_user_id,
                            name: user.name,
                            country_dial_code: getCountryCodeandPhone.countryCode,
                            mobile: getCountryCodeandPhone.number,
                            email: user.email,
                            field_current_status_sheriyans: "Explore",
                            field_form_stage: "Explore Batch",
                            field_application_status: "Explore Batch",
                            source: req.cookies.source || "website",
                            medium: req.cookies.medium || "organic",
                            campaign: req.cookies.campaign || "organic"
                        }, course.course_id, user._id);
                    }
                    return res.render('course-detail-pages/threejs-domination.ejs', {
                        title: course.course_name,
                        pageTitle: "Details",
                        course: course,
                        status,
                        content,
                        user: user,
                        request: req,
                        batch_status: batch_full
                    });
                }
                if(course.course_id == 'combo1'){
                    const coursesArray  = ["66bd174049ff6f892bacdd47","6686990a672568d40bfbac7b"]
                    
                    const comboCourses = await courses.find({ _id: { $in: coursesArray } }).select('+enrolled_students').exec();
                    return res.render('course-detail-pages/combo1.ejs', {
                        title: course.course_name,
                        pageTitle: "Details",
                        course: course,
                        status,
                        content,
                        user: user,
                        request: req,
                        comboCourses,
                        batch_status: batch_full
                    });
                }
                if(course.course_id == "ddc01"){
                    // check if user is already enrolled in the course or not
                    const isEnrolled = user.enrolledCourses.includes(course._id);
                    if (!isEnrolled && user.isVerified) {
                        meritto.updateCourseLead({
                            user_id: user.crm_user_id,
                            name: user.name,
                            country_dial_code: getCountryCodeandPhone.countryCode,
                            mobile: getCountryCodeandPhone.number,
                            email: user.email,
                            field_current_status_sheriyans: "Explore",
                            field_form_stage: "Explore Batch",
                            field_application_status: "Explore Batch",
                            source: req.cookies.source || "website",
                            medium: req.cookies.medium || "organic",
                            campaign: req.cookies.campaign || "organic"
                        }, course.course_id, user._id);
                    }
                    return res.render('course-detail-pages/dsa-domination-cohort.ejs', {
                        title: course.course_name,
                        pageTitle: "Details",
                        course: course,
                        status,
                        content,
                        user: user,
                        request: req,
                        batch_status: batch_full
                    });
                }


                return res.render('CoursesDetails', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    status,
                    content,
                    user: user,
                    request: req,
                    batch_status: batch_full
                });
            });
        } else {
            let batch_full = 0;
            let checkPaid = 0;
            if (course.course_id == 'proj2') {
                batch_full = 101;
            }
            req.session.returnTo = req.originalUrl;
            if(course.course_id == 'rb01'){
                return res.render('course-detail-pages/backend-domination.ejs', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    content,
                    status: "",
                    user: "",
                    request: null,
                    batch_status: batch_full
                });
            }
            if(course.course_id == 'mco-01'){
                return res.render('course-detail-pages/job-ready-live-batch.ejs', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    content,
                    status: "",
                    user: "",
                    request: null,
                    batch_status: batch_full
                });
            }
            if(course.course_id == 'rf01'){
                return res.render('course-detail-pages/frontend-domination.ejs', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    status: "",
                    user: "",
                    content,
                    request: null,
                    batch_status: batch_full
                });
            }
            if(course.course_id == 'ap01'){
                return res.render('course-detail-pages/aptitude-reasoning.ejs', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    status: "",
                    user: "",
                    content,
                    request: null,
                    batch_status: batch_full
                });
            }
            if(course.course_id == 'dsa01'){
                return res.render('course-detail-pages/java-dsa-domination.ejs', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    status: "",
                    user: "",
                    content,
                    request: null,
                    batch_status: batch_full
                });
            }
            
            if(course.course_id == 'combo1'){
                const coursesArray  = ["66bd174049ff6f892bacdd47","6686990a672568d40bfbac7b"]
                const comboCourses = await courses.find({ _id: { $in: coursesArray } }).select('+enrolled_students').exec();
                return res.render('course-detail-pages/combo1.ejs', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    status: "",
                    user: "",
                    content,
                    comboCourses,
                    request: null,
                    batch_status: batch_full
                });
            }
            if(course.course_id == 'tj01'){
                return res.render('course-detail-pages/threejs-domination.ejs', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    status: "",
                    user: "",
                    content,
                    request: null,
                    batch_status: batch_full
                });
            }
            if(course.course_id == 'ddc01'){
                return res.render('course-detail-pages/dsa-domination-cohort.ejs', {
                    title: course.course_name,
                    pageTitle: "Details",
                    course: course,
                    status: "",
                    user: "",
                    content,
                    request: null,
                    batch_status: batch_full
                });
            }
            return res.render('CoursesDetails', {
                title: course.course_name,
                request: null,
                pageTitle: "Details",
                course: course,
                status: "",
                user: "",
                content,
                check_paid: checkPaid,
                batch_status: batch_full
            });
        }
    });
};


module.exports.masterCourse = async (req, res, next) => {
    try {
        const course = await courses.findOne({ _id: req.params.id }).select('+enrolled_students')
        if (!course) {
            console.log(req.params.id, "Course not found")
            res.redirect('back')
            return
        }
        var request = null
        var currentUser = null
        if (req.user) {
            currentUser = await User.findOne({
                _id: req.user._id
            })
            if (!currentUser) {
                res.redirect('back')
                return
            }
            request = req
        }

        var status = 'not enrolled'
        if (course.enrolled_students.includes(currentUser._id)) {
            status = 'paid'
        }


        return res.render('masterCourseDetails', {
            title: course.course_name,
            pageTitle: "Master course",
            request: request,
            course: course,
            status,
            user: currentUser,
        });


    } catch (err) {
        console.log(err)
        res.redirect('back')
    }
}



