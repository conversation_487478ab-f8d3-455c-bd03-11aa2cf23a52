const Src = require("../Models/src");
const { sendConfirmationEmail } = require("../utils/nodemailer");

module.exports.registerSrc = async (req, res) => {
    try {
        const { fullname, phone, email, expertise, city, thoughts, applied_for_role, worklink } = req.body;
        console.log(req.body);
        // Check for unique email
        const existingEmail = await Src.findOne({ email });
        if (existingEmail) {
            return res.status(409).json({ success: false, message: "Email already registered." });
        }
        // Check for unique phone
        const existingPhone = await Src.findOne({ phone });
        if (existingPhone) {
            return res.status(409).json({ success: false, message: "Phone number already registered." });
        }

        const src = new Src({
            fullname,
            phone,
            email,
            expertise,
            city,
            thoughts,
            applied_for_role,
            worklink: worklink.split(",").map((link) => link.trim()), // Split worklink by comma and trim spaces
        });

        await src.save();

        // Send confirmation email
        try {
            await sendConfirmationEmail(src);
        } catch (emailError) {
            console.error("Error sending confirmation email:", emailError);
            // Continue with the response even if email fails
        }

        return res.status(201).json({ success: true, message: "Src registered successfully", src });
    } catch (err) {
        // Mongoose validation error
        if (err.name === "ValidationError") {
            const errors = {};
            for (let field in err.errors) {
                errors[field] = err.errors[field].message;
            }
            return res.status(422).json({ success: false, message: "Schema validation error", errors });
        }
        return res.status(400).json({ success: false, message: err.message });
    }
};

module.exports.getSubmissions = async (req, res) => {
    try {
        const { page = 1, limit = 20, fullname, email, expertise, status, city, phase1Status, phase2Status, applied_for_role, sort, sortDirection } = req.query;

        const query = {};

        if (fullname) {
            query.fullname = { $regex: fullname, $options: "i" }; // Case-insensitive search
        }

        if (email) {
            query.email = { $regex: email, $options: "i" }; // Case-insensitive search
        }

        if (expertise) {
            query.expertise = expertise; // Exact match since it's an enum
        }

        if (status) {
            query.status = status;
        }

        if (city) {
            query.city = { $regex: city, $options: "i" };
        }

        if (applied_for_role) {
            query.applied_for_role = applied_for_role; // Exact match since it's an enum
        }

        if (phase1Status) {
            query["phases.phase1.status"] = phase1Status;
        }

        if (phase2Status) {
            query["phases.phase2.status"] = phase2Status;
        }

        // Prepare sort options
        const sortOptions = {};
        if (sort) {
            // Handle special cases for array fields
            if (sort === "worklink") {
                // Sort by the first worklink in the array
                sortOptions["worklink.0"] = sortDirection === "desc" ? -1 : 1;
            } else if (sort === "expertise") {
                // Sort by the first expertise in the array
                sortOptions["expertise.0"] = sortDirection === "desc" ? -1 : 1;
            } else {
                sortOptions[sort] = sortDirection === "desc" ? -1 : 1;
            }
        } else {
            // Default sort by creation date, newest first
            sortOptions["_id"] = -1;
        }

        const submissions = await Src.find(query)
            .sort(sortOptions)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .exec();

        const count = await Src.countDocuments(query);

        return res.status(200).json({
            success: true,
            submissions,
            totalPages: Math.ceil(count / limit),
            currentPage: parseInt(page),
            totalSubmissions: count,
        });
    } catch (err) {
        console.error("Error fetching submissions:", err);
        return res.status(500).json({ success: false, message: "Error fetching submissions", error: err.message });
    }
};

module.exports.showStatus = async (req, res) => {};

/*
Sample Request Bodies for updateSrcStatusAndPhases (PATCH /src/:uniqueId/status-phases)
uniqueId is part of the URL path (e.g., /src/aB1cD2eF/status-phases)

1. Update only the main status:
{
    "status": "good fit"
}

2. Update only the status of a specific phase (e.g., phase1):
{
    "phases": {
        "phase1": {
            "status": "accepted"
        }
    }
}

3. Update the main status and multiple phase statuses:
{
    "status": "best fit",
    "phases": {
        "phase1": {
            "status": "accepted"
        },
        "phase2": {
            "status": "pending"
        }
    }
}

4. Update all phase statuses without changing the main status:
{
    "phases": {
        "phase1": {
            "status": "accepted"
        },
        "phase2": {
            "status": "accepted"
        }
    }
}

Important Notes:
- You only need to include the fields you want to change.
- Status values must match those defined in the Mongoose schema's enum.
- 400 error if the request body is empty or has no valid fields to update.
- 422 error if status values are invalid according to schema enums.
*/
module.exports.updateSrcStatusAndPhases = async (req, res) => {
    try {
        const { uniqueId } = req.params;
        const updateData = {};

        if (req.body.status) {
            updateData.status = req.body.status;
        }

        if (req.body.phases) {
            // Construct the update object for phases to ensure only valid fields are set
            updateData.phases = {};
            if (req.body.phases.phase1 && req.body.phases.phase1.status) {
                updateData.phases.phase1 = { status: req.body.phases.phase1.status };
            }
            if (req.body.phases.phase2 && req.body.phases.phase2.status) {
                updateData.phases.phase2 = { status: req.body.phases.phase2.status };
            }
        }

        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({ success: false, message: "No update data provided." });
        }

        const updatedSrc = await Src.findOneAndUpdate({ uniqueId }, { $set: updateData }, { new: true, runValidators: true });

        if (!updatedSrc) {
            return res.status(404).json({ success: false, message: "Src entry not found." });
        }

        return res.status(200).json({ success: true, message: "Src status and phases updated successfully.", src: updatedSrc });
    } catch (err) {
        if (err.name === "ValidationError") {
            const errors = {};
            for (let field in err.errors) {
                errors[field] = err.errors[field].message;
            }
            return res.status(422).json({ success: false, message: "Schema validation error", errors });
        }
        console.error("Error updating Src status and phases:", err);
        return res.status(500).json({ success: false, message: "Error updating Src status and phases", error: err.message });
    }
};

// Get unique fields and cities for filter dropdowns
module.exports.getFilterOptions = async (req, res) => {
    try {
        // Get unique expertise values
        const uniqueExpertise = await Src.distinct("expertise");

        // Get unique applied_for_role values
        const uniqueRoles = await Src.distinct("applied_for_role");

        // Get unique cities
        const uniqueCities = await Src.distinct("city");

        return res.status(200).json({
            success: true,
            expertise: uniqueExpertise.filter(Boolean).sort(), // Remove null/empty values and sort
            applied_for_role: uniqueRoles.filter(Boolean).sort(), // Remove null/empty values and sort
            cities: uniqueCities.filter(Boolean).sort(), // Remove null/empty values and sort
        });
    } catch (err) {
        console.error("Error fetching filter options:", err);
        return res.status(500).json({
            success: false,
            message: "Error fetching filter options",
            error: err.message,
        });
    }
};

// Check if email or phone exists
module.exports.checkIfExists = async (req, res) => {
    try {
        const { type, value } = req.body;

        if (!type || !value) {
            return res.status(400).json({
                success: false,
                message: "Both type and value are required fields",
            });
        }

        if (type !== "email" && type !== "phone") {
            return res.status(400).json({
                success: false,
                message: "Type must be either 'email' or 'phone'",
            });
        }

        let exists = false;

        if (type === "email") {
            // Basic email format validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid email format",
                });
            }

            const existingEmail = await Src.findOne({ email: value });
            exists = !!existingEmail;
        } else if (type === "phone") {
            // Basic phone format validation - allows various formats
            const phoneRegex = /^\+?[0-9\s\-()]{8,20}$/;
            if (!phoneRegex.test(value)) {
                return res.status(400).json({
                    success: false,
                    message: "Invalid phone format",
                });
            }

            const existingPhone = await Src.findOne({ phone: value });
            exists = !!existingPhone;
        }

        return res.status(200).json({
            success: true,
            exists: exists,
            message: exists ? `This ${type} is already registered.` : `This ${type} is available.`,
        });
    } catch (err) {
        console.error("Error checking if field exists:", err);
        return res.status(500).json({
            success: false,
            message: "Server error while checking field",
        });
    }
};
